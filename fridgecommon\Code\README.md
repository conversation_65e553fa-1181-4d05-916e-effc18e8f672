# Code目录说明

本目录包含fridgecommon项目的核心源代码实现。

## 目录结构

```text
Code/
└── StepperMotor/                # 步进电机驱动库
    ├── StepperMotorLib.c        # 步进电机库实现文件
    └── StepperMotorLib.h        # 步进电机库头文件
```

## 模块说明

### StepperMotor - 步进电机驱动库

步进电机驱动库是一个通用的步进电机控制模块，支持多种驱动芯片类型，适用于冰箱等家电产品中的精确位置控制。

#### 主要特性

- **多驱动芯片支持**: 支持LB1909M、LB1205M等多种驱动IC
- **多种驱动模式**: 支持4拍和8拍驱动模式
- **精确控制**: 提供步数、速度、方向的精确控制
- **状态管理**: 完整的运行状态监控和管理
- **中断驱动**: 支持ISR中断服务程序调用
- **参数可配置**: 关键参数可在运行时配置

#### 支持的驱动芯片

| 驱动芯片 | 驱动模式 | 常量定义 |
|---------|---------|----------|
| LB1909M | 4拍 | `U8_DRIVER_TYPES_LB1909M_4BEATS` |
| LB1205M | 4拍 | `U8_DRIVER_TYPES_LB1205M_4BEATS` |
| LB1205M | 8拍 | `U8_DRIVER_TYPES_LB1205M_8BEATS` |

#### 核心数据结构

##### StepperMotorConstPara_st - 配置参数结构
```c
typedef struct StepperMotorConstPara_st {
  uint16_t u16_StepperMotorStartDelayTimeMs;     // 两次动作间隔时间
  uint16_t u16_StepperMotorFirstLastPulseTimeMs; // 首末脉冲时间
  uint8_t u8_StepperMotorRunPPSTimeMs;           // 单步执行时间
  uint8_t u8_DriverTypes;                        // 驱动类型
  // ... 更多参数
} StepperMotorConstPara_st;
```

##### StepperMotorRunPara_st - 运行参数结构
包含当前位置、目标位置、运行状态等动态信息。

#### 主要API函数

##### 初始化和配置
- `InitStepperMotor()` - 初始化步进电机
- `SetStepperMotorConstPara()` - 设置配置参数

##### 运动控制
- `SetStepperMotorTarget()` - 设置目标位置
- `StepperMotorPause()` - 暂停运动
- `StepperMotorResume()` - 恢复运动
- `StepperMotorStop()` - 停止运动

##### 状态查询
- `GetStepperMotorRunningStatus()` - 获取运行状态
- `GetStepperMotorStepsInfo()` - 获取步数信息

##### 中断服务
- `DriveStepperMotorISR()` - 中断服务程序接口

#### IO控制接口

步进电机通过5个IO信号进行控制：

| 信号 | 位掩码 | 说明 |
|------|--------|------|
| IO_En | `U8_StepperMotorMask_IO_En` | 使能信号 |
| IO_InA | `U8_StepperMotorMask_IO_InA` | A相正向 |
| IO_InB | `U8_StepperMotorMask_IO_InB` | B相正向 |
| IO_InA_ | `U8_StepperMotorMask_IO_InA_` | A相反向 |
| IO_InB_ | `U8_StepperMotorMask_IO_InB_` | B相反向 |

#### 使用示例

```c
#include "StepperMotor/StepperMotorLib.h"

// 1. 初始化步进电机
StepperMotorConstPara_st config = {
    .u16_StepperMotorStartDelayTimeMs = 100,
    .u16_StepperMotorFirstLastPulseTimeMs = 50,
    .u8_StepperMotorRunPPSTimeMs = 10,
    .u8_DriverTypes = U8_DRIVER_TYPES_LB1205M_4BEATS
};

InitStepperMotor();
SetStepperMotorConstPara(&config);

// 2. 设置目标位置
SetStepperMotorTarget(1000, U8_StepperMotorFarAwayDirection);

// 3. 在定时器中断中调用驱动函数
void timer_isr(void) {
    DriveStepperMotorISR();
}

// 4. 查询运行状态
if (GetStepperMotorRunningStatus() == STEPPER_MOTOR_IDLE) {
    // 运动完成
}
```

## 编码规范

### 命名约定
- **常量**: 使用 `U8_`, `U16_`, `U32_` 前缀表示数据类型
- **结构体**: 使用 `_st` 后缀
- **函数**: 使用驼峰命名法，模块名作为前缀
- **变量**: 使用匈牙利命名法

### 文件组织
- **头文件**: 包含公共接口、常量定义、数据结构
- **源文件**: 包含具体实现，私有函数和变量

### 注释规范
- 使用Doxygen风格的注释
- 每个公共函数都有详细的参数和返回值说明
- 重要的算法和逻辑有充分的注释

## 依赖关系

### 标准库依赖
- `<stdbool.h>` - 布尔类型支持
- `<stdint.h>` - 标准整数类型
- `<stddef.h>` - 标准定义
- `<assert.h>` - 断言支持

### 外部依赖
- 需要实现IO控制回调函数
- 需要定时器中断支持

## 移植说明

要将此库移植到新的硬件平台：

1. **实现IO控制函数**: 根据硬件平台实现具体的GPIO控制
2. **配置定时器**: 设置合适的定时器中断频率
3. **调整参数**: 根据具体的步进电机和驱动芯片调整时序参数
4. **测试验证**: 使用单元测试验证功能正确性

## 版权信息

Copyright Xiaomi Corporation - Confidential - All rights reserved

本代码为小米公司机密代码，仅供内部使用。
