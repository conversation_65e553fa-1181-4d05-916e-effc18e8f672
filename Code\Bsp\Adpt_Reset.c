/*!
 * @file
 * @brief Clock adapter.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Adpt_Reset.h"
#include "reset.h"

static uint32_t u32_ResetFlag = 0;

/**
 * @brief 读取复位源（标识）
 * @retval None
 */
void Board_InitReset(void)
{
    u32_ResetFlag = M0P_RESET->RESET_FLAG;
    M0P_RESET->RESET_FLAG = 0;
    if(u32_ResetFlag & ResetFlagMskWdt)
    {
        //Set_HeartbeatState((uint8_t)eBlinking_ResetWdt);
    }
}

uint16_t Get_ResetFlag(void)
{
    return ((uint16_t)u32_ResetFlag);
}
