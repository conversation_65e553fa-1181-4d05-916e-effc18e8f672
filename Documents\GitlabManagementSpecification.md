# Gitlab软件项目管理规范

- 1.建立新工程：
  软件项目路径[Refrigerator_Software_Mainboard](https://git.n.xiaomi.com/refrigerator_software_mainboard)；<br />
  进入Refrigerator_Software_Mainboard，点击new project；<br />
  Project name填写对应型号工程；<br />
  Project description (optional)填写工程详细信息；<br />
  完成后点击create project；<br />
  进入Refrigerator_Software_Mainboard / bcd_508wgbi_mainboard，点击菜单栏Issues，点击Board，点击Add List，依次勾选Sprint,Doing,Bug,Review<br />
  点击OPEN右上角+，增加设计需求书<br />
  内Issues。<br />
  
- 2.克隆工程，准备工程资料：<br />
  克隆工程到本地<br />
	$ git clone https://git.n.xiaomi.com/refrigerator_software_mainboard/bcd_508wgbi_mainboard.git<br />
  按照[model](./model)内格式准备工程资料。<br />
  注意创建README.md，添加说明文档；修改.gitignore，添加需忽略的文件及路径。<br />
  
- 3.将工程资料传到main分支：<br />
	$ cd bcd_508wgbi_mainboard<br />
	$ git add .<br />
	$ git commit -m "上传工程资料"<br />
	$ git push -u origin main<br />
	git status查看是否上传成功<br />
	$ git status<br />
	On branch main<br />
	Your branch is up to date with 'origin/main'.<br />

	nothing to commit, working tree clean<br />
- 4.本地新建develop分支并上传到远程服务器(组内测试合格之前，所有更改分支合并到develop分支)：<br />
	$ git checkout -b develop<br />
	$ git push origin develop<br />
	$ git status<br />
	On branch develop<br />
	nothing to commit, working tree clean<br />
	进入Refrigerator_Software_Mainboard / bcd_508wgbi_mainboard，点击菜单栏Issues，点击Board，将所要完成的Issue拖动至Sprint。<br />
	
- 5.根据按照设计需求书功能类型新建分支(命名统一用B_开头)B_UpdateTurboCoolCtrl:<br />
	$ git checkout -b B_UpdateTurboCoolCtrl<br />
	Switched to a new branch 'B_UpdateTurboCoolCtrl'<br />
  在本地修改速冷优化相关代码；<br />
  进入Refrigerator_Software_Mainboard / bcd_508wgbi_mainboard，点击菜单栏Issues，点击Board，将所要完成的Issue拖动至Doing。<br />
  
- 6.修改完成后上传到远程服务器：<br />
	$ git add .<br />
	$ git commit -m "速冷优化，整机检测增加8bytes故障代码"<br />
	[B_UpdateTurboCoolCtrl c6dacce] 速冷优化，整机检测增加8bytes故障代码<br />
	$ git push origin B_UpdateTurboCoolCtrl<br />
	$ git status<br />
	On branch B_UpdateTurboCoolCtrl<br />
	nothing to commit, working tree clean<br />
	
- 7.进入Refrigerator_Software_Mainboard / bcd_508wgbi_mainboard，点击菜单栏Merge Request，点击Create Merge Request，<br />
  点击Change branches选择需要合并的分支和目标分支，点击Compare branches and continue，<br />
  Description中添加相关issue和task list：<br />
	Related #1 <br />
	* [x]  增加速冷优化<br />
	* [x]  工装测试<br />
  Assignee-对应负责人，点击Submit Merge Request，<br />
  点击菜单栏Issues，点击Board，将所要完成的Issue拖动至Review。<br />
  review完成后，将点击Board，将所要完成的Issue拖动至Closed。<br />
  
- 8.如有新的更改需求，重复5、6、7。<br />
  
- 9.测试完成后，更新资料，重复6、7，最后将develop合并至main，点击Board，将所要完成的Issue拖动至Closed。 <br />

- 10.每一次版本发布都需要点击Repository->tags->new tag   
    tag name为对应版本号，例如V0.001；  
    Create from选择对应分支，集成测试及之前选develop，正式下发选main；   
    Message添加相关信息；   
    Release notes 添加本次下发比较上一版本的更改内容。 

    
  
  
  
  
  
  
  
  
