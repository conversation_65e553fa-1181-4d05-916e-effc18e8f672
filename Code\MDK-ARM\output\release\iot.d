./output/release/iot.o: ..\Source\Iot\Iot.c ..\Source\Iot\Iot.h \
  ..\Source\Iot\miio\miio_api.h ..\Source\Iot\miio\miio_define.h \
  ..\Source\Iot\arch\arch_define.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdio.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdlib.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\string.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdarg.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stddef.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\ctype.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdint.h \
  ..\Source\Iot\user\user_config.h ..\Source\Iot\miio\uart\miio_uart.h \
  ..\Source\Iot\arch\arch_os.h ..\Source\Iot\arch\arch_uart.h \
  ..\Source\Iot\miio\util\Iotlist.h ..\Source\Iot\spec\Iot_SpecHandler.h \
  ..\Source\Iot\spec\Iot_Spec.h ..\Source\Iot\IotUsr.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdbool.h \
  ..\Device\base_types.h d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\assert.h \
  ..\Source\Module\Driver_AdSample.h \
  ..\Source\Module\Driver_AdTemperature.h \
  ..\Source\Application\Parameter_TemperatureZone.h \
  ..\Source\Application\DisplayInterface.h ..\Source\Core\SimpleFsm.h \
  ..\Source\Module\Driver_DoorSwitch.h \
  ..\Source\Application\LedController.h \
  ..\Source\Application\DisplayInterface.h \
  ..\Source\Application\Parameter_TemperatureZone.h \
  ..\Hardware\DisplayUsart.h ..\Source\Application\Defrosting.h \
  ..\Source\Module\Driver_DoubleDamper.h ..\Source\Module\Driver_Fan.h \
  ..\Source\Module\Driver_Flash.h \
  ..\Source\Module\Driver_CompFrequency.h ..\Source\Core\Core_Types.h \
  ..\Source\Application\FaultCode.h ..\Parameter\ParameterManager.h \
  ..\Source\Application\SystemManager.h
