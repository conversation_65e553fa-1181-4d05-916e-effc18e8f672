#include "test_steppermotor.h"

// 模拟IO控制函数
static uint8_t last_io_state = 0;
void mock_stepper_motor_io(const uint8_t io_state) { last_io_state = io_state; }

// 重置IO状态
void reset_io_state(void) { last_io_state = 0; }

// 测试初始化函数
void test_init_stepper_motor(void **state) {
  StepperMotorDriver_st motor_inst;
  StepperMotorConstPara_st const_para = {
      .u16_StepperMotorStartDelayTimeMs = 100,
      .u16_StepperMotorFirstLastPulseTimeMs = 50,
      .u8_StepperMotorRunPPSTimeMs = 10,
      .u8_DriverTypes = U8_DRIVER_TYPES_LB1909M_4BEATS};

  // 初始化步进电机
  Init_StepperMotor(&motor_inst, mock_stepper_motor_io, &const_para);

  // 验证初始化后的状态
  assert_false(motor_inst._private.b_IsRunning);
  assert_false(motor_inst._private.b_IsPausing);
  assert_int_equal(motor_inst._private.u16_RunSteps, 0);
  assert_int_equal(motor_inst._private.u16_TargetSteps, 0);
  assert_int_equal(motor_inst._private.u8_Direction,
                   U8_StepperMotorGoHomeDirection);
  assert_true(motor_inst._private.b_IsInitialized);
}

// 测试设置目标步数和方向
void test_set_target(void **state) {
  StepperMotorDriver_st motor_inst;
  StepperMotorConstPara_st const_para = {
      .u16_StepperMotorStartDelayTimeMs = 100,
      .u16_StepperMotorFirstLastPulseTimeMs = 50,
      .u8_StepperMotorRunPPSTimeMs = 10,
      .u8_DriverTypes = U8_DRIVER_TYPES_LB1909M_4BEATS};

  // 初始化步进电机
  Init_StepperMotor(&motor_inst, mock_stepper_motor_io, &const_para);

  // 设置目标步数和方向
  bool result = motor_inst.api->pSet_StepperMotorTartget(
      &motor_inst, 100, U8_StepperMotorFarAwayDirection);

  // 验证设置结果
  assert_true(result);
  assert_true(motor_inst._private.b_IsRunning);
  assert_int_equal(motor_inst._private.u16_TargetSteps, 100);
  assert_int_equal(motor_inst._private.u8_Direction,
                   U8_StepperMotorFarAwayDirection);
  assert_int_equal(motor_inst._private.u16_RunSteps, 0);
  assert_true(motor_inst._private.b_FirstPulse);
  assert_false(motor_inst._private.b_LastPulse);
}

// 测试暂停和恢复功能
void test_pause_resume(void **state) {
  StepperMotorDriver_st motor_inst;
  StepperMotorConstPara_st const_para = {
      .u16_StepperMotorStartDelayTimeMs = 100,
      .u16_StepperMotorFirstLastPulseTimeMs = 50,
      .u8_StepperMotorRunPPSTimeMs = 10,
      .u8_DriverTypes = U8_DRIVER_TYPES_LB1909M_4BEATS};

  // 初始化步进电机
  Init_StepperMotor(&motor_inst, mock_stepper_motor_io, &const_para);

  // 设置目标步数和方向
  motor_inst.api->pSet_StepperMotorTartget(&motor_inst, 100,
                                           U8_StepperMotorFarAwayDirection);

  // 暂停电机
  motor_inst.api->pPause_StepperMotorAction(&motor_inst, true);
  assert_true(motor_inst._private.b_IsPausing);
  assert_true(motor_inst._private.b_IsRunning);

  // 恢复电机
  motor_inst.api->pPause_StepperMotorAction(&motor_inst, false);
  assert_false(motor_inst._private.b_IsPausing);
  assert_true(motor_inst._private.b_IsRunning);
  assert_true(motor_inst._private.b_FirstPulse);
}

// 测试停止功能
void test_stop_action(void **state) {
  StepperMotorDriver_st motor_inst;
  StepperMotorConstPara_st const_para = {
      .u16_StepperMotorStartDelayTimeMs = 100,
      .u16_StepperMotorFirstLastPulseTimeMs = 50,
      .u8_StepperMotorRunPPSTimeMs = 10,
      .u8_DriverTypes = U8_DRIVER_TYPES_LB1909M_4BEATS};

  // 初始化步进电机
  Init_StepperMotor(&motor_inst, mock_stepper_motor_io, &const_para);

  // 设置目标步数和方向
  motor_inst.api->pSet_StepperMotorTartget(&motor_inst, 100,
                                           U8_StepperMotorFarAwayDirection);

  // 停止电机
  motor_inst.api->pStop_StepperMotorAction(&motor_inst);

  // 验证停止后的状态
  assert_false(motor_inst._private.b_IsRunning);
  assert_int_equal(motor_inst._private.u16_TargetSteps, 0);
  assert_int_equal(motor_inst._private.u16_RunSteps, 0);
  assert_false(motor_inst._private.b_FirstPulse);
  assert_false(motor_inst._private.b_LastPulse);
}

// 测试获取运行状态
void test_get_running_status(void **state) {
  StepperMotorDriver_st motor_inst;
  StepperMotorConstPara_st const_para = {
      .u16_StepperMotorStartDelayTimeMs = 100,
      .u16_StepperMotorFirstLastPulseTimeMs = 50,
      .u8_StepperMotorRunPPSTimeMs = 10,
      .u8_DriverTypes = U8_DRIVER_TYPES_LB1909M_4BEATS};

  // 初始化步进电机
  Init_StepperMotor(&motor_inst, mock_stepper_motor_io, &const_para);

  // 初始状态应该是未运行
  assert_false(motor_inst.api->pGet_StepperMotorIsRunning(&motor_inst));

  // 设置目标步数和方向后应该是运行状态
  motor_inst.api->pSet_StepperMotorTartget(&motor_inst, 100,
                                           U8_StepperMotorFarAwayDirection);
  assert_true(motor_inst.api->pGet_StepperMotorIsRunning(&motor_inst));

  // 停止后应该是未运行状态
  motor_inst.api->pStop_StepperMotorAction(&motor_inst);
  assert_false(motor_inst.api->pGet_StepperMotorIsRunning(&motor_inst));
}

// 测试获取步数信息
void test_get_steps_info(void **state) {
  StepperMotorDriver_st motor_inst;
  StepperMotorConstPara_st const_para = {
      .u16_StepperMotorStartDelayTimeMs = 100,
      .u16_StepperMotorFirstLastPulseTimeMs = 50,
      .u8_StepperMotorRunPPSTimeMs = 10,
      .u8_DriverTypes = U8_DRIVER_TYPES_LB1909M_4BEATS};

  // 初始化步进电机
  Init_StepperMotor(&motor_inst, mock_stepper_motor_io, &const_para);

  // 设置目标步数和方向
  motor_inst.api->pSet_StepperMotorTartget(&motor_inst, 100,
                                           U8_StepperMotorFarAwayDirection);

  // 初始状态下，已运行步数为0，剩余步数为100
  assert_int_equal(motor_inst.api->pGet_StepperMotorRunSteps(&motor_inst), 0);
  assert_int_equal(motor_inst.api->pGet_StepperMotorRemainingSteps(&motor_inst),
                   100);

  // 模拟运行10步
  motor_inst._private.u16_RunSteps = 10;

  // 验证步数信息
  assert_int_equal(motor_inst.api->pGet_StepperMotorRunSteps(&motor_inst), 10);
  assert_int_equal(motor_inst.api->pGet_StepperMotorRemainingSteps(&motor_inst),
                   90);
}

// 测试驱动函数
void test_drive_stepper_motor_isr(void **state) {
  StepperMotorDriver_st motor_inst;
  StepperMotorConstPara_st const_para = {
      .u16_StepperMotorStartDelayTimeMs = 100,
      .u16_StepperMotorFirstLastPulseTimeMs = 50,
      .u8_StepperMotorRunPPSTimeMs = 10,
      .u8_DriverTypes = U8_DRIVER_TYPES_LB1909M_4BEATS};

  // 初始化步进电机
  Init_StepperMotor(&motor_inst, mock_stepper_motor_io, &const_para);
  last_io_state = 0;

  // 设置目标步数和方向
  motor_inst.api->pSet_StepperMotorTartget(&motor_inst, 5,
                                           U8_StepperMotorFarAwayDirection);

  // 模拟中断调用多次，直到计时器到0
  for (int i = 0; i < 15; i++) {
    motor_inst.api->pDrive_StepperMotorISR(&motor_inst);
  }
  assert_true(last_io_state != 0); // IO状态应该被设置

  // 重置计时器以模拟时间流逝
  motor_inst._private.u16_PPSTimer = 0;

  // 再次调用中断函数，应该前进一步
  motor_inst.api->pDrive_StepperMotorISR(&motor_inst);
  assert_int_equal(motor_inst._private.u16_RunSteps, 1);

  // 继续模拟直到完成所有步骤
  for (int i = 0; i < 10; i++) {
    motor_inst._private.u16_PPSTimer = 0;
    motor_inst.api->pDrive_StepperMotorISR(&motor_inst);
  }

  // 验证电机最终停止
  assert_false(motor_inst._private.b_IsRunning);
}

// 测试不同驱动类型
void test_different_driver_types(void **state) {
  StepperMotorDriver_st motor_inst;
  StepperMotorConstPara_st const_para = {
      .u16_StepperMotorStartDelayTimeMs = 100,
      .u16_StepperMotorFirstLastPulseTimeMs = 50,
      .u8_StepperMotorRunPPSTimeMs = 10,
      .u8_DriverTypes = U8_DRIVER_TYPES_LB1205M_8BEATS};

  // 初始化步进电机，使用8拍驱动
  Init_StepperMotor(&motor_inst, mock_stepper_motor_io, &const_para);

  // 设置目标步数和方向
  motor_inst.api->pSet_StepperMotorTartget(&motor_inst, 10,
                                           U8_StepperMotorFarAwayDirection);

  // 模拟中断调用
  motor_inst._private.u16_PPSTimer = 0;
  motor_inst.api->pDrive_StepperMotorISR(&motor_inst);

  // 验证IO状态被设置
  assert_true(last_io_state != 0);
}

// 测试用例数组
static const struct CMUnitTest test_cases[] = {
    cmocka_unit_test(test_init_stepper_motor),
    cmocka_unit_test(test_set_target),
    cmocka_unit_test(test_pause_resume),
    cmocka_unit_test(test_stop_action),
    cmocka_unit_test(test_get_running_status),
    cmocka_unit_test(test_get_steps_info),
    cmocka_unit_test(test_drive_stepper_motor_isr),
    cmocka_unit_test(test_different_driver_types),
};

// 获取测试用例数组
const struct CMUnitTest *get_test_cases(void) { return test_cases; }

// 获取测试用例数量
int get_test_cases_count(void) {
  return sizeof(test_cases) / sizeof(test_cases[0]);
}
