./output/release/datamanager.o: ..\Source\Application\DataManager.c \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stddef.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\string.h ..\Bsp\Adpt_Reset.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdint.h \
  ..\Source\Application\DataManager.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdbool.h ..\Bsp\Adpt_Usart.h \
  ..\PeriphDrivers\lpuart.h ..\PeriphDrivers\ddl.h \
  ..\Device\base_types.h d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdio.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\assert.h \
  ..\Device\board_stkhc32l186.h ..\Device\hc32l186.h \
  d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h \
  d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h \
  d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h \
  d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h \
  ..\Device\system_hc32l186.h ..\PeriphDrivers\sysctrl.h \
  ..\Device\interrupts_hc32l186.h ..\PeriphDrivers\ddl.h \
  ..\PeriphDrivers\ddl_device.h ..\PeriphDrivers\uart.h \
  ..\Hardware\TestUsart.h ..\Source\Core\Core_Types.h \
  ..\Source\Application\FridgeRunner.h ..\Source\Core\SimpleFsm.h \
  ..\Source\Application\Defrosting.h ..\Source\Module\Driver_AdSample.h \
  ..\Source\Module\Driver_AdTemperature.h \
  ..\Source\Application\Parameter_TemperatureZone.h \
  ..\Source\Application\CoolingCycle.h \
  ..\Source\Application\Parameter_Device.h \
  ..\Source\Module\Driver_CompFrequency.h ..\Source\Module\Driver_Fan.h \
  ..\Source\Module\Driver_DoorSwitch.h \
  ..\Source\Module\Driver_DoubleDamper.h ..\Hardware\IO_Device.h \
  ..\Bsp\Adpt_GPIO.h ..\PeriphDrivers\gpio.h \
  ..\Source\Module\Driver_GradualLamp.h \
  ..\Source\Module\SystemTimerModule.h \
  ..\Source\Application\VerticalBeamHeater.h \
  ..\Source\Application\DisplayInterface.h ..\Hardware\InverterUsart.h \
  ..\Source\Application\ResolverDevice.h \
  ..\Source\Application\FaultCode.h \
  ..\Source\Application\SystemManager.h ..\Source\Module\Driver_Flash.h \
  ..\Source\Application\FunctionalCircuitTest.h \
  ..\Parameter\ParameterManager.h ..\Source\Application\FactoryMode.h
