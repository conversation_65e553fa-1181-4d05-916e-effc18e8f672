./output/release/iot_spechandler.o: ..\Source\Iot\spec\Iot_SpecHandler.c \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\string.h \
  ..\Source\Iot\spec\Iot_SpecHandler.h ..\Source\Iot\spec\Iot_Spec.h \
  ..\Source\Iot\IotUsr.h d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdint.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdbool.h \
  ..\Device\base_types.h d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdio.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stddef.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\assert.h \
  ..\Source\Iot\miio\device\codec\iot_operation_encoder.h \
  ..\Source\Iot\spec\Iot_Spec.h ..\Source\Iot\miio\miio_define.h \
  ..\Source\Iot\arch\arch_define.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdlib.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdarg.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\ctype.h \
  ..\Source\Iot\user\user_config.h ..\Source\Iot\miio\util\util.h \
  ..\Source\Iot\miio\device\codec\iot_operation_decoder.h \
  ..\Source\Iot\arch\arch_os.h ..\Source\Iot\miio\miio_api.h \
  ..\Source\Iot\miio\miio_define.h ..\Source\Iot\miio\uart\miio_uart.h \
  ..\Source\Iot\arch\arch_uart.h ..\Source\Iot\miio\util\Iotlist.h
