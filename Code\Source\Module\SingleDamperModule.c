/*!
 * @file
 * @brief This file defines functions for for the single damper module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

//#include "r_cg_macrodriver.h"
#include "SystemTimerModule.h"
#include "SingleDamperModule.h"
#include "StepperMotorLib.h"
#include "gpio.h"

#ifndef OFF
#define OFF     (0)   // Defines On
#endif

#ifndef ON
#define ON      (1)   // Defines On
#endif

/// Select Driver IC types Configuration here:
/// -------------------------------------------------------------------------------
#define U8_CONFIG_DRIVER                (U8_DRIVER_TYPES_LB1909M_4BEATS)
///#define U8_CONFIG_DRIVER                   (U8_DRIVER_TYPES_LB1205M_4BEATS)
///#define U8_CONFIG_DRIVER                   (U8_DRIVER_TYPES_LB1205M_8BEATS)
/// -------------------------------------------------------------------------------

/// 由风门两次动作间隔时间
#define U16_ACTION_INTERVAL_TIME_MSECS          ((uint16_t)2000)
/// 正常驱动时间
/// 首末脉冲时间
#define U16_DRIVER_FIRST_LAST_TIME_MSECS        ((uint16_t)4)
#define U8_DRIVER_PPS_TIME_MSECS                ((uint8_t)4)

#define U16_SINGLE_DAMPER_CHECK_PART_STEPS      ((uint16_t)65535)
#define U16_SINGLE_DAMPER_RESET_TIME_SECS       ((uint16_t)3600)
#define U16_SINGLE_DAMPER_ACTION_OVER_TIME_SECS ((uint16_t)120)     

// #define IO_SINGLE_DAMPER_ID0_En                   P15.5
// #define IO_SINGLE_DAMPER_ID0_InA                  P10.0
// #define IO_SINGLE_DAMPER_ID0_InB                  P15.6

// #define IO_SINGLE_DAMPER_ID1_En                   P0.4
// #define IO_SINGLE_DAMPER_ID1_InA                  P13.0
// #define IO_SINGLE_DAMPER_ID1_InB                  P10.2

#define Set_IO_SingleDamperID0_En()               Gpio_SetIO(GpioPortA, GpioPin8)//IO_SINGLE_DAMPER_ID0_En = ON   
#define Clear_IO_SingleDamperID0_En()             Gpio_ClrIO(GpioPortA, GpioPin8)//IO_SINGLE_DAMPER_ID0_En = OFF 
  
#define Set_IO_SingleDamperID0_InA()              Gpio_SetIO(GpioPortC, GpioPin8)//IO_SINGLE_DAMPER_ID0_InA = ON   
#define Clear_IO_SingleDamperID0_InA()            Gpio_ClrIO(GpioPortC, GpioPin8)//IO_SINGLE_DAMPER_ID0_InA = OFF
    
#define Set_IO_SingleDamperID0_InB()              Gpio_SetIO(GpioPortC, GpioPin9)//IO_SINGLE_DAMPER_ID0_InB = ON   
#define Clear_IO_SingleDamperID0_InB()            Gpio_ClrIO(GpioPortC, GpioPin9)//IO_SINGLE_DAMPER_ID0_InB = OFF  

#define Set_IO_SingleDamperID1_En()               IO_SINGLE_DAMPER_ID1_En = ON   
#define Clear_IO_SingleDamperID1_En()             IO_SINGLE_DAMPER_ID1_En = OFF 
  
#define Set_IO_SingleDamperID1_InA()              IO_SINGLE_DAMPER_ID1_InA = ON   
#define Clear_IO_SingleDamperID1_InA()            IO_SINGLE_DAMPER_ID1_InA = OFF
    
#define Set_IO_SingleDamperID1_InB()              IO_SINGLE_DAMPER_ID1_InB = ON   
#define Clear_IO_SingleDamperID1_InB()            IO_SINGLE_DAMPER_ID1_InB = OFF                    


/******************************************************************************
VARIABLES TYPE DEFINE 变量类型声明
******************************************************************************/

typedef struct SingleDamper_st
{
    StepperMotorDriver_st st_StepperMotorDriver;

    uint16_t u16_DamperSteps;
    uint16_t u16_DamperNoActionSecond;
    uint8_t u8_DamperNowState;
    uint8_t u8_DamperMidState;
    uint8_t u8_DamperNewState;
    uint8_t u8_DamperTargetState;
    uint8_t u8_DamperDirection;
    bool b_IsDamperInitialized;
    bool b_NeedToResetDamper;
    bool b_NeedToResetDamperBak;
    bool b_IsPausing;
    bool b_IsRunning;
    bool b_CheckPart;                   // 整机做电检测
    bool b_FunctionTest;                // 功能检测
} SingleDamper_st;

/******************************************************************************
LOCAL VARIABLES 静态变量定义
******************************************************************************/
static const uint16_t ary_SingleDamper_RunSteps[(uint8_t)SingleDamper_MaxState]\
    [(uint8_t)SingleDamper_MaxState] =
{
    {    0,  250,  550,  850, 1150, 1450, 1750 },
    {  350,    0,  250,  550,  850, 1150, 1450 },
    {  650,  350,    0,  250,  550,  850, 1150 },
    {  950,  650,  350,    0,  250,  550,  850 },
    { 1250,  950,  650,  350,    0,  250,  550 },
    { 1550, 1250,  950,  650,  350,    0,  250 },
    { 1850, 1550, 1250,  950,  650,  350,    0 }
};

///初始化单风门参数，顺序勿动，只改宏定义的值即可
static const StepperMotorConstPara_st st_SingleDamperConPara =
{
    U16_ACTION_INTERVAL_TIME_MSECS,     /// 两次动作间隔时间
    U16_DRIVER_FIRST_LAST_TIME_MSECS,   /// 首末脉冲时间
    U8_DRIVER_PPS_TIME_MSECS,           /// 中断PPS
    U8_CONFIG_DRIVER                    /// 驱动类型
};

static SingleDamper_st ary_SingleDamper[(uint8_t)SingleDamper_MaxNumber];

/******************************************************************************
GLOBAL VARIABLES 全局变量定义
******************************************************************************/


/******************************************************************************
LOCAL FUNCTIONS  静态函数声明
******************************************************************************/

static void Config_IO_SingleDamperID0(const uint8_t u8_stepperMotorIOState);

// static void Config_IO_SingleDamperID1(const uint8_t u8_stepperMotorIOState);

static const p_ImportStepperMotorIO ary_ImportStepperMotorIO[(uint8_t)SingleDamper_MaxNumber] =
{
    Config_IO_SingleDamperID0,
    // Config_IO_SingleDamperID1	
};

static void Init_SingleDamperParm(uint8_t u8_singleDamperID); 
static void Control_SingleDamper(SingleDamper_st *pst_damperInst);
static void Antifreeze_SingleDamper(SingleDamper_st *pst_damperInst);

/******************************************************************************
GLOBAL FUNCTIONS 全局函数声明
******************************************************************************/

/*!
 * @brief 单风门初始化，步进电机初始化
 * @param void
 */
void Init_SingleDamper(void);

/*!
 * @brief 执行步进电机驱动程序
 * @param void
 * @attention 放在1ms中断处理
 */
void Execute_SingleDamperDriver(void);

/*!
 * @brief 执行单风门控制程序
 * @param void
 * @attention 放在1s周期性任务内
 */
void Execute_SingleDamperControl(void);

/*!
 * @brief 单风门复位处理
 * @param u8_singleDamperSettingID - 风门ID
 */
void Reset_SingleDamper(uint8_t u8_singleDamperSettingID);

/*!
 * @brief 强制单风门动作至少30秒或立即停止
 * @param u8_singleDamperSettingID - 风门ID
 * @param b_forcedSingleDamperRunning - 1：转动，0：停止
 */
void Force_SingleDamperAction(uint8_t u8_singleDamperSettingID,
    bool b_forcedSingleDamperRunning);

/*!
 * @brief 强制单风门打开角度
 * @param u8_singleDamperSettingID - 风门ID
 * @param u8_singleDamperSettingState - 打开角度
 */
void Force_SingleDamperState(uint8_t u8_singleDamperSettingID,
    uint16_t u16_singleDamperSettingState);

/*!
 * @brief 设置单风门打开角度
 * @param u8_singleDamperSettingID - 风门ID
 * @param u8_singleDamperSettingState - 打开角度
 */
void Set_SingleDamperState(uint8_t u8_singleDamperSettingID,
    uint16_t u16_singleDamperSettingState);

/*!
 * @brief 暂停单风门动作
 * @param u8_singleDamperSettingID - 风门ID
 * @param b_pausedSingleDamperRunning - 1：转动，0：停止
 */
void Pause_SingleDamperAction(uint8_t u8_singleDamperSettingID,
    bool b_pausedSingleDamperRunning);

/*!
 * @brief 获取单风门当前复位状态
 * @param u8_singleDamperSettingID - 风门ID
 */
bool Get_SingleDamperResetState(uint8_t u8_singleDamperSettingID);

/*!
 * @brief 获取单风门当前暂停状态
 * @param u8_singleDamperSettingID - 风门ID
 */
bool Get_SingleDamperPauseState(uint8_t u8_singleDamperSettingID);

/*!
 * @brief 获取单风门当前运行状态
 * @param u8_singleDamperSettingID - 风门ID
 */
bool Get_SingleDamperRunningState(uint8_t u8_singleDamperSettingID);

/*!
 * @brief 获取单风门当前打开角度
 * @param u8_singleDamperSettingID - 风门ID
 */
uint16_t Get_SingleDamperState(uint8_t u8_singleDamperSettingID);

/*!
 * @brief 获取单风门当前状态保持时间
 * @param u8_singleDamperSettingID - 风门ID
 */
uint16_t Get_SingleDamperHoldTimer(uint8_t u8_singleDamperSettingID);

/******************************************************************************
* Function name  : Init_SingleDamper
* Description    : This function initializes variables and timers required
*                  by single damper.
* Parameters     : None
* Return         : None
* Notes          : None
******************************************************************************/

void Init_SingleDamper(void)
{
    uint8_t u8_index = 0;
    
    for (u8_index = 0; u8_index < (uint8_t)SingleDamper_MaxNumber; u8_index++)
    {
        Init_SingleDamperParm(u8_index);        
    }
}

/******************************************************************************
* Function name  : Execute_SingleDamperDriver
* Description    : This function executes single damper running.
* Parameters     : None
* Return         : None
* Notes          : Called in 1ms ISR.
******************************************************************************/

void Execute_SingleDamperDriver(void)
{
    uint8_t u8_index = 0;
    SingleDamper_st *pst_damperInst = (SingleDamper_st *)NULL;
    StepperMotorDriver_st *pst_stepperMotorInst = (StepperMotorDriver_st *)NULL;

    for (u8_index = 0; u8_index < (uint8_t)SingleDamper_MaxNumber; u8_index++)
    {
        pst_damperInst = &(ary_SingleDamper[u8_index]);
        if (true == pst_damperInst->b_IsDamperInitialized)
        {
            pst_stepperMotorInst = &(pst_damperInst->st_StepperMotorDriver);
    	    pst_stepperMotorInst->api->pDrive_StepperMotorISR(pst_stepperMotorInst);
        }
    }
}

/******************************************************************************
* Function name  : Execute_SingleDamperControl
* Description    : This function executes single damper control.
* Parameters     : None
* Return         : None
* Notes          : Called in 1s task.
******************************************************************************/

void Execute_SingleDamperControl(void)
{
    uint8_t u8_index = 0;
    SingleDamper_st *pst_damperInst = (SingleDamper_st *)NULL;

    for (u8_index = 0; u8_index < (uint8_t)SingleDamper_MaxNumber; u8_index++)
    {
        pst_damperInst = &(ary_SingleDamper[u8_index]);
        Control_SingleDamper(pst_damperInst);
        Antifreeze_SingleDamper(pst_damperInst);
    }
}

/******************************************************************************
* Function name  : Reset_SingleDamper
* Description    : This function resets the single damper.
* Parameters     : uint8_t u8_singleDamperSettingID
* Return         : None
* Notes          : None
******************************************************************************/

void Reset_SingleDamper(uint8_t u8_singleDamperSettingID)
{
    SingleDamper_st *pst_damperInst = (SingleDamper_st *)NULL;
	
    if (u8_singleDamperSettingID >= (uint8_t)SingleDamper_MaxNumber)
    {
    	u8_singleDamperSettingID = (uint8_t)SingleDamper_ID0;
    }
	
    pst_damperInst = &(ary_SingleDamper[u8_singleDamperSettingID]);

    if ((false == pst_damperInst->b_CheckPart) ||
        (true == pst_damperInst->b_FunctionTest))
    {
        pst_damperInst->b_NeedToResetDamper = true;
        pst_damperInst->b_NeedToResetDamperBak = false;
    }
}

/******************************************************************************
* Function name  : Force_SingleDamperAction
* Description    : This function forces the single damper to running or stopped.
* Parameters     : uint8_t u8_singleDamperSettingID
*                  bool b_forcedSingleDamperRunning
* Return         : None
* Notes          : Called in CheckPart
******************************************************************************/

void Force_SingleDamperAction(uint8_t u8_singleDamperSettingID,
	bool b_forcedSingleDamperRunning)
{
    SingleDamper_st *pst_damperInst = (SingleDamper_st *)NULL;
    StepperMotorDriver_st *pst_stepperMotorInst = (StepperMotorDriver_st *)NULL;

    if (u8_singleDamperSettingID >= (uint8_t)SingleDamper_MaxNumber)
    {
    	u8_singleDamperSettingID = (uint8_t)SingleDamper_ID0;
    }
    
    pst_damperInst = &(ary_SingleDamper[u8_singleDamperSettingID]);
    pst_stepperMotorInst = &(pst_damperInst->st_StepperMotorDriver);

    pst_damperInst->u8_DamperNowState = (uint8_t)SingleDamper_AllClose;
    pst_damperInst->u8_DamperMidState = (uint8_t)SingleDamper_AllClose;
    pst_damperInst->u8_DamperNewState = (uint8_t)SingleDamper_AllClose;
    pst_damperInst->u8_DamperTargetState = (uint8_t)SingleDamper_AllClose;
    pst_damperInst->b_NeedToResetDamper = false;
    pst_damperInst->b_NeedToResetDamperBak = false;
    pst_damperInst->b_CheckPart = true;
    pst_damperInst->b_FunctionTest = false;

    pst_stepperMotorInst->api->pStop_StepperMotorAction(pst_stepperMotorInst);

    if (false == b_forcedSingleDamperRunning)
    {        
        pst_damperInst->u16_DamperSteps = 0;
        pst_damperInst->b_IsRunning = false;
    }
    else
    {            
        pst_stepperMotorInst->api->pPause_StepperMotorAction(\
            pst_stepperMotorInst, false);
			
        pst_damperInst->u16_DamperSteps = U16_SINGLE_DAMPER_CHECK_PART_STEPS;
        pst_damperInst->u8_DamperDirection = U8_StepperMotorGoHomeDirection;
        pst_damperInst->b_IsRunning = true;
        pst_stepperMotorInst->api->pSet_StepperMotorTartget(\
            pst_stepperMotorInst, 
            U16_SINGLE_DAMPER_CHECK_PART_STEPS,
            U8_StepperMotorGoHomeDirection);
    }
}

/******************************************************************************
* Function name  : Force_SingleDamperState
* Description    : This function forces the single damper running to certain
*                  position.
* Parameters     : uint8_t u8_singleDamperSettingID, 
*                  uint16_t u16_singleDamperSettingState
* Return         : None
* Notes          : Called in CheckPart.
******************************************************************************/

void Force_SingleDamperState(uint8_t u8_singleDamperSettingID, 
    uint16_t u16_singleDamperSettingState)
{
    uint8_t u8_singleDamperSettingState = 0;
    SingleDamper_st *pst_damperInst = (SingleDamper_st *)NULL;
    StepperMotorDriver_st *pst_stepperMotorInst = (StepperMotorDriver_st *)NULL;

    u8_singleDamperSettingState = (uint8_t)u16_singleDamperSettingState;    

    if(u8_singleDamperSettingID >= (uint8_t)SingleDamper_MaxNumber)
    {
    	u8_singleDamperSettingID = (uint8_t)SingleDamper_ID0;
    }

    if(u8_singleDamperSettingState >= (uint8_t)SingleDamper_MaxState)
    {
        u8_singleDamperSettingState = (uint8_t)SingleDamper_AllClose;
    }
    
    pst_damperInst = &(ary_SingleDamper[u8_singleDamperSettingID]);
    pst_stepperMotorInst = &(pst_damperInst->st_StepperMotorDriver);

    if (U16_SINGLE_DAMPER_CHECK_PART_STEPS == pst_damperInst->u16_DamperSteps)
    {        
        pst_damperInst->u16_DamperSteps = 0;
        pst_damperInst->b_IsRunning = false;
        pst_stepperMotorInst->api->pStop_StepperMotorAction(pst_stepperMotorInst);
    }

    pst_damperInst->u8_DamperTargetState = u8_singleDamperSettingState;

    pst_damperInst->b_CheckPart = true;
    pst_damperInst->b_FunctionTest = true;
           
    pst_stepperMotorInst->api->pPause_StepperMotorAction(pst_stepperMotorInst,\
        false);    
}

/******************************************************************************
* Function name  : Set_SingleDamperState
* Description    : This function sets the single damper running to certain
*                  position.
* Parameters     : uint8_t u8_singleDamperSettingID, 
*                  uint16_t u16_singleDamperSettingState
* Return         : None
* Notes          : None
******************************************************************************/

void Set_SingleDamperState(uint8_t u8_singleDamperSettingID, 
    uint16_t u16_singleDamperSettingState)
{
    uint8_t u8_singleDamperSettingState = 0;
    SingleDamper_st *pst_damperInst = (SingleDamper_st *)NULL;

    u8_singleDamperSettingState = (uint8_t)u16_singleDamperSettingState;    

    if(u8_singleDamperSettingID >= (uint8_t)SingleDamper_MaxNumber)
    {
    	u8_singleDamperSettingID = (uint8_t)SingleDamper_ID0;
    }

    if(u8_singleDamperSettingState >= (uint8_t)SingleDamper_MaxState)
    {
        u8_singleDamperSettingState = (uint8_t)SingleDamper_AllClose;
    }
    
    pst_damperInst = &(ary_SingleDamper[u8_singleDamperSettingID]);

    if (false == pst_damperInst->b_CheckPart)
    {
        pst_damperInst->u8_DamperTargetState = u8_singleDamperSettingState;
    }
}

/******************************************************************************
* Function name  : Pause_SingleDamperAction
* Description    : This function pauses the single damper running.
* Parameters     : uint8_t u8_singleDamperSettingID, 
*                  bool b_pausedSingleDamperRunning
* Return         : None
* Notes          : None
******************************************************************************/

void Pause_SingleDamperAction(uint8_t u8_singleDamperSettingID,
    bool b_pausedSingleDamperRunning)
{
    SingleDamper_st *pst_damperInst = (SingleDamper_st *)NULL;
    StepperMotorDriver_st *pst_stepperMotorInst = (StepperMotorDriver_st *)NULL;

    if(u8_singleDamperSettingID >= (uint8_t)SingleDamper_MaxNumber)
    {
    	u8_singleDamperSettingID = (uint8_t)SingleDamper_ID0;
    }
    
    pst_damperInst = &(ary_SingleDamper[u8_singleDamperSettingID]);
    pst_stepperMotorInst = &(pst_damperInst->st_StepperMotorDriver);

    if ((false == b_pausedSingleDamperRunning) ||
        (false == pst_damperInst->b_CheckPart) ||
        (true == pst_damperInst->b_FunctionTest))
    {
        pst_damperInst->b_IsPausing = b_pausedSingleDamperRunning;
        pst_stepperMotorInst->api->pPause_StepperMotorAction(
            pst_stepperMotorInst, 
            (bool)b_pausedSingleDamperRunning);
    }

    if ((false == b_pausedSingleDamperRunning) &&
        (true == pst_damperInst->b_IsRunning))
    {
        pst_damperInst->u16_DamperNoActionSecond = Get_SecondCount();                		
    }
}

/******************************************************************************
* Function name  : Get_SingleDamperResetState
* Description    : This function returns the single damper reset state.
* Parameters     : uint8_t u8_singleDamperSettingID
* Return         : b_NeedToResetDamper
* Notes          : None
******************************************************************************/

bool Get_SingleDamperResetState(uint8_t u8_singleDamperSettingID)
{
    SingleDamper_st *pst_damperInst = (SingleDamper_st *)NULL;

    if(u8_singleDamperSettingID >= (uint8_t)SingleDamper_MaxNumber)
    {
    	u8_singleDamperSettingID = (uint8_t)SingleDamper_ID0;
    }
    
    pst_damperInst = &(ary_SingleDamper[u8_singleDamperSettingID]);

    return(pst_damperInst->b_NeedToResetDamper);
}

/******************************************************************************
* Function name  : Get_SingleDamperPauseState
* Description    : This function returns the single damper pause state.
* Parameters     : uint8_t u8_singleDamperSettingID
* Return         : b_IsPausing
* Notes          : None
******************************************************************************/

bool Get_SingleDamperPauseState(uint8_t u8_singleDamperSettingID)
{
    SingleDamper_st *pst_damperInst = (SingleDamper_st *)NULL;

    if(u8_singleDamperSettingID >= (uint8_t)SingleDamper_MaxNumber)
    {
    	u8_singleDamperSettingID = (uint8_t)SingleDamper_ID0;
    }
    
    pst_damperInst = &(ary_SingleDamper[u8_singleDamperSettingID]);

    return(pst_damperInst->b_IsPausing);
}

/******************************************************************************
* Function name  : Get_SingleDamperRunningState
* Description    : This function returns the single damper running state.
* Parameters     : uint8_t u8_singleDamperSettingID
* Return         : b_IsRunning
* Notes          : None
******************************************************************************/

bool Get_SingleDamperRunningState(uint8_t u8_singleDamperSettingID)
{
    SingleDamper_st *pst_damperInst = (SingleDamper_st *)NULL;

    if(u8_singleDamperSettingID >= (uint8_t)SingleDamper_MaxNumber)
    {
    	u8_singleDamperSettingID = (uint8_t)SingleDamper_ID0;
    }
    
    pst_damperInst = &(ary_SingleDamper[u8_singleDamperSettingID]);

    return(pst_damperInst->b_IsRunning);
}

/******************************************************************************
* Function name  : Get_SingleDamperState
* Description    : This function returns the single damper current position.
* Parameters     : uint8_t u8_singleDamperSettingID
* Return         : u8_DamperNowState
* Notes          : None
******************************************************************************/

uint16_t Get_SingleDamperState(uint8_t u8_singleDamperSettingID)
{
    SingleDamper_st *pst_damperInst = (SingleDamper_st *)NULL;

    if(u8_singleDamperSettingID >= (uint8_t)SingleDamper_MaxNumber)
    {
    	u8_singleDamperSettingID = (uint8_t)SingleDamper_ID0;
    }
    
    pst_damperInst = &(ary_SingleDamper[u8_singleDamperSettingID]);

    return((uint16_t)(pst_damperInst->u8_DamperNowState));
}

/******************************************************************************
* Function name  : Get_SingleDamperHoldTimer
* Description    : This function returns the holding time of the single damper.
* Parameters     : uint8_t u8_singleDamperSettingID
* Return         : u16_singleDamperHoldTimer
* Notes          : None
******************************************************************************/

uint16_t Get_SingleDamperHoldTimer(uint8_t u8_singleDamperSettingID)
{
    uint16_t u16_singleDamperHoldTimer = 0;
    SingleDamper_st *pst_damperInst = (SingleDamper_st *)NULL;

    if(u8_singleDamperSettingID >= (uint8_t)SingleDamper_MaxNumber)
    {
    	u8_singleDamperSettingID = (uint8_t)SingleDamper_ID0;
    }
    
    pst_damperInst = &(ary_SingleDamper[u8_singleDamperSettingID]);

    u16_singleDamperHoldTimer = 
        Get_SecondElapsedTime(pst_damperInst->u16_DamperNoActionSecond);   

    return(u16_singleDamperHoldTimer);
}

/******************************************************************************
* Function name  : Init_SingleDamperParm
* Description    : This function initializes variables and timers required
*                  by single damper.
* Parameters     : uint8_t u8_singleDamperID
* Return         : None
* Notes          : None
******************************************************************************/

static void Init_SingleDamperParm(uint8_t u8_singleDamperID)
{
    SingleDamper_st *pst_damperInst = (SingleDamper_st *)NULL;
    StepperMotorDriver_st *pst_stepperMotorInst = (StepperMotorDriver_st *)NULL;

    if(u8_singleDamperID >= (uint8_t)SingleDamper_MaxNumber)
    {
    	u8_singleDamperID = (uint8_t)SingleDamper_ID0;
    }
    
    pst_damperInst = &(ary_SingleDamper[u8_singleDamperID]);
    pst_stepperMotorInst = &(pst_damperInst->st_StepperMotorDriver);

    pst_damperInst->u8_DamperNowState = (uint8_t)SingleDamper_AllClose;
    pst_damperInst->u8_DamperMidState = (uint8_t)SingleDamper_AllClose;
    pst_damperInst->u8_DamperNewState = (uint8_t)SingleDamper_AllClose;
    pst_damperInst->u8_DamperTargetState = (uint8_t)SingleDamper_AllClose;
    pst_damperInst->u16_DamperSteps = 0;
    pst_damperInst->u16_DamperNoActionSecond = 0;
    pst_damperInst->b_IsRunning = false;
    pst_damperInst->b_IsPausing = false;
    pst_damperInst->b_NeedToResetDamper = false;
    pst_damperInst->b_NeedToResetDamperBak = false;
    pst_damperInst->b_CheckPart = false;
    pst_damperInst->b_FunctionTest = false;
    pst_damperInst->b_IsDamperInitialized  = true;

    Init_StepperMotor(
        pst_stepperMotorInst,
        ary_ImportStepperMotorIO[u8_singleDamperID],
        &st_SingleDamperConPara);
}

/******************************************************************************
* Function name  : Control_SingleDamper
* Description    : This function controls the single damper run to target state.
* Parameters     : SingleDamper_st *pst_damperInst
* Return         : None
* Notes          : None
******************************************************************************/

static void Control_SingleDamper(SingleDamper_st *pst_damperInst)
{
    uint16_t u16_damperActionSecond = 0;

    StepperMotorDriver_st *pst_stepperMotorInst = (StepperMotorDriver_st *)NULL;
	
    if (pst_damperInst != (SingleDamper_st *)NULL)
    {        
        if ((false == pst_damperInst->b_CheckPart) ||
            (true == pst_damperInst->b_FunctionTest))
        {
            pst_stepperMotorInst = &(pst_damperInst->st_StepperMotorDriver);
			
            if (true == pst_damperInst->b_IsRunning)
            {
                if (false == pst_stepperMotorInst->api->\
                    pGet_StepperMotorIsRunning(pst_stepperMotorInst))
                {
                    pst_damperInst->b_IsRunning = false;
                    if (pst_damperInst->u16_DamperSteps != pst_stepperMotorInst->api->\
                        pGet_StepperMotorRunSteps(pst_stepperMotorInst))
					{
                        pst_damperInst->b_NeedToResetDamper = true; 
                        pst_damperInst->b_NeedToResetDamperBak = false;   
                    }					
                }
                else
                {
                    u16_damperActionSecond = 
                        Get_SecondElapsedTime(pst_damperInst->u16_DamperNoActionSecond);

                    if (U16_SINGLE_DAMPER_ACTION_OVER_TIME_SECS <= u16_damperActionSecond)
                    {
                        if (false == pst_stepperMotorInst->api->\
                            pGet_StepperMotorIsPausing(pst_stepperMotorInst))
                        {
                            pst_stepperMotorInst->api->pStop_StepperMotorAction(\
                                pst_stepperMotorInst);							
                        }												
                    }					
                }
            }
            else
            {
                if (pst_damperInst->u8_DamperNewState == 
                    pst_damperInst->u8_DamperNowState)
                {

                    if (true == pst_damperInst->b_NeedToResetDamperBak)
                    {
                        pst_damperInst->b_NeedToResetDamper = false;
                        pst_damperInst->b_NeedToResetDamperBak = false;
                    }						
                    else if (true == pst_damperInst->b_NeedToResetDamper)
                    {
                        pst_damperInst->b_NeedToResetDamperBak = true;
                        if ((uint8_t)SingleDamper_AllClose == 
                            pst_damperInst->u8_DamperNowState)
                        {
                            pst_damperInst->u8_DamperMidState = 
                                (uint8_t)SingleDamper_AllOpen;
                        }
                        else
                        {
                            pst_damperInst->u8_DamperMidState =
                                (uint8_t)SingleDamper_AllClose;
                        }
                    }
                    else if (pst_damperInst->u8_DamperNewState != 
                        pst_damperInst->u8_DamperTargetState)
					{
                        pst_damperInst->u8_DamperNewState =
                            pst_damperInst->u8_DamperTargetState;
							
                        if ((uint8_t)SingleDamper_AllClose == 
                            pst_damperInst->u8_DamperNowState)
                        {
                            pst_damperInst->u8_DamperMidState = 
                                pst_damperInst->u8_DamperNewState;
                        }
                        else
                        {
                            pst_damperInst->u8_DamperMidState = 
                                (uint8_t)SingleDamper_AllClose;
                        }							        
                    }
                    else
					{
                        pst_damperInst->b_FunctionTest = false;							        
                    }						
                }						
                else 							
                {
                    if ((uint8_t)SingleDamper_AllClose == 
                        pst_damperInst->u8_DamperNowState)
                    {
                        pst_damperInst->u8_DamperMidState = 
                            pst_damperInst->u8_DamperNewState;
                    }
                    else
                    {
                        pst_damperInst->u8_DamperMidState = 
                            (uint8_t)SingleDamper_AllClose;
                    }
                }

                if (pst_damperInst->u8_DamperMidState != 
                    pst_damperInst->u8_DamperNowState)
                {
                    if ((uint8_t)SingleDamper_AllClose == 
                        pst_damperInst->u8_DamperMidState)
                    {
                        pst_damperInst->u8_DamperDirection = 
                            U8_StepperMotorGoHomeDirection;
                    }
                    else
                    {
                        pst_damperInst->u8_DamperDirection = 
                            U8_StepperMotorFarAwayDirection;
                    }

                    pst_damperInst->u16_DamperSteps = ary_SingleDamper_RunSteps\
                        [pst_damperInst->u8_DamperNowState]\
                        [pst_damperInst->u8_DamperMidState];
                    
                    if (pst_stepperMotorInst->api->pSet_StepperMotorTartget(\
                        pst_stepperMotorInst,\
                        pst_damperInst->u16_DamperSteps,\
                        pst_damperInst->u8_DamperDirection)
                        == true)
					{
                        pst_damperInst->b_IsRunning = true;
                        pst_damperInst->u8_DamperNowState = 
                            pst_damperInst->u8_DamperMidState;
                        pst_damperInst->u16_DamperNoActionSecond = 
                            Get_SecondCount();							
                    }
                    else
                    {
                        pst_stepperMotorInst->api->pStop_StepperMotorAction(\
                            pst_stepperMotorInst);							
                    }
                }
            }
        }
    }
}

/******************************************************************************
* Function name  : Antifreeze_SingleDamper
* Description    : This function deals with anti freezing of the single damper.
* Parameters     : SingleDamper_st *pst_damperInst
* Return         : None
* Notes          : None
******************************************************************************/

static void Antifreeze_SingleDamper(SingleDamper_st *pst_damperInst)
{
    uint16_t u16_damperNoActionSecond = 0;
	
    if (pst_damperInst != (SingleDamper_st *)NULL)
    {
        if (false == pst_damperInst->b_CheckPart)
        {
            u16_damperNoActionSecond = 
                Get_SecondElapsedTime(pst_damperInst->u16_DamperNoActionSecond);
            if (U16_SINGLE_DAMPER_RESET_TIME_SECS <= u16_damperNoActionSecond)
            {
                pst_damperInst->b_NeedToResetDamper = true;
                pst_damperInst->b_NeedToResetDamperBak = false;
                pst_damperInst->u16_DamperNoActionSecond = Get_SecondCount();							
            }
        }
    }
}

/******************************************************************************
* Function name  : Config_IO_SingleDamperID0
* Description    : This function drives the single damper ID0.
* Parameters     : const uint8_t u8_stepperMotorIOState
* Return         : None
* Notes          : None
******************************************************************************/

static void Config_IO_SingleDamperID0(const uint8_t u8_stepperMotorIOState)
{    
    if ((u8_stepperMotorIOState & U8_StepperMotorMask_IO_En) == 
        U8_StepperMotorMask_IO_En)
    {
        Set_IO_SingleDamperID0_En();
    }
    else
    {
        Clear_IO_SingleDamperID0_En();
    }
	
    if ((u8_stepperMotorIOState & U8_StepperMotorMask_IO_InA) == 
        U8_StepperMotorMask_IO_InA)
    {
        Set_IO_SingleDamperID0_InA();      
    }
    else
    {
        Clear_IO_SingleDamperID0_InA();
    }
	
    if ((u8_stepperMotorIOState & U8_StepperMotorMask_IO_InB) == 
        U8_StepperMotorMask_IO_InB)
    {
        Set_IO_SingleDamperID0_InB();      
    }
    else
    {
        Clear_IO_SingleDamperID0_InB();
    }	
}

/******************************************************************************
* Function name  : Config_IO_SingleDamperID1
* Description    : This function drives the single damper ID1.
* Parameters     : const uint8_t u8_stepperMotorIOState
* Return         : None
* Notes          : None
******************************************************************************/

/*
static void Config_IO_SingleDamperID1(const uint8_t u8_stepperMotorIOState)
{    
    if ((u8_stepperMotorIOState & U8_StepperMotorMask_IO_En) == 
        U8_StepperMotorMask_IO_En)
    {
        Set_IO_SingleDamperID1_En();
    }
    else
    {
        Clear_IO_SingleDamperID1_En();
    }
	
    if ((u8_stepperMotorIOState & U8_StepperMotorMask_IO_InA) == 
        U8_StepperMotorMask_IO_InA)
    {
        Set_IO_SingleDamperID1_InA();      
    }
    else
    {
        Clear_IO_SingleDamperID1_InA();
    }
	
    if ((u8_stepperMotorIOState & U8_StepperMotorMask_IO_InB) == 
        U8_StepperMotorMask_IO_InB)
    {
        Set_IO_SingleDamperID1_InB();      
    }
    else
    {
        Clear_IO_SingleDamperID1_InB();
    }	
}
*/

/******************************************************************************
******************************************************************************/
