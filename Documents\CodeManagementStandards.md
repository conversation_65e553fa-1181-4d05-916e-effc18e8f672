## 1 代码管理内容

1. Code
   1. 源代码
   2. 集成开发环境（IDE）工程
2. 开发文档
   1. 控制规格书
   2. 设计流程图
3. 测试文档
   1. MCU资源使用
   2. 任务执行时间
   3. 集成测试用例

## 2 gitignore

    为了方便review，保持工程的整洁，上传项目前需在根目录下添加.gitignore文件，忽略工程编译生成的中间文件及保存的本地信息，远程仓库的工程下载到本地后编译不影响。

* 规则 作用

  * /Debug 过滤整个文件夹
  * *.zip 过滤所有.zip文件
  * /Debug /do.c 过滤某个具体文件
  * !/Debug /one.txt 追踪（不过滤）某个具体文件

    注意：如果你创建.gitignore文件之前就push了某一文件，那么即使你在.gitignore文件中写入过滤该文件的规则，该规则也不会起作用，git仍然会对该文件进行版本管理。
* 配置语法

  * 以斜杠“/”开头表示目录；
  * 以星号“*”通配多个字符；
  * 以问号“?”通配单个字符；
  * 以方括号“[]”包含单个字符的匹配列表；
  * 以叹号“!”表示不忽略(跟踪)匹配到的文件或目录。

    注意： git 对于 .gitignore配置文件是按行从上到下进行规则匹配的,详细的忽略规则可以参考[官方文档]

**https://git-scm.com/docs/gitignore**

* 使用
  * 在仓库的根目录创建.gitignore文件
  * 提交之前，修改.gitignore文件，添加需要忽略的文件。然后再做add commit push 等
  * 在使用过程中，需要对.gitignore文件进行再次的修改,  这时我们需要清除一下缓存cache，才能使.gitignore 生效

```Bash
> git rm -r --cached .    
> git add .   
> git commit -m 'update .gitignore'   
```

## 3 代码管理流程

原则上对EVT，DVT，PVT，MP这四个阶段的代码修改量要逐步趋于收敛状态

* 建立新工程：

  * 软件项目路径[Refrigerator_Software_Mainboard]
    **https://git.n.xiaomi.com/refrigerator_software_mainboard**
  * 进入Refrigerator_Software_Mainboard，点击new project， Project name填写对应型号工程， Project description (optional)填写工程详细信息，完成后点击create project。
* 看板管理：

  * 进入Refrigerator_Software_Mainboard / bcd_508wgbi_mainboard，点击菜单栏Issues，点击Board，点击Add List，依次勾选Sprint,Doing,Bug,Review，  点击OPEN右上角+，增加设计需求书内Issues。
* 建立本地代码仓：

  * 克隆工程到本地
    $ git clone https://git.n.xiaomi.com/refrigerator_software_mainboard/bcd_508wgbi_mainboard.git
  * 按照规定格式准备工程资料。
    ![](https://xiaomi.f.mioffice.cn/space/api/box/stream/download/asynccode/?code=ODBmYWQ2ZWNhZmQ2OWU3YjZiOTllMzViZTcyMTNlNzFfTlhMRGZpb2tGUDdJYjB4NGlUb25CZVhXdGY5NnFNdXVfVG9rZW46Ym94azRRR05PVHhlREh3ekkzYWU3eXpsa3hlXzE3NTEyNjIwOTk6MTc1MTI2NTY5OV9WNA)

    注意创建README.md，添加说明文档；修改.gitignore，添加需忽略的文件及路径。
* 将本地代码管理内容上传到main分支：

```Bash
$ cd bcd_508wgbi_mainboard
$ git add .
$ git commit -m "上传工程资料"
$ git push -u origin main
git status查看是否上传成功
$ git status
On branch main
Your branch is up to date with 'origin/main'.
nothing to commit, working tree clean
```

* 本地新建develop分支并上传到远程服务器(版本发布之前，所有更改分支合并到develop分支)：

```Bash
$ git checkout -b develop
$ git push origin develop
$ git status
On branch develop
nothing to commit, working tree clean
```

* 更新看板：

  * 进入Refrigerator_Software_Mainboard / bcd_508wgbi_mainboard，点击菜单栏Issues，点击Board，将所要完成的Issue拖动至Sprint。
* 功能开发及上仓：

  * 根据开发计划按照对应Issue新建分支(命名统一用B_开头)B_UpdateTurboCoolCtrl:
    `$ git checkout -b B_UpdateTurboCoolCtrl`

    Switched to a new branch 'B_UpdateTurboCoolCtrl'

    在本地修改速冷优化相关代码；

    同时维护看板，进入Refrigerator_Software_Mainboard / bcd_508wgbi_mainboard，点击菜单栏Issues，

    点击Board，将所要完成的Issue拖动至Doing。
  * 本地代码仓完成设计需求后，上传到远程服务器：

  ```Bash
        $ git add .
        $ git commit -m "速冷优化"
        [B_UpdateTurboCoolCtrl c6dacce] 速冷优化
        $ git push origin B_UpdateTurboCoolCtrl
        $ git status
        On branch B_UpdateTurboCoolCtrl
        nothing to commit, working tree clean
  ```
* 提交合并申请：

  * 进入Refrigerator_Software_Mainboard / bcd_508wgbi_mainboard，点击菜单栏Merge Request，点击Create Merge Request，点击Change branches选择需要合并的分支和目标分支，点击Compare branches and continue，  Description中添加相关issue和task list：

  ```Bash
        Related #1（对应Issue编号） 
        * [x]  增加速冷优化
        * [x]  工装测试
  ```

  Assignee-对应负责人，点击Submit Merge Request，

  点击菜单栏Issues，点击Board，将所要完成的Issue拖动至Review。

  review完成后，将点击Board，将所要完成的Issue拖动至Closed。
* 持续集成：

  * 每次新功能开发，需拉取develop分支最新代码，然后新建分支进行开发
  * 代码review完成，进行合并出现冲突后，需手动解决冲突，重新提交。

## 4 代码发布

    每一次版本发布都需要点击Repository->tags->new tag

    tag name为对应版本号，例如V0.001；

    Create from选择对应分支，集成测试及之前选develop，正式下发选main；

    Message添加相关信息；

    Release notes 添加本次下发比较上一版本的更改内容。

## 5 格式化文件配置

.clang-format文件是一个 **代码格式化配置文件** ，用于定义代码风格规则，配合 [Clang-Format](https://clang.llvm.org/docs/ClangFormat.html) 工具（LLVM 项目的一部分）自动统一代码的缩进、换行、空格、对齐等格式。它广泛应用于 C/C++ 项目。

在该文件中，我们关注如下几个点（该文件已在Documents目录下提供）：

1. **基础设置**

`Language:        Cpp` 指定适用于 C++ 语言

`AccessModifierOffset: -3`  public/private 等访问修饰符的缩进偏移量（-3 表示向左缩进 3 空格）。

2. **对齐与换行**

`AlignAfterOpenBracket: DontAlign` 括号后的内容不强制对齐（如函数参数换行时不对齐）。

`AlignConsecutiveAssignments: false` 连续赋值的 = 不对齐。

`AlignEscapedNewlines: Right` 反斜杠 \（用于宏或字符串换行）在行尾右对齐。

`AlignOperands:   false` 多行表达式的操作数不对齐。

`ColumnLimit:     0` 每行长度无限制（若根据规范设为132，则超长自动换行）。

3. **大括号（BraceWrapping）**

`BreakBeforeBraces: Custom` 大括号换行规则自定义，以下子项生效：

`AfterCaseLabel:  true`，case标签后的大括号换行。

`AfterFunction:   true`，函数定义的大括号换行。

`AfterControlStatement: true`，if/for等控制语句的大括号换行。

4. **缩进与空格**

`IndentWidth:     4`，缩进宽度为 4 空格。

`UseTab:          Never`，禁止使用 Tab，只用空格。

`SpaceBeforeParens: Never`，函数名与括号之间不加空格（如 func() 而非 func ()）。

`SpacesInAngles:  false`，模板尖括号内不加空格（如 vector `<int>`而非 vector< int >）。

5. **函数与语句**

`AllowShortFunctionsOnASingleLine: None`, 禁止任何函数（包括空函数）写在一行。

`AllowShortIfStatementsOnASingleLine: false`, if语句必须换行（禁止 if(x) return; 单行形式）。

`AlwaysBreakBeforeMultilineStrings: true`，多行字符串（如长文本）前强制换行。

6. **指针与引用**

`PointerAlignment: Right`，指针符号 * 靠右（如 int* ptr 而非 int *ptr）。

7. **注释与预处理****指令**

`ReflowComments:  true`，自动重排注释文本（如统一每行长度）。

`IndentPPDirectives: None`，预处理指令（如#ifdef）不缩进。

8. **包含文件（Includes）**

`SortIncludes:    false` 不自动排序 #include 语句。

`IncludeCategories:`，定义 #include 的分组优先级（如系统头文件优先）。

9. **其他关键规则**

`BinPackParameters: false`，函数参数不压缩到一行（每个参数单独一行）。

`PenaltyBreakString: 1000`，字符串字面量换行的惩罚权重（值越大越避免换行）。

`Standard:        Cpp11`，代码遵循 C++11 标准。

### 5.1 vscode方法强制格式化设置

在拷贝.clang-format文件到工程根目录下之后，有三种方法完成文件格式化，择其一即可

1. 在文件中右键菜单选择"Format Document"

![](https://xiaomi.f.mioffice.cn/space/api/box/stream/download/asynccode/?code=MTY0Y2Y3NzZkOTY0ZmJkMThhZDQxNmNmOTI0MDYyZDJfaVhYUGRJSnFCekxJWUJhRndjV0l6Wk5Xc1FtSjIzek9fVG9rZW46Ym94azRoMDFYM3lZSVJqTE4ya2g5VWZWTlBkXzE3NTEyNjIxNjI6MTc1MTI2NTc2Ml9WNA)

2. 使用相应的键盘快捷键：
   1. Windows: `Ctrl+Shift+F`
   2. Linux: `Ctrl+Shift+I`
   3. macOS: `Shift+Option+F`
3. 保存时自动格式化

要启用保存时自动格式化功能，需在 `VSCode`的设置文件 `(settings.json)`中添加以下配置，这样每次保存文件时 `VSCode`都会自动应用当前文件类型的格式化规则，帮助您保持代码风格的一致性。

```JSON
{
    "editor.formatOnSave": true
}
```
