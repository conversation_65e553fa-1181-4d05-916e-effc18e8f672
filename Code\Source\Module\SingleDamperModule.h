/*!
 * @file
 * @brief This file defines public constants, types for the single damper
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef __SINGLE_DAMPER_MODULE_H__
#define __SINGLE_DAMPER_MODULE_H__

#include <stdbool.h>
#include <stdint.h>

typedef enum {
  SingleDamper_ID0 = 0,
  // SingleDamper_ID1,
  SingleDamper_MaxNumber,
  SingleDamper_LeftVar = SingleDamper_ID0,
//  SingleDamper_RightVar = SingleDamper_ID1
} SingleDamperID_em;

typedef enum {
  SingleDamper_AllClose = 0,
  SingleDamper_Angle15Open,
  SingleDamper_Angle30Open,
  SingleDamper_Angle45Open,
  SingleDamper_Angle60Open,
  SingleDamper_Angle75Open,
  SingleDamper_AllOpen,
  SingleDamper_MaxState
} SingleDamperState_em;

typedef enum {
  Damper_AllClose = 0,
  Damper_Angle15O<PERSON>,
  <PERSON><PERSON>_<PERSON><PERSON>O<PERSON>,
  <PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>_<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>_<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>_MaxState
} DamperState_em;

/******************************************************************************
GLOBAL VARIABLES 全局变量声明
******************************************************************************/

/******************************************************************************
GLOBAL FUNCTIONS  全局函数声明
******************************************************************************/

/*!
 * @brief 单风门初始化，步进电机初始化
 * @param void
 */
extern void Init_SingleDamper(void);

/*!
 * @brief 执行步进电机驱动程序
 * @param void
 * @attention 放在1ms中断处理
 */
extern void Execute_SingleDamperDriver(void);

/*!
 * @brief 执行单风门控制程序
 * @param void
 * @attention 放在1s周期性任务内
 */
extern void Execute_SingleDamperControl(void);

/*!
 * @brief 单风门复位处理
 * @param u8_singleDamperSettingID - 风门ID
 */
extern void Reset_SingleDamper(uint8_t u8_singleDamperSettingID);

/*!
 * @brief 强制单风门动作至少30秒或立即停止
 * @param u8_singleDamperSettingID - 风门ID
 * @param b_forcedSingleDamperRunning - 1：转动，0：停止
 */
extern void Force_SingleDamperAction(uint8_t u8_singleDamperSettingID,
                                     bool b_forcedSingleDamperRunning);

/*!
 * @brief 强制单风门打开角度
 * @param u8_singleDamperSettingID - 风门ID
 * @param u16_singleDamperSettingState - 打开角度
 */
extern void Force_SingleDamperState(uint8_t u8_singleDamperSettingID,
                                    uint16_t u16_singleDamperSettingState);

/*!
 * @brief 设置单风门打开角度
 * @param u8_singleDamperSettingID - 风门ID
 * @param u16_singleDamperSettingState - 打开角度
 */
extern void Set_SingleDamperState(uint8_t u8_singleDamperSettingID,
                                  uint16_t u16_singleDamperSettingState);

/*!
 * @brief 暂停单风门动作
 * @param u8_singleDamperSettingID - 风门ID
 * @param b_pausedSingleDamperRunning - 1：转动，0：停止
 */
extern void Pause_SingleDamperAction(uint8_t u8_singleDamperSettingID,
                                     bool b_pausedSingleDamperRunning);

/*!
 * @brief 获取单风门当前复位状态
 * @param u8_singleDamperSettingID - 风门ID
 */
extern bool Get_SingleDamperResetState(uint8_t u8_singleDamperSettingID);

/*!
 * @brief 获取单风门当前暂停状态
 * @param u8_singleDamperSettingID - 风门ID
 */
extern bool Get_SingleDamperPauseState(uint8_t u8_singleDamperSettingID);

/*!
 * @brief 获取单风门当前运行状态
 * @param u8_singleDamperSettingID - 风门ID
 */
extern bool Get_SingleDamperRunningState(uint8_t u8_singleDamperSettingID);

/*!
 * @brief 获取单风门当前打开角度
 * @param u8_singleDamperSettingID - 风门ID
 */
extern uint16_t Get_SingleDamperState(uint8_t u8_singleDamperSettingID);

/*!
 * @brief 获取单风门当前状态保持时间(单位为秒)
 * @param u8_singleDamperSettingID - 风门ID
 */
extern uint16_t Get_SingleDamperHoldTimer(uint8_t u8_singleDamperSettingID);

/******************************************************************************
******************************************************************************/

#endif /* __SINGLE_DAMPER_MODULE_H__ */
