var searchData=
[
  ['dac全局函数定义_0',['DAC全局函数定义',['../group___d_a_c___global___functions.html',1,'']]],
  ['dac全局类型定义_1',['DAC全局类型定义',['../group___d_a_c___global___types.html',1,'']]],
  ['dac模块驱动库_2',['DAC模块驱动库',['../group___d_d_l___d_a_c.html',1,'']]],
  ['ddl全局函数定义_3',['DDL全局函数定义',['../group___d_d_l___global___functions.html',1,'']]],
  ['ddl全局宏定义_4',['DDL全局宏定义',['../group___d_d_l___global___macros.html',1,'']]],
  ['ddl全局类型定义_5',['DDL全局类型定义',['../group___d_d_l___global___types.html',1,'']]],
  ['ddl模块驱动库_6',['DDL模块驱动库',['../group___d_d_l___d_d_l.html',1,'']]],
  ['debug全局函数定义_7',['DEBUG全局函数定义',['../group___d_e_b_u_g___global___functions.html',1,'']]],
  ['debug全局类型定义_8',['DEBUG全局类型定义',['../group___d_e_b_u_g___global___types.html',1,'']]],
  ['debug模块驱动库_9',['DEBUG模块驱动库',['../group___d_d_l___d_e_b_u_g.html',1,'']]],
  ['definition_20for_20dma_5fconf_20en_10',['Bits definition for DMA_CONF.EN',['../group___d_m_a___c_o_n_f___e_n.html',1,'']]],
  ['definition_20for_20dma_5fconf_20prio_11',['Bits definition for DMA_CONF.PRIO',['../group___d_m_a___c_o_n_f___p_r_i_o.html',1,'']]],
  ['definition_20for_20dma_5fconfax_20bc_20x_200_201_12',['Bits definition for DMA_CONFAx.BC(x=0~1)',['../group___d_m_a___c_o_n_f_a___b_c.html',1,'']]],
  ['definition_20for_20dma_5fconfax_20ens_20x_200_201_13',['Bits definition for DMA_CONFAx.ENS(x=0~1)',['../group___d_m_a___c_o_n_f_a___e_n_s.html',1,'']]],
  ['definition_20for_20dma_5fconfax_20pas_20x_200_201_14',['Bits definition for DMA_CONFAx.PAS(x=0~1)',['../group___d_m_a___c_o_n_f_a___p_a_s.html',1,'']]],
  ['definition_20for_20dma_5fconfax_20st_20x_200_201_15',['Bits definition for DMA_CONFAx.ST(x=0~1)',['../group___d_m_a___c_o_n_f_a___s_t.html',1,'']]],
  ['definition_20for_20dma_5fconfax_20tc_20x_200_201_16',['Bits definition for DMA_CONFAx.TC(x=0~1)',['../group___d_m_a___c_o_n_f_a___t_c.html',1,'']]],
  ['definition_20for_20dma_5fconfax_20trisel_20x_200_201_17',['Bits definition for DMA_CONFAx.TRISEL(x=0~1)',['../group___d_m_a___c_o_n_f_a___t_r_i_s_e_l.html',1,'']]],
  ['definition_20for_20dma_5fconfbx_20errie_20x_200_201_18',['Bits definition for DMA_CONFBx.ERRIE(x=0~1)',['../group___d_m_a___c_o_n_f_b___e_r_r_i_e.html',1,'']]],
  ['definition_20for_20dma_5fconfbx_20fisie_20x_200_201_19',['Bits definition for DMA_CONFBx.FISIE(x=0~1)',['../group___d_m_a___c_o_n_f_b___f_i_s_i_e.html',1,'']]],
  ['definition_20for_20dma_5fconfbx_20msk_20x_200_201_20',['Bits definition for DMA_CONFBx.MSK(x=0~1)',['../group___d_m_a___c_o_n_f_b___m_s_k.html',1,'']]],
  ['definition_20for_20dma_5fconfbx_20stat_20x_200_201_21',['Bits definition for DMA_CONFBx.STAT(x=0~1)',['../group___d_m_a___c_o_n_f_b___s_t_a_t.html',1,'']]],
  ['definition_20for_20dma_5fconfbx_20width_20x_200_201_22',['Bits definition for DMA_CONFBx.WIDTH(x=0~1)',['../group___d_m_a___c_o_n_f_b___w_i_d_t_h.html',1,'']]],
  ['dma局部宏定义_23',['DMA局部宏定义',['../group___d_m_a_c___local___macros.html',1,'']]],
  ['dma_5fconf_20en_24',['Bits definition for DMA_CONF.EN',['../group___d_m_a___c_o_n_f___e_n.html',1,'']]],
  ['dma_5fconf_20prio_25',['Bits definition for DMA_CONF.PRIO',['../group___d_m_a___c_o_n_f___p_r_i_o.html',1,'']]],
  ['dma_5fconfax_20bc_20x_200_201_26',['Bits definition for DMA_CONFAx.BC(x=0~1)',['../group___d_m_a___c_o_n_f_a___b_c.html',1,'']]],
  ['dma_5fconfax_20ens_20x_200_201_27',['Bits definition for DMA_CONFAx.ENS(x=0~1)',['../group___d_m_a___c_o_n_f_a___e_n_s.html',1,'']]],
  ['dma_5fconfax_20pas_20x_200_201_28',['Bits definition for DMA_CONFAx.PAS(x=0~1)',['../group___d_m_a___c_o_n_f_a___p_a_s.html',1,'']]],
  ['dma_5fconfax_20st_20x_200_201_29',['Bits definition for DMA_CONFAx.ST(x=0~1)',['../group___d_m_a___c_o_n_f_a___s_t.html',1,'']]],
  ['dma_5fconfax_20tc_20x_200_201_30',['Bits definition for DMA_CONFAx.TC(x=0~1)',['../group___d_m_a___c_o_n_f_a___t_c.html',1,'']]],
  ['dma_5fconfax_20trisel_20x_200_201_31',['Bits definition for DMA_CONFAx.TRISEL(x=0~1)',['../group___d_m_a___c_o_n_f_a___t_r_i_s_e_l.html',1,'']]],
  ['dma_5fconfbx_20errie_20x_200_201_32',['Bits definition for DMA_CONFBx.ERRIE(x=0~1)',['../group___d_m_a___c_o_n_f_b___e_r_r_i_e.html',1,'']]],
  ['dma_5fconfbx_20fisie_20x_200_201_33',['Bits definition for DMA_CONFBx.FISIE(x=0~1)',['../group___d_m_a___c_o_n_f_b___f_i_s_i_e.html',1,'']]],
  ['dma_5fconfbx_20msk_20x_200_201_34',['Bits definition for DMA_CONFBx.MSK(x=0~1)',['../group___d_m_a___c_o_n_f_b___m_s_k.html',1,'']]],
  ['dma_5fconfbx_20stat_20x_200_201_35',['Bits definition for DMA_CONFBx.STAT(x=0~1)',['../group___d_m_a___c_o_n_f_b___s_t_a_t.html',1,'']]],
  ['dma_5fconfbx_20width_20x_200_201_36',['Bits definition for DMA_CONFBx.WIDTH(x=0~1)',['../group___d_m_a___c_o_n_f_b___w_i_d_t_h.html',1,'']]],
  ['dmac全局函数定义_37',['DMAC全局函数定义',['../group___d_m_a_c___global___functions.html',1,'']]],
  ['dmac全局类型定义_38',['DMAC全局类型定义',['../group___d_m_a_c___global___types.html',1,'']]],
  ['dmac模块驱动库_39',['DMAC模块驱动库',['../group___d_d_l___d_m_a_c.html',1,'']]],
  ['dynamic_20memory_20allocation_40',['Dynamic Memory Allocation',['../group__cmocka__alloc.html',1,'']]]
];
