/*!
 * @file
 * @brief This file defines functions for the stepper motor library.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "StepperMotorLib.h"

#ifndef NUM_ELEMENTS
#define NUM_ELEMENTS(array) ((sizeof(array)) / (sizeof(array[0])))
#endif

typedef enum
{
    STEPPERMOTOR_STEP_STATE_0,
    STEPPERMOTOR_STEP_STATE_1,
    STEPPERMOTOR_STEP_STATE_2,
    STEPPERMOTOR_STEP_STATE_3,
    STEPPERMOTOR_STEP_STATE_4,
    STEPPERMOTOR_STEP_STATE_5,
    STEPPERMOTOR_STEP_STATE_6,
    STEPPERMOTOR_STEP_STATE_7,
    STEPPERMOTOR_STEP_STATE_MAX
} StepperMotorStepState_em;

typedef struct StepperMotorStepState_st
{
    const uint8_t *pary_IO_State_Beats;
    uint8_t u8_StepperMotorMaxStepState;
} StepperMotorStepState_st;

static const uint8_t ary_IO_State_Lb1909m4Beats[] = {
    // STEPPERMOTOR_STEP_STATE_0
    U8_StepperMotorMask_IO_En,

    // STEPPERMOTOR_STEP_STATE_1
    U8_StepperMotorMask_IO_En | U8_StepperMotorMask_IO_InA,

    // STEPPERMOTOR_STEP_STATE_2
    U8_StepperMotorMask_IO_En | U8_StepperMotorMask_IO_InA |
        U8_StepperMotorMask_IO_InB,

    // STEPPERMOTOR_STEP_STATE_3
    U8_StepperMotorMask_IO_En | U8_StepperMotorMask_IO_InB
};

static const uint8_t ary_IO_State_Lb1205m4Beats[] = {
    // STEPPERMOTOR_STEP_STATE_0
    U8_StepperMotorMask_IO_En | U8_StepperMotorMask_IO_InA_ |
        U8_StepperMotorMask_IO_InB_,

    // STEPPERMOTOR_STEP_STATE_1
    U8_StepperMotorMask_IO_En | U8_StepperMotorMask_IO_InA_ |
        U8_StepperMotorMask_IO_InB,

    // STEPPERMOTOR_STEP_STATE_2
    U8_StepperMotorMask_IO_En | U8_StepperMotorMask_IO_InA |
        U8_StepperMotorMask_IO_InB,

    // STEPPERMOTOR_STEP_STATE_3
    U8_StepperMotorMask_IO_En | U8_StepperMotorMask_IO_InA |
        U8_StepperMotorMask_IO_InB_
};

static const uint8_t ary_IO_State_Lb1205m8Beats[] = {
    // STEPPERMOTOR_STEP_STATE_0
    U8_StepperMotorMask_IO_En | U8_StepperMotorMask_IO_InA_ |
        U8_StepperMotorMask_IO_InB | U8_StepperMotorMask_IO_InB_,

    // STEPPERMOTOR_STEP_STATE_1
    U8_StepperMotorMask_IO_En | U8_StepperMotorMask_IO_InA_ |
        U8_StepperMotorMask_IO_InB_,

    // STEPPERMOTOR_STEP_STATE_2
    U8_StepperMotorMask_IO_En | U8_StepperMotorMask_IO_InA |
        U8_StepperMotorMask_IO_InA_ | U8_StepperMotorMask_IO_InB_,

    // STEPPERMOTOR_STEP_STATE_3
    U8_StepperMotorMask_IO_En | U8_StepperMotorMask_IO_InA |
        U8_StepperMotorMask_IO_InB_,

    // STEPPERMOTOR_STEP_STATE_4
    U8_StepperMotorMask_IO_En | U8_StepperMotorMask_IO_InA |
        U8_StepperMotorMask_IO_InB | U8_StepperMotorMask_IO_InB_,

    // STEPPERMOTOR_STEP_STATE_5
    U8_StepperMotorMask_IO_En | U8_StepperMotorMask_IO_InA |
        U8_StepperMotorMask_IO_InB,

    // STEPPERMOTOR_STEP_STATE_6
    U8_StepperMotorMask_IO_En | U8_StepperMotorMask_IO_InA |
        U8_StepperMotorMask_IO_InA_ | U8_StepperMotorMask_IO_InB,

    // STEPPERMOTOR_STEP_STATE_7
    U8_StepperMotorMask_IO_En | U8_StepperMotorMask_IO_InA_ |
        U8_StepperMotorMask_IO_InB
};

static const StepperMotorStepState_st ary_StepperMotorStepState[U8_DRIVER_TYPES_MAX] = {
    // U8_DRIVER_TYPES_LB1909M_4BEATS
    { ary_IO_State_Lb1909m4Beats, NUM_ELEMENTS(ary_IO_State_Lb1909m4Beats) },

    // U8_DRIVER_TYPES_LB1205M_4BEATS
    { ary_IO_State_Lb1205m4Beats, NUM_ELEMENTS(ary_IO_State_Lb1205m4Beats) },

    // U8_DRIVER_TYPES_LB1205M_8BEATS
    { ary_IO_State_Lb1205m8Beats, NUM_ELEMENTS(ary_IO_State_Lb1205m8Beats) }
};

static void Drive_StepperMotorISR(StepperMotorDriver_st *pst_stepperMotorInst);
static bool Set_StepperMotorTartget(StepperMotorDriver_st *pst_stepperMotorInst, uint16_t u16_tartgetSteps, uint8_t u8_tartgetDirection);
static void Stop_StepperMotorAction(StepperMotorDriver_st *pst_stepperMotorInst);
static void Pause_StepperMotorAction(StepperMotorDriver_st *pst_stepperMotorInst, bool b_pausingState);
static bool Get_StepperMotorIsRunning(StepperMotorDriver_st *pst_stepperMotorInst);
static bool Get_StepperMotorIsPausing(StepperMotorDriver_st *pst_stepperMotorInst);
static uint16_t Get_StepperMotorRunSteps(StepperMotorDriver_st *pst_stepperMotorInst);
static uint16_t Get_StepperMotorRemainingSteps(StepperMotorDriver_st *pst_stepperMotorInst);
static const StepperMotorDriver_API_st Api = {
    Drive_StepperMotorISR, Set_StepperMotorTartget, Stop_StepperMotorAction, Pause_StepperMotorAction, Get_StepperMotorIsRunning, Get_StepperMotorIsPausing, Get_StepperMotorRunSteps, Get_StepperMotorRemainingSteps
};

static void Drive_StepperMotorISR(StepperMotorDriver_st *pst_stepperMotorInst)
{
    uint8_t u8_stepperMotorIOState = U8_StepperMotorIdleState;
    uint8_t u8_stepperMotorMaxStepState = (uint8_t)STEPPERMOTOR_STEP_STATE_3;
    const uint8_t *pary_IO_State_Beats = (uint8_t *)NULL;

    if(pst_stepperMotorInst == (StepperMotorDriver_st *)NULL)
        return;

    const StepperMotorConstPara_st *pst_constPara = pst_stepperMotorInst->_private.pst_StepperMotorConstPara;

    if((pst_constPara->u8_DriverTypes < U8_DRIVER_TYPES_MAX) &&
        (true == pst_stepperMotorInst->_private.b_IsInitialized))
    {
        if(pst_stepperMotorInst->_private.u16_PPSTimer > 0)
        {
            pst_stepperMotorInst->_private.u16_PPSTimer--;
        }

        if(0 == pst_stepperMotorInst->_private.u16_PPSTimer)
        {
            if((false == pst_stepperMotorInst->_private.b_IsRunning) ||
                (true == pst_stepperMotorInst->_private.b_IsPausing))
            {
                u8_stepperMotorIOState = U8_StepperMotorIdleState;
                pst_stepperMotorInst->_private.u16_PPSTimer = (uint16_t)(pst_constPara->u8_StepperMotorRunPPSTimeMs);
            }
            else if(true == pst_stepperMotorInst->_private.b_LastPulse)
            {
                pst_stepperMotorInst->_private.b_LastPulse = false;
                pst_stepperMotorInst->_private.b_IsRunning = false;
                u8_stepperMotorIOState = U8_StepperMotorIdleState;
                pst_stepperMotorInst->_private.u16_PPSTimer = pst_constPara->u16_StepperMotorStartDelayTimeMs;
            }
            else
            {
                u8_stepperMotorMaxStepState = ary_StepperMotorStepState[pst_constPara->u8_DriverTypes].u8_StepperMotorMaxStepState;
                pary_IO_State_Beats = ary_StepperMotorStepState[pst_constPara->u8_DriverTypes].pary_IO_State_Beats;

                if(true == pst_stepperMotorInst->_private.b_FirstPulse)
                {
                    pst_stepperMotorInst->_private.b_FirstPulse = false;
                    pst_stepperMotorInst->_private.u16_PPSTimer = pst_constPara->u16_StepperMotorFirstLastPulseTimeMs;
                }
                else if(pst_stepperMotorInst->_private.u16_RunSteps >= pst_stepperMotorInst->_private.u16_TargetSteps)
                {
                    pst_stepperMotorInst->_private.b_LastPulse = true;
                    pst_stepperMotorInst->_private.u16_PPSTimer = pst_constPara->u16_StepperMotorFirstLastPulseTimeMs;
                }
                else
                {
                    pst_stepperMotorInst->_private.u16_PPSTimer = (uint16_t)(pst_constPara->u8_StepperMotorRunPPSTimeMs);

                    pst_stepperMotorInst->_private.u16_RunSteps++;

                    if(U8_StepperMotorGoHomeDirection == pst_stepperMotorInst->_private.u8_Direction)
                    {
                        pst_stepperMotorInst->_private.u8_StepState--;
                        if(pst_stepperMotorInst->_private.u8_StepState >= u8_stepperMotorMaxStepState)
                        {
                            pst_stepperMotorInst->_private.u8_StepState = u8_stepperMotorMaxStepState - 1;
                        }
                    }
                    else
                    {
                        pst_stepperMotorInst->_private.u8_StepState++;
                        if(pst_stepperMotorInst->_private.u8_StepState >= u8_stepperMotorMaxStepState)
                        {
                            pst_stepperMotorInst->_private.u8_StepState = (uint8_t)STEPPERMOTOR_STEP_STATE_0;
                        }
                    }
                }

                u8_stepperMotorIOState = pary_IO_State_Beats[pst_stepperMotorInst->_private.u8_StepState];
            }

            if(pst_stepperMotorInst->_private.ImportStepperMotorIO != (p_ImportStepperMotorIO)NULL)
            {
                pst_stepperMotorInst->_private.ImportStepperMotorIO(u8_stepperMotorIOState);
            }
        }
    }
}

static bool Set_StepperMotorTartget(StepperMotorDriver_st *pst_stepperMotorInst,
    uint16_t u16_tartgetSteps,
    uint8_t u8_tartgetDirection)
{
    bool b_returnState = false;

    if((pst_stepperMotorInst != (StepperMotorDriver_st *)NULL) &&
        (false == pst_stepperMotorInst->_private.b_IsRunning))
    {
        pst_stepperMotorInst->_private.u8_Direction = u8_tartgetDirection;
        pst_stepperMotorInst->_private.u16_TargetSteps = u16_tartgetSteps;
        pst_stepperMotorInst->_private.u16_RunSteps = 0;
        pst_stepperMotorInst->_private.b_LastPulse = false;
        pst_stepperMotorInst->_private.b_FirstPulse = true;
        pst_stepperMotorInst->_private.b_IsRunning = true;
        b_returnState = true;
    }

    return b_returnState;
}

static void Stop_StepperMotorAction(StepperMotorDriver_st *pst_stepperMotorInst)
{
    if(pst_stepperMotorInst != (StepperMotorDriver_st *)NULL)
    {
        pst_stepperMotorInst->_private.u16_TargetSteps = 0;
        pst_stepperMotorInst->_private.u16_RunSteps = 0;
        pst_stepperMotorInst->_private.b_LastPulse = false;
        pst_stepperMotorInst->_private.b_FirstPulse = false;
        pst_stepperMotorInst->_private.b_IsRunning = false;
    }
}

static void Pause_StepperMotorAction(StepperMotorDriver_st *pst_stepperMotorInst,
    bool b_pausingState)
{
    if(pst_stepperMotorInst != (StepperMotorDriver_st *)NULL)
    {
        if(true == pst_stepperMotorInst->_private.b_IsRunning)
        {
            if(false == b_pausingState)
            {
                pst_stepperMotorInst->_private.b_FirstPulse = true;
            }
        }
        pst_stepperMotorInst->_private.b_IsPausing = b_pausingState;
    }
}

static bool Get_StepperMotorIsRunning(StepperMotorDriver_st *pst_stepperMotorInst)
{
    bool b_returnState = false;

    if(pst_stepperMotorInst != (StepperMotorDriver_st *)NULL)
    {
        b_returnState = pst_stepperMotorInst->_private.b_IsRunning;
    }

    return b_returnState;
}

static bool Get_StepperMotorIsPausing(StepperMotorDriver_st *pst_stepperMotorInst)
{
    bool b_returnState = false;

    if(pst_stepperMotorInst != (StepperMotorDriver_st *)NULL)
    {
        b_returnState = pst_stepperMotorInst->_private.b_IsPausing;
    }

    return b_returnState;
}

static uint16_t Get_StepperMotorRunSteps(StepperMotorDriver_st *pst_stepperMotorInst)
{
    uint16_t u16_runSteps = 0;

    if(pst_stepperMotorInst != (StepperMotorDriver_st *)NULL)
    {
        u16_runSteps = pst_stepperMotorInst->_private.u16_RunSteps;
    }

    return u16_runSteps;
}

static uint16_t Get_StepperMotorRemainingSteps(StepperMotorDriver_st *pst_stepperMotorInst)
{
    uint16_t u16_remainingSteps = 0;

    if((pst_stepperMotorInst != (StepperMotorDriver_st *)NULL) &&
        (pst_stepperMotorInst->_private.u16_TargetSteps >=
            pst_stepperMotorInst->_private.u16_RunSteps))
    {
        u16_remainingSteps = pst_stepperMotorInst->_private.u16_TargetSteps -
            pst_stepperMotorInst->_private.u16_RunSteps;
    }

    return u16_remainingSteps;
}

#ifdef __ARMCC_VERSION
void __aeabi_assert(const char *expr, const char *file, int line)
{
    while(1)
        ;
}
#endif

void Init_StepperMotor(StepperMotorDriver_st *pst_stepperMotorInst,
    p_ImportStepperMotorIO importIO,
    const StepperMotorConstPara_st *pst_constPara)
{
    assert((pst_stepperMotorInst != (StepperMotorDriver_st *)NULL) &&
        (importIO != (p_ImportStepperMotorIO)NULL) &&
        (pst_constPara != (StepperMotorConstPara_st *)NULL) &&
        (pst_constPara->u16_StepperMotorStartDelayTimeMs > 0) &&
        (pst_constPara->u16_StepperMotorFirstLastPulseTimeMs > 0) &&
        (pst_constPara->u8_StepperMotorRunPPSTimeMs > 0) &&
        (pst_constPara->u8_DriverTypes < U8_DRIVER_TYPES_MAX));

    pst_stepperMotorInst->api = &Api;
    pst_stepperMotorInst->_private.ImportStepperMotorIO = importIO;
    pst_stepperMotorInst->_private.pst_StepperMotorConstPara = pst_constPara;
    pst_stepperMotorInst->_private.u16_TargetSteps = 0;
    pst_stepperMotorInst->_private.u16_RunSteps = 0;
    pst_stepperMotorInst->_private.u16_PPSTimer = (uint16_t)pst_constPara->u8_StepperMotorRunPPSTimeMs;
    pst_stepperMotorInst->_private.u8_StepState = (uint8_t)STEPPERMOTOR_STEP_STATE_0;
    pst_stepperMotorInst->_private.u8_Direction = U8_StepperMotorGoHomeDirection;
    pst_stepperMotorInst->_private.b_IsInitialized = true;
    pst_stepperMotorInst->_private.b_IsRunning = false;
    pst_stepperMotorInst->_private.b_IsPausing = false;
}
