./output/release/util.o: ..\Source\Iot\miio\util\util.c \
  ..\Source\Iot\miio\miio_define.h ..\Source\Iot\arch\arch_define.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdio.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdlib.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\string.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdarg.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stddef.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\ctype.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdint.h \
  ..\Source\Iot\user\user_config.h ..\Source\Iot\miio\util\util.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\String.h
