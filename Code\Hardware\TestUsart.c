/*!
 * @file
 * @brief Initialize MCU.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Adpt_Usart.h"
#include "TestUsart.h"
#include "Crc16_CCITT_FALSE.h"
#include "DataManager.h"

static UartTestParm_st st_UartTestParm;
static bool b_TestUartInitialized;
static void Start_UartTestRece(void);
static void Handle_UartTestSendData_Bus(void);
static void Handle_UartTestReceData_Bus(const uint8_t u8_rece_data);

void Init_UartTest(void)
{
    Board_InitTestUsart();

    st_UartTestParm.f_SendIE = true; // 发送允许
    st_UartTestParm.f_HaveFrameToSend = false; // 有报文要发送
    st_UartTestParm.f_HaveFrameToHandle = false; // 有报文要处理

    st_UartTestParm.u8_SendTimeOutCount = 0; // 发送超时计数
    st_UartTestParm.u8_ReceTimeOutCount = 0; // 接收超时计数

    Start_UartTestRece();
    b_TestUartInitialized = true;
}

void Handle_UartTestSendData(void)
{
    if(true == b_TestUartInitialized)
    {
        st_UartTestParm.u8_SendTimeOutCount = U8_MAX_DATA_OVERTIME_UART_TEST;

        if(true == st_UartTestParm.f_SendIE)
        {
            st_UartTestParm.u8_SendTimeOutCount = 0;
        }
        else
        {
            Handle_UartTestSendData_Bus();
        }
    }
}

void Handle_UartTestReceData(const uint8_t u8_rece_data)
{
    if(true == b_TestUartInitialized)
    {
        // 是否有需要处理的报文
        if(false == st_UartTestParm.f_HaveFrameToHandle)
        {
            // 没有需要处理的报文
            // 置位接收超时计时
            st_UartTestParm.u8_ReceTimeOutCount = U8_MAX_DATA_OVERTIME_UART_TEST;

            // 接收数据是否越界
            if(st_UartTestParm.u8_ReceCount < U8_MAX_RECE_DATA_LENGTH_UART_TEST)
            {
                Handle_UartTestReceData_Bus(u8_rece_data);
            }
        }
    }
}

void Handle_UartTestFrame(void)
{
    uint8_t u8_send_data_length = 0;
    uint16_t u16_send_crc_value = 0;

    if(true == b_TestUartInitialized)
    {
        if(true == st_UartTestParm.f_SendIE)
        {
            if(true == st_UartTestParm.f_HaveFrameToHandle)
            {
                Handle_MasterComputerFrame(&st_UartTestParm);

                // 启动接收
                Start_UartTestRece();
                st_UartTestParm.u8_ReceTimeOutCount = 0;
                st_UartTestParm.f_HaveFrameToHandle = false;
            }

            if(true == st_UartTestParm.f_HaveFrameToSend)
            {
                st_UartTestParm.f_HaveFrameToSend = false;

                u8_send_data_length = (st_UartTestParm.ary_SendBuff[6] + 10); // 总长度数据长度+10
                if(u8_send_data_length <= U8_MAX_SEND_DATA_LENGTH_UART_TEST)
                {
                    u16_send_crc_value = Cal_CRC_MultipleData(&st_UartTestParm.ary_SendBuff[0], (u8_send_data_length - 3));
                    st_UartTestParm.ary_SendBuff[u8_send_data_length - 3] = (uint8_t)((u16_send_crc_value >> 8) & 0x00ff); // 高八位
                    st_UartTestParm.ary_SendBuff[u8_send_data_length - 2] = (uint8_t)(u16_send_crc_value & 0x00ff); // 低八位
                    st_UartTestParm.ary_SendBuff[u8_send_data_length - 1] = U8_BUS_FRAME_END;
                    st_UartTestParm.f_SendIE = false;
                }

                if(false == st_UartTestParm.f_SendIE)
                {
                    // 启动发送
                    TestUart_SendOneData(st_UartTestParm.ary_SendBuff[0]);
                    st_UartTestParm.u8_SendCount = 1;
                    st_UartTestParm.u8_SendDataState = 1;
                    st_UartTestParm.u8_SendTimeOutCount = U8_MAX_DATA_OVERTIME_UART_TEST;
                    TestUart_EnalbeTxInterrupts();
                }
            }
        }
    }
}

void Handle_UartTestOverTime(void)
{
    if(true == b_TestUartInitialized)
    {
        if(st_UartTestParm.u8_SendTimeOutCount > 0)
        {
            st_UartTestParm.u8_SendTimeOutCount--;
            if(0 == st_UartTestParm.u8_SendTimeOutCount)
            {
                st_UartTestParm.f_SendIE = true;
                TestUart_DisalbeTxInterrupts();
            }
        }

        if(st_UartTestParm.u8_ReceTimeOutCount > 0)
        {
            st_UartTestParm.u8_ReceTimeOutCount--;
            if(0 == st_UartTestParm.u8_ReceTimeOutCount)
            {
                Start_UartTestRece();
                st_UartTestParm.f_HaveFrameToHandle = false;
            }
        }
    }
}

static void Start_UartTestRece(void)
{
    st_UartTestParm.u8_ReceDataState = 0;
    st_UartTestParm.u8_ReceCount = 0;
    st_UartTestParm.u8_ReceLength = 0;
}

static void Handle_UartTestSendData_Bus(void)
{
    uint8_t u8_send_data = 0;

    switch(st_UartTestParm.u8_SendDataState)
    {
        case(uint8_t)BUS_PROTOCOL_STATE_HEAD:
            TestUart_SendOneData(U8_BUS_FRAME_HEAD);
            st_UartTestParm.u8_SendCount = 1;
            st_UartTestParm.u8_SendDataState = (uint8_t)BUS_PROTOCOL_STATE_LOCAL_ADDR;
            break;
        case(uint8_t)BUS_PROTOCOL_STATE_LOCAL_ADDR: // 发源地址U8_BUS_LOCAL_ADDR
            TestUart_SendOneData(U8_BUS_LOCAL_ADDR);
            st_UartTestParm.u8_SendCount = 2;
            st_UartTestParm.u8_SendDataState = (uint8_t)BUS_PROTOCOL_STATE_DEST_ADDR;
            break;
        case(uint8_t)BUS_PROTOCOL_STATE_DEST_ADDR: // 发目标设备地址
            TestUart_SendOneData(st_UartTestParm.ary_SendBuff[2]); // 2是目标地址
            st_UartTestParm.u8_SendCount = 3;
            st_UartTestParm.u8_SendDataState = (uint8_t)BUS_PROTOCOL_STATE_SETUP_FUNCBYTE1;
            break; // 下一个发三个设备号
        case(uint8_t)BUS_PROTOCOL_STATE_SETUP_FUNCBYTE1: // 发功能1 0xCC
            TestUart_SendOneData(st_UartTestParm.ary_SendBuff[3]);
            st_UartTestParm.u8_SendCount = 4;
            st_UartTestParm.u8_SendDataState = (uint8_t)BUS_PROTOCOL_STATE_SETUP_FUNCBYTE2;
            break;
        case(uint8_t)BUS_PROTOCOL_STATE_SETUP_FUNCBYTE2: // 发功能2
            TestUart_SendOneData(st_UartTestParm.ary_SendBuff[4]);
            st_UartTestParm.u8_SendCount = 5;
            st_UartTestParm.u8_SendDataState = (uint8_t)BUS_PROTOCOL_STATE_SETUP_FUNCBYTE3;
            break;
        case(uint8_t)BUS_PROTOCOL_STATE_SETUP_FUNCBYTE3: // 发功能3
            TestUart_SendOneData(st_UartTestParm.ary_SendBuff[5]);
            st_UartTestParm.u8_SendCount = 6;
            st_UartTestParm.u8_SendDataState = (uint8_t)BUS_PROTOCOL_STATE_LENGTH;
            break;
        case(uint8_t)BUS_PROTOCOL_STATE_LENGTH:
            u8_send_data = st_UartTestParm.ary_SendBuff[6]; // 报文长度buff2-->6
            TestUart_SendOneData(u8_send_data);
            st_UartTestParm.u8_SendLength = (u8_send_data + 10);
            st_UartTestParm.u8_SendCount = 7;
            st_UartTestParm.u8_SendDataState = (uint8_t)BUS_PROTOCOL_STATE_DATA;
            break;

        case(uint8_t)BUS_PROTOCOL_STATE_DATA:
            u8_send_data = st_UartTestParm.ary_SendBuff[st_UartTestParm.u8_SendCount++];
            TestUart_SendOneData(u8_send_data);
            if(st_UartTestParm.u8_SendCount >= st_UartTestParm.u8_SendLength)
            {
                st_UartTestParm.u8_SendDataState = (uint8_t)BUS_PROTOCOL_STATE_OVER;
            }
            break;

        case(uint8_t)BUS_PROTOCOL_STATE_OVER:
        case(uint8_t)BUS_PROTOCOL_STATE_TAIL:
        default:
            st_UartTestParm.f_SendIE = true;
            st_UartTestParm.u8_SendTimeOutCount = 0;
            TestUart_DisalbeTxInterrupts();
            break;
    }
}

static void Handle_UartTestReceData_Bus(const uint8_t u8_rece_data)
{
    static uint8_t u8_CrcValueLow = 0;
    static uint8_t u8_CrcValueHigh = 0;
    switch(st_UartTestParm.u8_ReceDataState)
    {
        case(uint8_t)BUS_PROTOCOL_STATE_HEAD:
            // 报文头
            if(U8_BUS_FRAME_HEAD == u8_rece_data)
            {
                st_UartTestParm.ary_ReceBuff[0] = U8_BUS_FRAME_HEAD;
                st_UartTestParm.u8_ReceCount = 1;
                st_UartTestParm.u8_ReceDataState = (uint8_t)BUS_PROTOCOL_STATE_DEST_ADDR;
                st_UartTestParm.un_CheckWord.u16_ReceCRCValue =
                    Cal_CRC_SingleData(U16_CRC_INITIAL_VALUE, u8_rece_data); // CRC初始值0xffff
            }
            break;

        case(uint8_t)BUS_PROTOCOL_STATE_DEST_ADDR:
            // 目的地址
            st_UartTestParm.ary_ReceBuff[1] = u8_rece_data;
            st_UartTestParm.u8_ReceCount = 2;
            st_UartTestParm.u8_ReceDataState = (uint8_t)BUS_PROTOCOL_STATE_LOCAL_ADDR;
            st_UartTestParm.un_CheckWord.u16_ReceCRCValue =
                Cal_CRC_SingleData(st_UartTestParm.un_CheckWord.u16_ReceCRCValue, u8_rece_data);
            break;

        case(uint8_t)BUS_PROTOCOL_STATE_LOCAL_ADDR:
            // 本地地址
            st_UartTestParm.ary_ReceBuff[2] = u8_rece_data;
            st_UartTestParm.u8_ReceCount = 3;
            st_UartTestParm.u8_ReceDataState = (uint8_t)BUS_PROTOCOL_STATE_SETUP_FUNCBYTE1;
            st_UartTestParm.un_CheckWord.u16_ReceCRCValue =
                Cal_CRC_SingleData(st_UartTestParm.un_CheckWord.u16_ReceCRCValue, u8_rece_data);
            break;
        case(uint8_t)BUS_PROTOCOL_STATE_SETUP_FUNCBYTE1: // CC
            st_UartTestParm.ary_ReceBuff[3] = u8_rece_data;
            st_UartTestParm.u8_ReceCount = 4;
            st_UartTestParm.u8_ReceDataState = (uint8_t)BUS_PROTOCOL_STATE_SETUP_FUNCBYTE2;
            st_UartTestParm.un_CheckWord.u16_ReceCRCValue =
                Cal_CRC_SingleData(st_UartTestParm.un_CheckWord.u16_ReceCRCValue, u8_rece_data);
            break;
        case(uint8_t)BUS_PROTOCOL_STATE_SETUP_FUNCBYTE2: // 02
            st_UartTestParm.ary_ReceBuff[4] = u8_rece_data;
            st_UartTestParm.u8_ReceCount = 5;
            st_UartTestParm.u8_ReceDataState = (uint8_t)BUS_PROTOCOL_STATE_SETUP_FUNCBYTE3;
            st_UartTestParm.un_CheckWord.u16_ReceCRCValue =
                Cal_CRC_SingleData(st_UartTestParm.un_CheckWord.u16_ReceCRCValue, u8_rece_data);
            break;
        case(uint8_t)BUS_PROTOCOL_STATE_SETUP_FUNCBYTE3: // 01
            st_UartTestParm.ary_ReceBuff[5] = u8_rece_data;
            st_UartTestParm.u8_ReceCount = 6;
            st_UartTestParm.u8_ReceDataState = (uint8_t)BUS_PROTOCOL_STATE_LENGTH;
            st_UartTestParm.un_CheckWord.u16_ReceCRCValue =
                Cal_CRC_SingleData(st_UartTestParm.un_CheckWord.u16_ReceCRCValue, u8_rece_data);
            break;
        case(uint8_t)BUS_PROTOCOL_STATE_LENGTH: // 0A
            // 报文长度
            st_UartTestParm.ary_ReceBuff[6] = (u8_rece_data + 10);
            st_UartTestParm.u8_ReceCount = 7;
            st_UartTestParm.u8_ReceLength = ((u8_rece_data + 10) - 1);
            st_UartTestParm.u8_ReceDataState = (uint8_t)BUS_PROTOCOL_STATE_DATA;
            st_UartTestParm.un_CheckWord.u16_ReceCRCValue =
                Cal_CRC_SingleData(st_UartTestParm.un_CheckWord.u16_ReceCRCValue, u8_rece_data);
            break;
        case(uint8_t)BUS_PROTOCOL_STATE_DATA:
            // 正常报文数据
            if(st_UartTestParm.u8_ReceCount == (st_UartTestParm.u8_ReceLength - 2)) // 到crc h
            {
                st_UartTestParm.u8_ReceDataState = (uint8_t)BUS_PROTOCOL_STATE_CRC;
                st_UartTestParm.ary_ReceBuff[st_UartTestParm.u8_ReceCount++] = u8_rece_data;
                st_UartTestParm.u8_ReceCount++;
            }
            else
            {
                st_UartTestParm.ary_ReceBuff[st_UartTestParm.u8_ReceCount++] = u8_rece_data;
                st_UartTestParm.un_CheckWord.u16_ReceCRCValue =
                    Cal_CRC_SingleData(st_UartTestParm.un_CheckWord.u16_ReceCRCValue, u8_rece_data);
            }

            break;
        case(uint8_t)BUS_PROTOCOL_STATE_CRC: // 查CRCH
            u8_CrcValueHigh = (uint8_t)((st_UartTestParm.un_CheckWord.u16_ReceCRCValue >> 8) & 0x00ff);
            st_UartTestParm.ary_ReceBuff[st_UartTestParm.u8_ReceLength - 1] = u8_rece_data; // 低8位
            if(u8_CrcValueHigh == (st_UartTestParm.ary_ReceBuff[st_UartTestParm.u8_ReceLength - 2]))
            {
                st_UartTestParm.u8_ReceDataState = (uint8_t)BUS_PROTOCOL_STATE_TAIL;
            }
            st_UartTestParm.u8_ReceCount++;
            break;
        case(uint8_t)BUS_PROTOCOL_STATE_TAIL:
            // 报文尾+CRCL
            st_UartTestParm.ary_ReceBuff[st_UartTestParm.u8_ReceLength] = u8_rece_data; // 帧尾
            u8_CrcValueLow = (uint8_t)(st_UartTestParm.un_CheckWord.u16_ReceCRCValue & 0x00ff);
            if((U8_BUS_FRAME_END == u8_rece_data) && (u8_CrcValueLow == st_UartTestParm.ary_ReceBuff[st_UartTestParm.u8_ReceLength - 1])) // 5A
            {
                st_UartTestParm.f_HaveFrameToHandle = true;
                st_UartTestParm.u8_ReceTimeOutCount = 0;
            }
            else
            {
                // 启动接收
                Start_UartTestRece();
            }
            break;
        default:
            break;
    }
}
