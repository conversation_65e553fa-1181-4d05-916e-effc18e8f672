./output/release/arch_uart.o: ..\Source\Iot\arch\arch_uart.c \
  ..\Source\Iot\arch\arch_define.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdio.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdlib.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\string.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdarg.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stddef.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\ctype.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdint.h \
  ..\Source\Iot\user\user_config.h ..\Source\Iot\arch\arch_uart.h \
  ..\Bsp\Adpt_Usart.h ..\PeriphDrivers\lpuart.h ..\PeriphDrivers\ddl.h \
  ..\Device\base_types.h d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\assert.h \
  ..\Device\board_stkhc32l186.h ..\Device\hc32l186.h \
  d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h \
  d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h \
  d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h \
  d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h \
  ..\Device\system_hc32l186.h ..\PeriphDrivers\sysctrl.h \
  ..\Device\interrupts_hc32l186.h ..\PeriphDrivers\ddl.h \
  ..\PeriphDrivers\ddl_device.h ..\PeriphDrivers\uart.h \
  ..\Source\Iot\IotUsr.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdbool.h
