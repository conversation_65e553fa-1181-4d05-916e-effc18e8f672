/*!
 * @file
 * @brief This file defines public constants, types and functions for test usart.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef TEST_USART_
#define TEST_USART_

#include <stdint.h>
#include <stdbool.h>

#define U8_MAX_SEND_DATA_LENGTH_UART_TEST ((uint8_t)44)
#define U8_MAX_RECE_DATA_LENGTH_UART_TEST ((uint8_t)40)
#define U8_MAX_DATA_OVERTIME_UART_TEST ((uint8_t)5)
#define U8_OVER_TIME_UART_TEST ((uint8_t)0)
#define U8_BUS_FRAME_HEAD (uint8_t)0xA5 // 帧头
#define U8_BUS_FRAME_END (uint8_t)0x5A // 帧尾
#define U8_BUS_LOCAL_ADDR (uint8_t)0x01 // 本地地址
#define U16_CRC_INITIAL_VALUE (uint16_t)0xFFFF // CRC校验初始值

// 帧格式
typedef enum
{
    BUS_PROTOCOL_STATE_HEAD = 0, // 报文头
    BUS_PROTOCOL_STATE_LOCAL_ADDR, // 源地址 //添加源地址
    BUS_PROTOCOL_STATE_DEST_ADDR, // 目的地址
    BUS_PROTOCOL_STATE_SETUP_FUNCBYTE1, // 设定功能1 0xCC
    BUS_PROTOCOL_STATE_SETUP_FUNCBYTE2, // 设定功能2
    BUS_PROTOCOL_STATE_SETUP_FUNCBYTE3, // 设定功能3
    BUS_PROTOCOL_STATE_LENGTH, // 报文长度
    BUS_PROTOCOL_STATE_DATA, // 正常报文数据
    BUS_PROTOCOL_STATE_CRC, // CRC 数据
    BUS_PROTOCOL_STATE_TAIL, // 报文尾
    BUS_PROTOCOL_STATE_OVER, // 完成
    BUS_PROTOCOL_STATE_MAX, // 转义字符
} BusProtocolState_em;

typedef enum
{
    BUS_FRAME_TYPE_CTRL = 0, // 控制帧
    BUS_FRAME_TYPE_INVALID, // 无效帧
    BUS_FRAME_TYPE_INQUIRE_PARM, // 参数查询
    BUS_FRAME_TYPE_INQUIRE_INFO, // 信息查询
    BUS_FRAME_TYPE_PROTOCOL_SWITCH, // 协议转换
    BUS_FRAME_TYPE_MAX
} BusFrameType_em;

typedef union
{
    uint16_t u16_ReceCRCValue;
    struct
    {
        uint8_t u8_ReceSum;
        uint8_t u8_SendSum;
    } ByteHL;
} CheckWord_un;

typedef struct
{
    CheckWord_un un_CheckWord; // 校验
    uint8_t ary_SendBuff[U8_MAX_SEND_DATA_LENGTH_UART_TEST]; // 发送缓冲区
    uint8_t ary_ReceBuff[U8_MAX_RECE_DATA_LENGTH_UART_TEST]; // 接收缓冲区

    uint8_t u8_SendDataState; // 发送数据状态
    uint8_t u8_ReceDataState; // 接收数据状态
    uint8_t u8_SendLength; // 发送长度
    uint8_t u8_SendCount; // 发送计数
    uint8_t u8_ReceLength; // 接收长度
    uint8_t u8_ReceCount; // 接收计数
    uint8_t u8_SendTimeOutCount; // 发送超时计数
    uint8_t u8_ReceTimeOutCount; // 接收超时计数
    bool f_SendIE; // 发送允许
    bool f_HaveFrameToSend; // 有报文要发送
    bool f_HaveFrameToHandle; // 有报文要处理
} UartTestParm_st;

void Init_UartTest(void);
void Handle_UartTestSendData(void);
void Handle_UartTestReceData(const uint8_t u8_recedata);
void Handle_UartTestFrame(void);
void Handle_UartTestOverTime(void);

#endif
