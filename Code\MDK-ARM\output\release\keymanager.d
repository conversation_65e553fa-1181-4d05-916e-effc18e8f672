./output/release/keymanager.o: ..\Source\Application\KeyManager.c \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stddef.h \
  ..\Source\Application\KeyManager.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdint.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdbool.h \
  ..\Source\Module\SystemTimerModule.h \
  ..\Source\Application\DisplayInterface.h ..\Source\Core\SimpleFsm.h \
  ..\Source\Application\Parameter_TemperatureZone.h \
  ..\Source\Module\Driver_AdSample.h \
  ..\Source\Module\Driver_AdTemperature.h ..\Hardware\DisplayUsart.h \
  ..\Source\Application\LedController.h \
  ..\Source\Application\SpecialKeyManager.h \
  ..\Source\Application\SystemManager.h \
  ..\Source\Application\VerticalBeamHeater.h \
  ..\Source\Iot\miio\miio_api.h ..\Source\Iot\miio\miio_define.h \
  ..\Source\Iot\arch\arch_define.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdio.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdlib.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\string.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdarg.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\ctype.h \
  ..\Source\Iot\user\user_config.h ..\Source\Iot\miio\uart\miio_uart.h \
  ..\Source\Iot\arch\arch_os.h ..\Source\Iot\arch\arch_uart.h \
  ..\Source\Iot\miio\util\Iotlist.h ..\Source\Module\Driver_Flash.h \
  ..\Source\Module\Driver_DoorSwitch.h
