<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\output\release\HC32L18x.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\output\release\HC32L18x.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6160001: Last Updated: Tue Jul  1 17:02:56 2025
<BR><P>
<H3>Maximum Stack Usage =        264 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
WDT_IRQHandler &rArr; Wdt_IRQHandler &rArr; HardFault_Handler &rArr; cm_backtrace_fault &rArr; print_call_stack &rArr; cm_backtrace_call_stack
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[3]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">SVC_Handler</a><BR>
 <LI><a href="#[4]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">PendSV_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1e]">ADC_DAC_IRQHandler</a> from interrupts_hc32l186.o(.text.ADC_DAC_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[25]">CLKTRIM_CTRIM_IRQHandler</a> from interrupts_hc32l186.o(.text.CLKTRIM_CTRIM_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[29]">Config_IO_SingleDamperID0</a> from singledampermodule.o(.text.Config_IO_SingleDamperID0) referenced from singledampermodule.o(.text.Init_SingleDamperParm)
 <LI><a href="#[a]">DMAC_IRQHandler</a> from interrupts_hc32l186.o(.text.DMAC_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[2c]">Drive_StepperMotorISR</a> from steppermotorlib.o(.text.Drive_StepperMotorISR) referenced from steppermotorlib.o(.rodata.Api)
 <LI><a href="#[24]">FLASH_RAM_IRQHandler</a> from interrupts_hc32l186.o(.text.FLASH_RAM_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[31]">Get_StepperMotorIsPausing</a> from steppermotorlib.o(.text.Get_StepperMotorIsPausing) referenced from steppermotorlib.o(.rodata.Api)
 <LI><a href="#[30]">Get_StepperMotorIsRunning</a> from steppermotorlib.o(.text.Get_StepperMotorIsRunning) referenced from steppermotorlib.o(.rodata.Api)
 <LI><a href="#[33]">Get_StepperMotorRemainingSteps</a> from steppermotorlib.o(.text.Get_StepperMotorRemainingSteps) referenced from steppermotorlib.o(.rodata.Api)
 <LI><a href="#[32]">Get_StepperMotorRunSteps</a> from steppermotorlib.o(.text.Get_StepperMotorRunSteps) referenced from steppermotorlib.o(.rodata.Api)
 <LI><a href="#[2]">HardFault_Handler</a> from cmb_fault.o(.text) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[12]">I2C0_IRQHandler</a> from interrupts_hc32l186.o(.text.I2C0_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[13]">I2C1_IRQHandler</a> from interrupts_hc32l186.o(.text.I2C1_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[23]">LCD_IRQHandler</a> from interrupts_hc32l186.o(.text.LCD_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[17]">LPTIM0_1_IRQHandler</a> from interrupts_hc32l186.o(.text.LPTIM0_1_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[e]">LPUART0_IRQHandler</a> from interrupts_hc32l186.o(.text.LPUART0_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[f]">LPUART1_IRQHandler</a> from interrupts_hc32l186.o(.text.LPUART1_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[22]">LVD_IRQHandler</a> from interrupts_hc32l186.o(.text.LVD_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from startup_hc32l186.o(.text) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[1b]">PCA_WWDT_IRQHandler</a> from interrupts_hc32l186.o(.text.PCA_WWDT_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[1f]">PCNT_IRQHandler</a> from interrupts_hc32l186.o(.text.PCNT_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[6]">PORTA_IRQHandler</a> from interrupts_hc32l186.o(.text.PORTA_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[7]">PORTB_IRQHandler</a> from interrupts_hc32l186.o(.text.PORTB_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[8]">PORTC_E_IRQHandler</a> from interrupts_hc32l186.o(.text.PORTC_E_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[9]">PORTD_F_IRQHandler</a> from interrupts_hc32l186.o(.text.PORTD_F_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[2f]">Pause_StepperMotorAction</a> from steppermotorlib.o(.text.Pause_StepperMotorAction) referenced from steppermotorlib.o(.rodata.Api)
 <LI><a href="#[4]">PendSV_Handler</a> from startup_hc32l186.o(.text) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[1d]">RTC_IRQHandler</a> from interrupts_hc32l186.o(.text.RTC_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_hc32l186.o(.text) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[10]">SPI0_IRQHandler</a> from interrupts_hc32l186.o(.text.SPI0_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[11]">SPI1_IRQHandler</a> from interrupts_hc32l186.o(.text.SPI1_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[3]">SVC_Handler</a> from startup_hc32l186.o(.text) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[2d]">Set_StepperMotorTartget</a> from steppermotorlib.o(.text.Set_StepperMotorTartget) referenced from steppermotorlib.o(.rodata.Api)
 <LI><a href="#[2e]">Stop_StepperMotorAction</a> from steppermotorlib.o(.text.Stop_StepperMotorAction) referenced from steppermotorlib.o(.rodata.Api)
 <LI><a href="#[5]">SysTick_Handler</a> from interrupts_hc32l186.o(.text.SysTick_Handler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[27]">SystemInit</a> from system_hc32l186.o(.text.SystemInit) referenced from startup_hc32l186.o(.text)
 <LI><a href="#[14]">TIM0_IRQHandler</a> from interrupts_hc32l186.o(.text.TIM0_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[15]">TIM1_IRQHandler</a> from interrupts_hc32l186.o(.text.TIM1_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[16]">TIM2_IRQHandler</a> from interrupts_hc32l186.o(.text.TIM2_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[b]">TIM3_IRQHandler</a> from interrupts_hc32l186.o(.text.TIM3_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[18]">TIM4_IRQHandler</a> from interrupts_hc32l186.o(.text.TIM4_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[19]">TIM5_IRQHandler</a> from interrupts_hc32l186.o(.text.TIM5_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[1a]">TIM6_IRQHandler</a> from interrupts_hc32l186.o(.text.TIM6_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[c]">UART0_2_IRQHandler</a> from interrupts_hc32l186.o(.text.UART0_2_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[d]">UART1_3_IRQHandler</a> from interrupts_hc32l186.o(.text.UART1_3_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[20]">VC0_IRQHandler</a> from interrupts_hc32l186.o(.text.VC0_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[21]">VC1_2_IRQHandler</a> from interrupts_hc32l186.o(.text.VC1_2_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[1c]">WDT_IRQHandler</a> from interrupts_hc32l186.o(.text.WDT_IRQHandler) referenced from startup_hc32l186.o(RESET)
 <LI><a href="#[28]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_hc32l186.o(.text)
 <LI><a href="#[2b]">_sputc</a> from printf8.o(i._sputc) referenced from printf8.o(i.__0sprintf$8)
 <LI><a href="#[2a]">fputc</a> from ramlog.o(.text.fputc) referenced from printf8.o(i.__0printf$8)
 <LI><a href="#[26]">main</a> from system_hc32l186.o(.text.$Sub$$main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[28]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(.text)
</UL>
<P><STRONG><a name="[cb]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[38]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[41]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[cc]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[cd]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[ce]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[cf]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[d0]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, startup_hc32l186.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_hc32l186.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_hc32l186.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_hc32l186.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, cmb_fault.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = HardFault_Handler &rArr; cm_backtrace_fault &rArr; print_call_stack &rArr; cm_backtrace_call_stack
</UL>
<BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wdt_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[d1]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[d2]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[3b]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ddl_memclr
</UL>

<P><STRONG><a name="[c0]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_call_stack
</UL>

<P><STRONG><a name="[d3]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[3d]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[4c]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, uidiv_div0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay10us
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time0Init
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitSysTick
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer3Init
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer2Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay1ms
</UL>

<P><STRONG><a name="[d4]"></a>__aeabi_uidivmod</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, uidiv_div0.o(.text), UNUSED)

<P><STRONG><a name="[3e]"></a>__aeabi_uldivmod</STRONG> (Thumb, 96 bytes, Stack size 48 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[39]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[d5]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[40]"></a>__aeabi_llsl</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, llshl.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[d6]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[3f]"></a>__aeabi_llsr</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, llushr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[d7]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[26]"></a>main</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, system_hc32l186.o(.text.$Sub$$main))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = main &rArr; $Super$$main &rArr; Init_Mcu &rArr; Board_InitTim &rArr; AdtTimer3Init &rArr; Tim3_M23_PortOutput_Cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[1e]"></a>ADC_DAC_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.ADC_DAC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_DAC_IRQHandler &rArr; Adc_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Dac_IRQHandler (Weak Reference)
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adc_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>Adc_ClrIrqStatus</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, adc.o(.text.Adc_ClrIrqStatus))
<BR><BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adc_IRQHandler
</UL>

<P><STRONG><a name="[44]"></a>Adc_GetIrqStatus</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, adc.o(.text.Adc_GetIrqStatus))
<BR><BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adc_IRQHandler
</UL>

<P><STRONG><a name="[46]"></a>Adc_GetSqrResult</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc.o(.text.Adc_GetSqrResult))
<BR><BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adc_IRQHandler
</UL>

<P><STRONG><a name="[34]"></a>Adc_IRQHandler</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, adpt_adc.o(.text.Adc_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Adc_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adc_SQR_Stop
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adc_GetSqrResult
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adc_GetIrqStatus
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adc_ClrIrqStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DAC_IRQHandler
</UL>

<P><STRONG><a name="[47]"></a>Adc_SQR_Stop</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, adc.o(.text.Adc_SQR_Stop))
<BR><BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Adc_IRQHandler
</UL>

<P><STRONG><a name="[80]"></a>Add_MSecCount</STRONG> (Thumb, 108 bytes, Stack size 0 bytes, systemtimermodule.o(.text.Add_MSecCount))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ISR_Timer_1ms
</UL>

<P><STRONG><a name="[54]"></a>AdtTimer3Init</STRONG> (Thumb, 180 bytes, Stack size 96 bytes, adpt_pwm.o(.text.AdtTimer3Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = AdtTimer3Init &rArr; Tim3_M23_PortOutput_Cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim3_Mode23_Init
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim3_M23_SetValidPeriod
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim3_M23_Run
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim3_M23_PortOutput_Cfg
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim3_M23_EnPWM_Output
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim3_M23_Cnt16Set
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim3_M23_CCR_Set
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim3_M23_ARRSet
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ddl_memclr
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_SetPeripheralGate
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitTim
</UL>

<P><STRONG><a name="[60]"></a>App_SystemClkInit_RC48M</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, adpt_clock.o(.text.App_SystemClkInit_RC48M))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = App_SystemClkInit_RC48M &rArr; Sysctrl_ClkSourceEnable &rArr; delay10us &rArr; __aeabi_uidiv
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_SysClkSwitch
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_SetRC48MTrim
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_ClkSourceEnable
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_WaitCycle
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitClock
</UL>

<P><STRONG><a name="[65]"></a>Board_InitClock</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, adpt_clock.o(.text.Board_InitClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = Board_InitClock &rArr; App_SystemClkInit_RC48M &rArr; Sysctrl_ClkSourceEnable &rArr; delay10us &rArr; __aeabi_uidiv
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;App_SystemClkInit_RC48M
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Mcu
</UL>

<P><STRONG><a name="[66]"></a>Board_InitPins</STRONG> (Thumb, 756 bytes, Stack size 88 bytes, adpt_gpio.o(.text.Board_InitPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Board_InitPins &rArr; Gpio_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gpio_SetAnalogMode
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gpio_SetAfMode
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gpio_Init
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_SetPeripheralGate
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Mcu
</UL>

<P><STRONG><a name="[84]"></a>Board_InitReset</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, adpt_reset.o(.text.Board_InitReset))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Mcu
</UL>

<P><STRONG><a name="[6a]"></a>Board_InitSysTick</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, adpt_timebase.o(.text.Board_InitSysTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = Board_InitSysTick &rArr; Time0Init &rArr; Bt_Mode0_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time0Init
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Config
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Mcu
</UL>

<P><STRONG><a name="[6d]"></a>Board_InitTim</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, adpt_pwm.o(.text.Board_InitTim))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Board_InitTim &rArr; AdtTimer3Init &rArr; Tim3_M23_PortOutput_Cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer3Init
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer2Init
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_SetPeripheralGate
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Mcu
</UL>

<P><STRONG><a name="[a7]"></a>Bt_ClearIntFlag</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, bt.o(.text.Bt_ClearIntFlag))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time0Init
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim0_IRQHandler
</UL>

<P><STRONG><a name="[a6]"></a>Bt_GetIntFlag</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, bt.o(.text.Bt_GetIntFlag))
<BR><BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim0_IRQHandler
</UL>

<P><STRONG><a name="[a9]"></a>Bt_M0_ARRSet</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, bt.o(.text.Bt_M0_ARRSet))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Bt_M0_ARRSet
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time0Init
</UL>

<P><STRONG><a name="[aa]"></a>Bt_M0_Cnt16Set</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, bt.o(.text.Bt_M0_Cnt16Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Bt_M0_Cnt16Set
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time0Init
</UL>

<P><STRONG><a name="[ac]"></a>Bt_M0_Run</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, bt.o(.text.Bt_M0_Run))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time0Init
</UL>

<P><STRONG><a name="[4d]"></a>Bt_M23_ARRSet</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, bt.o(.text.Bt_M23_ARRSet))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Bt_M23_ARRSet
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer2Init
</UL>

<P><STRONG><a name="[4e]"></a>Bt_M23_CCR_Set</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, bt.o(.text.Bt_M23_CCR_Set))
<BR><BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer2Init
</UL>

<P><STRONG><a name="[51]"></a>Bt_M23_Cnt16Set</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, bt.o(.text.Bt_M23_Cnt16Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Bt_M23_Cnt16Set
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer2Init
</UL>

<P><STRONG><a name="[52]"></a>Bt_M23_EnPWM_Output</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, bt.o(.text.Bt_M23_EnPWM_Output))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Bt_M23_EnPWM_Output
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer2Init
</UL>

<P><STRONG><a name="[4f]"></a>Bt_M23_PortOutput_Cfg</STRONG> (Thumb, 168 bytes, Stack size 24 bytes, bt.o(.text.Bt_M23_PortOutput_Cfg))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Bt_M23_PortOutput_Cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer2Init
</UL>

<P><STRONG><a name="[53]"></a>Bt_M23_Run</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, bt.o(.text.Bt_M23_Run))
<BR><BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer2Init
</UL>

<P><STRONG><a name="[50]"></a>Bt_M23_SetValidPeriod</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, bt.o(.text.Bt_M23_SetValidPeriod))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Bt_M23_SetValidPeriod
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer2Init
</UL>

<P><STRONG><a name="[ab]"></a>Bt_Mode0_EnableIrq</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, bt.o(.text.Bt_Mode0_EnableIrq))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time0Init
</UL>

<P><STRONG><a name="[a8]"></a>Bt_Mode0_Init</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, bt.o(.text.Bt_Mode0_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Bt_Mode0_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time0Init
</UL>

<P><STRONG><a name="[4b]"></a>Bt_Mode23_Init</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, bt.o(.text.Bt_Mode23_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Bt_Mode23_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer2Init
</UL>

<P><STRONG><a name="[25]"></a>CLKTRIM_CTRIM_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.CLKTRIM_CTRIM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CLKTRIM_CTRIM_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ctrim_IRQHandler (Weak Reference)
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ClkTrim_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>DMAC_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.DMAC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMAC_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Dmac_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>EnableNvic</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, interrupts_hc32l186.o(.text.EnableNvic))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = EnableNvic
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_EnableIRQ
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_DisableIRQ
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_ClearPendingIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time0Init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;software_reset
</UL>

<P><STRONG><a name="[79]"></a>Execute_SingleDamperControl</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, singledampermodule.o(.text.Execute_SingleDamperControl))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Execute_SingleDamperControl &rArr; Control_SingleDamper
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Control_SingleDamper
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Antifreeze_SingleDamper
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[82]"></a>Execute_SingleDamperDriver</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, singledampermodule.o(.text.Execute_SingleDamperDriver))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Execute_SingleDamperDriver
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ISR_Timer_500us
</UL>

<P><STRONG><a name="[24]"></a>FLASH_RAM_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.FLASH_RAM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FLASH_RAM_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ram_IRQHandler (Weak Reference)
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Flash_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>Flash_WaitCycle</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, flash.o(.text.Flash_WaitCycle))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;App_SystemClkInit_RC48M
</UL>

<P><STRONG><a name="[5f]"></a>Get_SecondCount</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, systemtimermodule.o(.text.Get_SecondCount))
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Control_SingleDamper
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Antifreeze_SingleDamper
</UL>

<P><STRONG><a name="[5e]"></a>Get_SecondElapsedTime</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, systemtimermodule.o(.text.Get_SecondElapsedTime))
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Control_SingleDamper
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Antifreeze_SingleDamper
</UL>

<P><STRONG><a name="[70]"></a>Gpio_ClrIO</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, gpio.o(.text.Gpio_ClrIO))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Gpio_ClrIO
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetBit
</UL>
<BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Config_IO_SingleDamperID0
</UL>

<P><STRONG><a name="[67]"></a>Gpio_Init</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, gpio.o(.text.Gpio_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Gpio_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetBit
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitPins
</UL>

<P><STRONG><a name="[69]"></a>Gpio_SetAfMode</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gpio.o(.text.Gpio_SetAfMode))
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitPins
</UL>

<P><STRONG><a name="[68]"></a>Gpio_SetAnalogMode</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, gpio.o(.text.Gpio_SetAnalogMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Gpio_SetAnalogMode
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetBit
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitPins
</UL>

<P><STRONG><a name="[71]"></a>Gpio_SetIO</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, gpio.o(.text.Gpio_SetIO))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Gpio_SetIO
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetBit
</UL>
<BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Config_IO_SingleDamperID0
</UL>

<P><STRONG><a name="[12]"></a>I2C0_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.I2C0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C0_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2c0_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>I2C1_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.I2C1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C1_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2c1_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[7f]"></a>ISR_Timer_1ms</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, timebase.o(.text.ISR_Timer_1ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ISR_Timer_1ms
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Add_MSecCount
</UL>
<BR>[Called By]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_IRQHandler
</UL>

<P><STRONG><a name="[81]"></a>ISR_Timer_500us</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, timebase.o(.text.ISR_Timer_500us))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ISR_Timer_500us &rArr; Execute_SingleDamperDriver
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_SingleDamperDriver
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim0_IRQHandler
</UL>

<P><STRONG><a name="[83]"></a>Init_Mcu</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, init_mcu.o(.text.Init_Mcu))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = Init_Mcu &rArr; Board_InitTim &rArr; AdtTimer3Init &rArr; Tim3_M23_PortOutput_Cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitReset
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitSysTick
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitTim
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitPins
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitClock
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[85]"></a>Init_SingleDamper</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, singledampermodule.o(.text.Init_SingleDamper))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Init_SingleDamper &rArr; Init_SingleDamperParm &rArr; Init_StepperMotor
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_SingleDamperParm
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[87]"></a>Init_StepperMotor</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, steppermotorlib.o(.text.Init_StepperMotor))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Init_StepperMotor
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_SingleDamperParm
</UL>

<P><STRONG><a name="[23]"></a>LCD_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.LCD_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LCD_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lcd_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>LPTIM0_1_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.LPTIM0_1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPTIM0_1_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LpTim1_IRQHandler (Weak Reference)
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LpTim0_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>LPUART0_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.LPUART0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPUART0_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LpUart0_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>LPUART1_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.LPUART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LPUART1_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LpUart1_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>LVD_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.LVD_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LVD_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Lvd_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>PCA_WWDT_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.PCA_WWDT_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PCA_WWDT_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wwdt_IRQHandler (Weak Reference)
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Pca_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>PCNT_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.PCNT_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PCNT_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Pcnt_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>PORTA_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.PORTA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PORTA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PortA_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>PORTB_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.PORTB_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PORTB_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PortB_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>PORTC_E_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.PORTC_E_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PORTC_E_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PortE_IRQHandler (Weak Reference)
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PortC_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>PORTD_F_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.PORTD_F_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PORTD_F_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PortF_IRQHandler (Weak Reference)
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PortD_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[c4]"></a>Panic</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ramlog.o(.text.Panic))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;software_reset
</UL>

<P><STRONG><a name="[1d]"></a>RTC_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.RTC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RTC_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rtc_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[be]"></a>Reset_SingleDamper</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, singledampermodule.o(.text.Reset_SingleDamper))
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[10]"></a>SPI0_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.SPI0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI0_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi0_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>SPI1_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.SPI1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI1_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Spi1_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>SetBit</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, ddl.o(.text.SetBit))
<BR><BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gpio_SetAnalogMode
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gpio_Init
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_SetPeripheralGate
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gpio_SetIO
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gpio_ClrIO
</UL>

<P><STRONG><a name="[bf]"></a>Set_SingleDamperState</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, singledampermodule.o(.text.Set_SingleDamperState))
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;$Super$$main
</UL>

<P><STRONG><a name="[5]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SysTick_Handler &rArr; SysTick_IRQHandler &rArr; ISR_Timer_1ms
</UL>
<BR>[Calls]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>SysTick_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, adpt_timebase.o(.text.SysTick_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SysTick_IRQHandler &rArr; ISR_Timer_1ms
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ISR_Timer_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[63]"></a>Sysctrl_ClkSourceEnable</STRONG> (Thumb, 348 bytes, Stack size 24 bytes, sysctrl.o(.text.Sysctrl_ClkSourceEnable))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = Sysctrl_ClkSourceEnable &rArr; delay10us &rArr; __aeabi_uidiv
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay10us
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_SysctrlUnlock
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;App_SystemClkInit_RC48M
</UL>

<P><STRONG><a name="[9f]"></a>Sysctrl_GetHClkFreq</STRONG> (Thumb, 320 bytes, Stack size 24 bytes, sysctrl.o(.text.Sysctrl_GetHClkFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Sysctrl_GetHClkFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemCoreClockUpdate
</UL>

<P><STRONG><a name="[4a]"></a>Sysctrl_SetPeripheralGate</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, sysctrl.o(.text.Sysctrl_SetPeripheralGate))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Sysctrl_SetPeripheralGate
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetBit
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time0Init
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitTim
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer3Init
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer2Init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;software_reset
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitPins
</UL>

<P><STRONG><a name="[62]"></a>Sysctrl_SetRC48MTrim</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, sysctrl.o(.text.Sysctrl_SetRC48MTrim))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;App_SystemClkInit_RC48M
</UL>

<P><STRONG><a name="[64]"></a>Sysctrl_SysClkSwitch</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, sysctrl.o(.text.Sysctrl_SysClkSwitch))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Sysctrl_SysClkSwitch &rArr; SystemCoreClockUpdate &rArr; Sysctrl_GetHClkFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemCoreClockUpdate
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_SysctrlUnlock
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;App_SystemClkInit_RC48M
</UL>

<P><STRONG><a name="[9e]"></a>SystemCoreClockUpdate</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, system_hc32l186.o(.text.SystemCoreClockUpdate))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SystemCoreClockUpdate &rArr; Sysctrl_GetHClkFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_GetHClkFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_SysClkSwitch
</UL>

<P><STRONG><a name="[27]"></a>SystemInit</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, system_hc32l186.o(.text.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SystemInit &rArr; SystemCoreClockUpdate &rArr; Sysctrl_GetHClkFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemCoreClockUpdate
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(.text)
</UL>
<P><STRONG><a name="[14]"></a>TIM0_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.TIM0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = TIM0_IRQHandler &rArr; Tim0_IRQHandler &rArr; ISR_Timer_500us &rArr; Execute_SingleDamperDriver
</UL>
<BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim0_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>TIM1_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.TIM1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM1_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim1_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>TIM2_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.TIM2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM2_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim2_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>TIM3_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.TIM3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM3_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim3_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>TIM4_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.TIM4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM4_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim4_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>TIM5_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.TIM5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM5_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim5_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>TIM6_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.TIM6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM6_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tim6_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>Tim0_IRQHandler</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, adpt_timebase.o(.text.Tim0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Tim0_IRQHandler &rArr; ISR_Timer_500us &rArr; Execute_SingleDamperDriver
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ISR_Timer_500us
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_GetIntFlag
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_ClearIntFlag
</UL>
<BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM0_IRQHandler
</UL>

<P><STRONG><a name="[56]"></a>Tim3_M23_ARRSet</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, timer3.o(.text.Tim3_M23_ARRSet))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Tim3_M23_ARRSet
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer3Init
</UL>

<P><STRONG><a name="[57]"></a>Tim3_M23_CCR_Set</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, timer3.o(.text.Tim3_M23_CCR_Set))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer3Init
</UL>

<P><STRONG><a name="[5a]"></a>Tim3_M23_Cnt16Set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, timer3.o(.text.Tim3_M23_Cnt16Set))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer3Init
</UL>

<P><STRONG><a name="[5b]"></a>Tim3_M23_EnPWM_Output</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, timer3.o(.text.Tim3_M23_EnPWM_Output))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Tim3_M23_EnPWM_Output
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer3Init
</UL>

<P><STRONG><a name="[58]"></a>Tim3_M23_PortOutput_Cfg</STRONG> (Thumb, 512 bytes, Stack size 24 bytes, timer3.o(.text.Tim3_M23_PortOutput_Cfg))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Tim3_M23_PortOutput_Cfg
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer3Init
</UL>

<P><STRONG><a name="[5c]"></a>Tim3_M23_Run</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, timer3.o(.text.Tim3_M23_Run))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer3Init
</UL>

<P><STRONG><a name="[59]"></a>Tim3_M23_SetValidPeriod</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, timer3.o(.text.Tim3_M23_SetValidPeriod))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Tim3_M23_SetValidPeriod
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer3Init
</UL>

<P><STRONG><a name="[55]"></a>Tim3_Mode23_Init</STRONG> (Thumb, 140 bytes, Stack size 16 bytes, timer3.o(.text.Tim3_Mode23_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Tim3_Mode23_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer3Init
</UL>

<P><STRONG><a name="[6c]"></a>Time0Init</STRONG> (Thumb, 128 bytes, Stack size 48 bytes, adpt_timebase.o(.text.Time0Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = Time0Init &rArr; Bt_Mode0_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_Mode0_Init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_Mode0_EnableIrq
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_M0_Run
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_M0_Cnt16Set
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_M0_ARRSet
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_ClearIntFlag
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ddl_memclr
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_SetPeripheralGate
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnableNvic
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitSysTick
</UL>

<P><STRONG><a name="[c]"></a>UART0_2_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.UART0_2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART0_2_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart2_IRQHandler (Weak Reference)
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart0_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>UART1_3_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.UART1_3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART1_3_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart3_IRQHandler (Weak Reference)
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart1_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>VC0_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.VC0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = VC0_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vc0_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>VC1_2_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.VC1_2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = VC1_2_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vc2_IRQHandler (Weak Reference)
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vc1_IRQHandler (Weak Reference)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>WDT_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, interrupts_hc32l186.o(.text.WDT_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = WDT_IRQHandler &rArr; Wdt_IRQHandler &rArr; HardFault_Handler &rArr; cm_backtrace_fault &rArr; print_call_stack &rArr; cm_backtrace_call_stack
</UL>
<BR>[Calls]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wdt_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_hc32l186.o(RESET)
</UL>
<P><STRONG><a name="[c2]"></a>Wdt_GetIrqStatus</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, wdt.o(.text.Wdt_GetIrqStatus))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;software_reset
</UL>

<P><STRONG><a name="[35]"></a>Wdt_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, adpt_iwdg.o(.text.Wdt_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = Wdt_IRQHandler &rArr; HardFault_Handler &rArr; cm_backtrace_fault &rArr; print_call_stack &rArr; cm_backtrace_call_stack
</UL>
<BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WDT_IRQHandler
</UL>

<P><STRONG><a name="[c3]"></a>Wdt_IrqClr</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, wdt.o(.text.Wdt_IrqClr))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;software_reset
</UL>

<P><STRONG><a name="[b4]"></a>cm_backtrace_call_stack</STRONG> (Thumb, 272 bytes, Stack size 40 bytes, cm_backtrace.o(.text.cm_backtrace_call_stack))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = cm_backtrace_call_stack
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disassembly_ins_is_bl_blx
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_call_stack
</UL>

<P><STRONG><a name="[3a]"></a>cm_backtrace_fault</STRONG> (Thumb, 304 bytes, Stack size 48 bytes, cm_backtrace.o(.text.cm_backtrace_fault))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = cm_backtrace_fault &rArr; print_call_stack &rArr; cm_backtrace_call_stack
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;software_reset
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;puts
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_firmware_info
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_call_stack
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dump_stack
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>

<P><STRONG><a name="[b8]"></a>cm_backtrace_firmware_info</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, cm_backtrace.o(.text.cm_backtrace_firmware_info))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = cm_backtrace_firmware_info &rArr; puts &rArr; fputc &rArr; ramlog_puts
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;puts
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
</UL>

<P><STRONG><a name="[49]"></a>ddl_memclr</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, ddl.o(.text.ddl_memclr))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ddl_memclr
</UL>
<BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Time0Init
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer3Init
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdtTimer2Init
</UL>

<P><STRONG><a name="[9d]"></a>delay10us</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, ddl.o(.text.delay10us))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = delay10us &rArr; __aeabi_uidiv
</UL>
<BR>[Calls]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_ClkSourceEnable
</UL>

<P><STRONG><a name="[9c]"></a>delay1ms</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, ddl.o(.text.delay1ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = delay1ms &rArr; __aeabi_uidiv
</UL>
<BR>[Calls]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_ClkSourceEnable
</UL>

<P><STRONG><a name="[2a]"></a>fputc</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, ramlog.o(.text.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fputc &rArr; ramlog_puts
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ramlog_puts
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ramlog_putc
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;puts
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printf8.o(i.__0printf$8)
</UL>
<P><STRONG><a name="[42]"></a>$Super$$main</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = $Super$$main &rArr; Init_Mcu &rArr; Board_InitTim &rArr; AdtTimer3Init &rArr; Tim3_M23_PortOutput_Cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Mcu
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_SingleDamperState
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_SingleDamper
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_SingleDamper
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_SecondElapsedTime
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_SecondCount
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_SingleDamperControl
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[bb]"></a>software_reset</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, adpt_iwdg.o(.text.software_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = software_reset &rArr; puts &rArr; fputc &rArr; ramlog_puts
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wdt_IrqClr
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wdt_GetIrqStatus
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Panic
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SystemReset
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_SetPeripheralGate
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnableNvic
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;puts
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
</UL>

<P><STRONG><a name="[c6]"></a>__0printf$8</STRONG> (Thumb, 24 bytes, Stack size 24 bytes, printf8.o(i.__0printf$8), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[d8]"></a>__1printf$8</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf8.o(i.__0printf$8), UNUSED)

<P><STRONG><a name="[b6]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf8.o(i.__0printf$8))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_firmware_info
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_call_stack
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dump_stack
</UL>

<P><STRONG><a name="[c8]"></a>__0sprintf$8</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printf8.o(i.__0sprintf$8), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[d9]"></a>__1sprintf$8</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf8.o(i.__0sprintf$8), UNUSED)

<P><STRONG><a name="[c1]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf8.o(i.__0sprintf$8))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_call_stack
</UL>

<P><STRONG><a name="[da]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[db]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[dc]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[b7]"></a>puts</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, puts.o(i.puts))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = puts &rArr; fputc &rArr; ramlog_puts
</UL>
<BR>[Calls]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;software_reset
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_firmware_info
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;print_call_stack
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dump_stack
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[c5]"></a>__NVIC_SystemReset</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, adpt_iwdg.o(.text.__NVIC_SystemReset))
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;software_reset
</UL>

<P><STRONG><a name="[48]"></a>AdtTimer2Init</STRONG> (Thumb, 164 bytes, Stack size 96 bytes, adpt_pwm.o(.text.AdtTimer2Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = AdtTimer2Init &rArr; Bt_M23_PortOutput_Cfg
</UL>
<BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_Mode23_Init
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_M23_SetValidPeriod
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_M23_Run
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_M23_PortOutput_Cfg
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_M23_EnPWM_Output
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_M23_Cnt16Set
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_M23_CCR_Set
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bt_M23_ARRSet
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ddl_memclr
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_SetPeripheralGate
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitTim
</UL>

<P><STRONG><a name="[6b]"></a>SysTick_Config</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, adpt_timebase.o(.text.SysTick_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Board_InitSysTick
</UL>

<P><STRONG><a name="[9a]"></a>__NVIC_SetPriority</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, adpt_timebase.o(.text.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Config
</UL>

<P><STRONG><a name="[75]"></a>__NVIC_ClearPendingIRQ</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, interrupts_hc32l186.o(.text.__NVIC_ClearPendingIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnableNvic
</UL>

<P><STRONG><a name="[78]"></a>__NVIC_DisableIRQ</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, interrupts_hc32l186.o(.text.__NVIC_DisableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnableNvic
</UL>

<P><STRONG><a name="[77]"></a>__NVIC_EnableIRQ</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, interrupts_hc32l186.o(.text.__NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnableNvic
</UL>

<P><STRONG><a name="[76]"></a>__NVIC_SetPriority</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, interrupts_hc32l186.o(.text.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EnableNvic
</UL>

<P><STRONG><a name="[9b]"></a>_SysctrlUnlock</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, sysctrl.o(.text._SysctrlUnlock))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_SysClkSwitch
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sysctrl_ClkSourceEnable
</UL>

<P><STRONG><a name="[5d]"></a>Antifreeze_SingleDamper</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, singledampermodule.o(.text.Antifreeze_SingleDamper))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Antifreeze_SingleDamper
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_SecondElapsedTime
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_SecondCount
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_SingleDamperControl
</UL>

<P><STRONG><a name="[29]"></a>Config_IO_SingleDamperID0</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, singledampermodule.o(.text.Config_IO_SingleDamperID0))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Config_IO_SingleDamperID0 &rArr; Gpio_SetIO
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gpio_SetIO
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Gpio_ClrIO
</UL>
<BR>[Address Reference Count : 1]<UL><LI> singledampermodule.o(.text.Init_SingleDamperParm)
</UL>
<P><STRONG><a name="[72]"></a>Control_SingleDamper</STRONG> (Thumb, 232 bytes, Stack size 16 bytes, singledampermodule.o(.text.Control_SingleDamper))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Control_SingleDamper
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_SecondElapsedTime
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_SecondCount
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Execute_SingleDamperControl
</UL>

<P><STRONG><a name="[86]"></a>Init_SingleDamperParm</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, singledampermodule.o(.text.Init_SingleDamperParm))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Init_SingleDamperParm &rArr; Init_StepperMotor
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_StepperMotor
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_SingleDamper
</UL>

<P><STRONG><a name="[bc]"></a>ramlog_putc</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, ramlog.o(.text.ramlog_putc))
<BR><BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ramlog_puts
</UL>

<P><STRONG><a name="[bd]"></a>ramlog_puts</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, ramlog.o(.text.ramlog_puts))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ramlog_puts
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ramlog_putc
</UL>
<BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[b5]"></a>disassembly_ins_is_bl_blx</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, cm_backtrace.o(.text.disassembly_ins_is_bl_blx))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_call_stack
</UL>

<P><STRONG><a name="[b9]"></a>dump_stack</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, cm_backtrace.o(.text.dump_stack))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = dump_stack &rArr; puts &rArr; fputc &rArr; ramlog_puts
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;puts
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
</UL>

<P><STRONG><a name="[ba]"></a>print_call_stack</STRONG> (Thumb, 100 bytes, Stack size 160 bytes, cm_backtrace.o(.text.print_call_stack))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = print_call_stack &rArr; cm_backtrace_call_stack
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;puts
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_call_stack
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cm_backtrace_fault
</UL>

<P><STRONG><a name="[2c]"></a>Drive_StepperMotorISR</STRONG> (Thumb, 176 bytes, Stack size 16 bytes, steppermotorlib.o(.text.Drive_StepperMotorISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Drive_StepperMotorISR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> steppermotorlib.o(.rodata.Api)
</UL>
<P><STRONG><a name="[31]"></a>Get_StepperMotorIsPausing</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, steppermotorlib.o(.text.Get_StepperMotorIsPausing))
<BR>[Address Reference Count : 1]<UL><LI> steppermotorlib.o(.rodata.Api)
</UL>
<P><STRONG><a name="[30]"></a>Get_StepperMotorIsRunning</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, steppermotorlib.o(.text.Get_StepperMotorIsRunning))
<BR>[Address Reference Count : 1]<UL><LI> steppermotorlib.o(.rodata.Api)
</UL>
<P><STRONG><a name="[33]"></a>Get_StepperMotorRemainingSteps</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, steppermotorlib.o(.text.Get_StepperMotorRemainingSteps))
<BR>[Address Reference Count : 1]<UL><LI> steppermotorlib.o(.rodata.Api)
</UL>
<P><STRONG><a name="[32]"></a>Get_StepperMotorRunSteps</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, steppermotorlib.o(.text.Get_StepperMotorRunSteps))
<BR>[Address Reference Count : 1]<UL><LI> steppermotorlib.o(.rodata.Api)
</UL>
<P><STRONG><a name="[2f]"></a>Pause_StepperMotorAction</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, steppermotorlib.o(.text.Pause_StepperMotorAction))
<BR>[Address Reference Count : 1]<UL><LI> steppermotorlib.o(.rodata.Api)
</UL>
<P><STRONG><a name="[2d]"></a>Set_StepperMotorTartget</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, steppermotorlib.o(.text.Set_StepperMotorTartget))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Set_StepperMotorTartget
</UL>
<BR>[Address Reference Count : 1]<UL><LI> steppermotorlib.o(.rodata.Api)
</UL>
<P><STRONG><a name="[2e]"></a>Stop_StepperMotorAction</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, steppermotorlib.o(.text.Stop_StepperMotorAction))
<BR>[Address Reference Count : 1]<UL><LI> steppermotorlib.o(.rodata.Api)
</UL>
<P><STRONG><a name="[c7]"></a>_printf_core</STRONG> (Thumb, 1020 bytes, Stack size 104 bytes, printf8.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf$8
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf$8
</UL>

<P><STRONG><a name="[ca]"></a>_printf_post_padding</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, printf8.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[c9]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 40 bytes, printf8.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2b]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printf8.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf$8
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printf8.o(i.__0sprintf$8)
</UL><P>
<H3>
Undefined Global Symbols
</H3>
<P><STRONG><a name="[6f]"></a>ClkTrim_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLKTRIM_CTRIM_IRQHandler
</UL>

<P><STRONG><a name="[6e]"></a>Ctrim_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CLKTRIM_CTRIM_IRQHandler
</UL>

<P><STRONG><a name="[43]"></a>Dac_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DAC_IRQHandler
</UL>

<P><STRONG><a name="[73]"></a>Dmac_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMAC_IRQHandler
</UL>

<P><STRONG><a name="[7a]"></a>Flash_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_RAM_IRQHandler
</UL>

<P><STRONG><a name="[7d]"></a>I2c0_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C0_IRQHandler
</UL>

<P><STRONG><a name="[7e]"></a>I2c1_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C1_IRQHandler
</UL>

<P><STRONG><a name="[88]"></a>Lcd_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_IRQHandler
</UL>

<P><STRONG><a name="[89]"></a>LpTim0_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPTIM0_1_IRQHandler
</UL>

<P><STRONG><a name="[8a]"></a>LpTim1_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPTIM0_1_IRQHandler
</UL>

<P><STRONG><a name="[8b]"></a>LpUart0_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART0_IRQHandler
</UL>

<P><STRONG><a name="[8c]"></a>LpUart1_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LPUART1_IRQHandler
</UL>

<P><STRONG><a name="[8d]"></a>Lvd_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LVD_IRQHandler
</UL>

<P><STRONG><a name="[8e]"></a>Pca_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCA_WWDT_IRQHandler
</UL>

<P><STRONG><a name="[90]"></a>Pcnt_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCNT_IRQHandler
</UL>

<P><STRONG><a name="[91]"></a>PortA_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORTA_IRQHandler
</UL>

<P><STRONG><a name="[92]"></a>PortB_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORTB_IRQHandler
</UL>

<P><STRONG><a name="[93]"></a>PortC_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORTC_E_IRQHandler
</UL>

<P><STRONG><a name="[95]"></a>PortD_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORTD_F_IRQHandler
</UL>

<P><STRONG><a name="[94]"></a>PortE_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORTC_E_IRQHandler
</UL>

<P><STRONG><a name="[96]"></a>PortF_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORTD_F_IRQHandler
</UL>

<P><STRONG><a name="[7b]"></a>Ram_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_RAM_IRQHandler
</UL>

<P><STRONG><a name="[97]"></a>Rtc_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_IRQHandler
</UL>

<P><STRONG><a name="[98]"></a>Spi0_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_IRQHandler
</UL>

<P><STRONG><a name="[99]"></a>Spi1_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_IRQHandler
</UL>

<P><STRONG><a name="[a0]"></a>Tim1_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_IRQHandler
</UL>

<P><STRONG><a name="[a1]"></a>Tim2_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_IRQHandler
</UL>

<P><STRONG><a name="[a2]"></a>Tim3_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_IRQHandler
</UL>

<P><STRONG><a name="[a3]"></a>Tim4_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_IRQHandler
</UL>

<P><STRONG><a name="[a4]"></a>Tim5_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_IRQHandler
</UL>

<P><STRONG><a name="[a5]"></a>Tim6_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_IRQHandler
</UL>

<P><STRONG><a name="[ad]"></a>Uart0_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_2_IRQHandler
</UL>

<P><STRONG><a name="[af]"></a>Uart1_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_3_IRQHandler
</UL>

<P><STRONG><a name="[ae]"></a>Uart2_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_2_IRQHandler
</UL>

<P><STRONG><a name="[b0]"></a>Uart3_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_3_IRQHandler
</UL>

<P><STRONG><a name="[b1]"></a>Vc0_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VC0_IRQHandler
</UL>

<P><STRONG><a name="[b2]"></a>Vc1_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VC1_2_IRQHandler
</UL>

<P><STRONG><a name="[b3]"></a>Vc2_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VC1_2_IRQHandler
</UL>

<P><STRONG><a name="[8f]"></a>Wwdt_IRQHandler</STRONG> (Unknown, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PCA_WWDT_IRQHandler
</UL>
<HR></body></html>
