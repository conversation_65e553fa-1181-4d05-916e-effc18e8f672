## 嵌入式软件注释规范

### **1 优秀的代码可以自我解释，不通过注释即可轻易读懂。**

说明：优秀的代码不写注释也可轻易读懂，注释无法把糟糕的代码变好，需要很多注释来解释的代码往往存在坏味道，需要重构。

示例：注释不能消除代码的坏味道：

```C++
／＊判断m是否为素数＊／
／＊返问值：：是素数，：不是素数*／
intp(intm)
{
intk = sqrt(m)；
for(inti = 2; i <= k; i++) 
if(m % i == 0)
break；      ／＊ 发现整除，表示m不为素数，结束遍历*／
／＊ 遍历中没有发现整除的悄况，返回*／
if(i > k)
return1；
／* 遍历中没有发现整除的情况 ，返回＊／
else
return0；
}
```

重构代码后，不需要注释：

```C++
intIsPrimeNumber(intnum)
{
intsqrt_of_num = sqrt(num);
for(inti = 2; i <= sqrt_of_num; i++)
  {
if(num % i == 0) 
      {
returnFALSE;
      }
  }
returnTRUE;
}
```

### **2 注释的内容要清楚、明了，含义准确，防止注释二义性。**

说明：有歧义的注释反而会导致维护者更难看懂代码，正如带两块表反而不知道准确时间。

示例：注释与代码相矛盾，注释内容也不清楚 ，前后矛盾。

```C++
／＊ 上报网管时要求故障ID与恢复ID相一致*／
／＊ 因此在此由告警级别获知是不是恢复ID*/
／＊ 若是恢复ID则设置为 Clearld ，否则设置为Alarmld*/ 
if(CLEAR_ALARM_LEVEL != RcData.level)
{
  SetAlarmID(RcData.Alarmld)；
} 
else
{
  SetAlarmID(RcData.Clearld)；
}
```

正确做法：修改注释描述如下 ：

```Bash
／＊网管达成协议：上报故障ID与恢复ID由告警级别确定，若是清除级别，ID设置为ClearId，否则设为Alarmld 。＊／
```

### 3 ** 在代码的功能、** **意图****层次上进行注释，即注释解释代码难以直接表达的意图，而不是重复描述代码。**

说明：注释的目的是解释代码的目的、功能和采用的方法，提供代码以外的信息，帮助读者理解代码， 防止没必要的重复注释信息。

对于实现代码中巧妙的、晦涩的、有趣的、重要的地方加以注释。

注释不是为了名词解释（what），而是说明用途（why）。

示例：如下注释纯属多余。

```C++
++i; /* increment i *／
if (receive_flag) /* if receive_flag is TRUE */
```

如下这种无价值的注释不应出现 （空洞的笑话，无关紧要的注释）

```Bash
／＊ 时间有限，现在是：04，根本来不及想为什么，也没人能帮我说清楚＊／
```

而如下的注释则给出了有用的信息：

```C++
／＊由于xx编号网上问题，在xx情况下，芯片可能存在写错误，此芯片进行写操作后，必须进行回读校验，如果回
读不正确，需要再重复写回读操作，最多重复三次，这样可以解决绝大多数网上应用时的写错误问题＊／
inttime = 0; 
do
{
  write_reg(some_addr,value); 
  time++;
  }while((read_reg(some_addr)!= value)&&(time < 3));
```

对于实现代码中巧妙的、晦涩的、有趣的、重要的地方加以注释，出彩的或复杂的代码块前要加注释， 如：

```C++
//Divide result by two, taking into account that x contains the carry from the add.
for(inti = 0; i < result->size(); i++)
{
  x = (x << 8) + (*result)[i]; 
  (*result)[i] ＝ x >> 1;
  x &= 1;
}
```

### 4** 修改代码时，维护代码周边的所有注释，以保证注释与代码的一致性。不再有用的注释要删除。**

说明：不要将无用的代码留在注释中，随时可以从源代码配置库中找回代码；即使只是想暂时排除代码，也要留个标注，不然可能会忘记处理它。

### 5** 文件头部应进行注释，注释必须列出：版权说明、版本号、生成日期、作者姓名、工号、内容、功能说明、与其它文件的关系、修改日志等，头文件的注释中还应有函数功能简要说明。 **

说明：通常头文件要对功能和用法作简单说明，源文件包含了更多的实现细节或算法讨论。

版权声明格式：`Copyright (C) 2024 Xiaomi Corporation`

示例：

```C
/**
  * Copyright (C) 2024 Xiaomi Corporation
  * @file example.h
  * @brief 头文件的简短描述。
  *
  * 这里是头文件的详细描述，可以包含头文件的用途、功能等信息。
  * <AUTHOR>
  * @date 时间
  * @version 版本号
  */
```

### 6** 文件头、函数头、全局常量变量、类型定义的注释格式采用工具可识别的格式。 说明：采用工具可识别的注释格式，例如doxygen格式，方便工具导出注释形成帮助文档。**

* 文件头注释

```C
/**
  * @file example.c
  * @brief 这是example模块的源文件。
  *
  * 这里是文件的详细描述，可以包含文件的用途、功能等信息。
  * <AUTHOR>
  * @date 2024-10-31
  * @version 1.0
  */
```

* 函数头注释

对于源文件中的函数定义，可以使用以下注释风格：

```C
/**
  * @brief 计算两个整数的和。
  *
  * 这个函数接收两个整数参数，并返回它们的和。
  *
  * @param a 第一个整数。
  * @param b 第二个整数。
  * @return 两个整数的和。
  */

int add(int a, int b) 
{
    return a + b;
}

/**
  * @brief 计算两个整数的乘积。
  *
  * 这个函数接收两个整数参数，并返回它们的乘积。
  * 这个函数是静态的，只能在本文件中使用。
  *
  * @param a 第一个整数。
  * @param b 第二个整数。
  * @return 两个整数的乘积。
  */

static int multiply(int a, int b) 
{
    return a * b;
}
```

* 全局变量注释

```C
/**
  *    @brief 全局配置参数。
  *    这个变量用于存储全局配置参数。
  */

int global_config = 10;


/**
  * @brief 模块内使用的配置参数。
  *
  * 这个静态变量用于存储模块的配置参数，例如阈值或最大值。
  */

static int moduleConfig = 100;
```

### 7** 函数声明处注释描述函数功能、性能及用法，包括输入和输出参数、函数返回值、可重入的要求等：定义处详细描述函数功能和实现要点，如实现的简要步骤、实现的理由、设计约束等。**

说明：重要的、复杂的函数，提供外部使用的接口函数应编写详细的注释。

### 8** 全局变量要有较详细的注释，包括对其功能、取值范围以及存取时注意事项等的说明。 **

示例：

```C++
/* The ErrorCode when SCCP translate *／
/* Global  Title  failure,  as  follows  *//* 变量作用、含义*／
/* 0 -SUCCESS    1 -GT Table error */
/* 2  -GT error    Others  -no use*/  /＊ 变量取值范围＊／
/* only function SCCP Translate ()  in */
/* this modual  can modify  it,and other */
／* module can visit it through call */
/*  the function  GetGTTransErrorCode ()  */ /＊ 使用方法＊／
BYTE g_GTTranErrorCode ;
```

### 9** 注释应放在其代码上方相邻位置或右方，不可放在下面。如放于上方则需与其上面的代码用空行隔开，且与下方代码缩进相同。**

示例：

```C++
/* active statistic task number */
#defineMAX_ACT_TASK_NUMBER 1000

#defineMAX_ACT_TASK_NUMBER 1000 /* active statistic task number  */
```

可按如下形式说明枚举／数据／联合结构。

```C++
/* sccp  interface with  sccp user primitive message  name  */
enumSCCP_USER_PRIMITIVE
{
  N_UNITDATA_IND,  /*sccp notify sccp user unit data come  */
  N_NOTICE_IND,  /*sccp notify  user the No. 7 network  can not  transmission this message */
  N_UNITDATA_REQ,  /*sccp user’ s unit data transmission request*/
};
```

### 10 **对于switch语句下的case语句，如果因为特殊情况需要处理完一个case后进入下一个case处理，必须在该case语句处理完、下一个case语句前加上明确的注释。 **

说明：这样比较清楚程序编写者的意图，有效防止无故遗漏break语句。

示例 （注意斜体加粗部分） ：

```C++
caseCMD_FWD:
  ProcessFwd();
/* now jump into case CMD_A */
caseCMD_A:
  ProcessA(); 
break;
//对于中间无处理的连续 case，己能较清晰说明意图，不强制注释。
switch(cmd_flag)
  {
caseCMD_A:
caseCMD_B:
  {
    ProcessCMD();
break;
  }
  ......
  }
```

### 11** 避免在注释中使用缩写，除非是业界通用或子系统内****标准化****的缩写。 **

### 12** 避免在一行代码或表达式的中间插入注释。**

说明：除非必要，不应在代码或表达中间插入注释，否则容易使代码可理解性变差。

### 13** 注释应考虑程序易读及外观排版的因素，建议用英文**

说明：注释建议使用英文

## 4.2 嵌入式软件注释生成

注释生成可使用 `VSCode`插件 `Doxygen Documentation Generator` ，有效提高代码注释效率

* 快捷键生成Doxygen风格注释
* 自动识别函数参数和返回类型
* 支持自定义注释模板
