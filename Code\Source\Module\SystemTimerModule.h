/*!
 * @file
 * @brief This is the header file for the system timer module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef __SYSTEMTIMERMODULE_H__
#define __SYSTEMTIMERMODULE_H__

#include <stdbool.h>
#include <stdint.h>


struct I_SystemTimer_Api_st;

typedef struct I_SystemTimer_st {
  const struct I_SystemTimer_Api_st *Api;
} I_SystemTimer_st;

typedef struct Clock_st {
  uint8_t u8_Day;
  uint8_t u8_Hour;
  uint8_t u8_Minute;
  uint8_t u8_Second;
  uint16_t u16_MSec;
} Clock_st;

typedef struct SystemTimer_st {
  I_SystemTimer_st Interface;
  struct {
    Clock_st st_Clock;
    uint16_t u16_DayCount;
    uint16_t u16_HourCount;
    uint16_t u16_MinuteCount;
    uint16_t u16_SecondCount;
    uint16_t u16_MSecCount;
  } _private;
} SystemTimer_st;

typedef struct I_SystemTimer_Api_st {
  uint16_t (*p_Get_DayCount)(void);
  uint16_t (*p_Get_HourCount)(void);
  uint16_t (*p_Get_MinuteCount)(void);
  uint16_t (*p_Get_SecondCount)(void);
  uint16_t (*p_Get_MSecCount)(void);
  uint16_t (*p_Get_DayElapsedTime)(uint16_t u16_dayStartTime);
  uint16_t (*p_Get_HourElapsedTime)(uint16_t u16_hourStartTime);
  uint16_t (*p_Get_MinuteElapsedTime)(uint16_t u16_minuteStartTime);
  uint16_t (*p_Get_SecondElapsedTime)(uint16_t u16_secondStartTime);
  uint16_t (*p_Get_MSecElapsedTime)(uint16_t u16_msecStartTime);

  void (*p_Shorten_Timer)(const uint16_t u16_minute);

  void (*p_Add_MSecCount)(void);
} I_SystemTimer_Api_st;

#define Get_SystemTimer_DayCount(instance) (instance)->Api->p_Get_DayCount()

#define Get_SystemTimer_HourCount(instance) (instance)->Api->p_Get_HourCount()

#define Get_SystemTimer_MinuteCount(instance)                                  \
  (instance)->Api->p_Get_MinuteCount()

#define Get_SystemTimer_SecondCount(instance)                                  \
  (instance)->Api->p_Get_SecondCount()

#define Get_SystemTimer_MSecCount(instance) (instance)->Api->p_Get_MSecCount()

#define Get_SystemTimer_DayElapsedTime(instance, u16_dayStartTime)             \
  (instance)->Api->p_Get_DayElapsedTime(u16_dayStartTime)

#define Get_SystemTimer_HourElapsedTime(instance, u16_hourStartTime)           \
  (instance)->Api->p_Get_HourElapsedTime(u16_hourStartTime)

#define Get_SystemTimer_MinuteElapsedTime(instance, u16_minuteStartTime)       \
  (instance)->Api->p_Get_MinuteElapsedTime(u16_minuteStartTime)

#define Get_SystemTimer_SecondElapsedTime(instance, u16_secondStartTime)       \
  (instance)->Api->p_Get_SecondElapsedTime(u16_secondStartTime)

#define Get_SystemTimer_MSecElapsedTime(instance, u16_msecStartTime)           \
  (instance)->Api->p_Get_MSecElapsedTime(u16_msecStartTime)

#define Shorten_SystemTimer(instance, u16_minute)                              \
  (instance)->Api->p_Shorten_Timer(u16_minute)

#define Add_SystemTimer_MSecCount(instance) (instance)->Api->p_Add_MSecCount()

/******************************************************************************
GLOBAL VARIABLES ȫ�ֱ�������
******************************************************************************/

extern SystemTimer_st st_SystemTimer;

#define SystemTimerModule_Interface st_SystemTimer.Interface

/******************************************************************************
GLOBAL FUNCTIONS ȫ�ֺ�������
******************************************************************************/

extern void Init_SystemTimer(void);

extern uint16_t Get_DayCount(void);

extern uint16_t Get_HourCount(void);

extern uint16_t Get_MinuteCount(void);

extern uint16_t Get_SecondCount(void);

extern uint16_t Get_MSecCount(void);

extern uint16_t Get_DayElapsedTime(uint16_t u16_dayStartTime);

extern uint16_t Get_HourElapsedTime(uint16_t u16_hourStartTime);

extern uint16_t Get_MinuteElapsedTime(uint16_t u16_minuteStartTime);

extern uint16_t Get_SecondElapsedTime(uint16_t u16_secondStartTime);

extern uint16_t Get_MSecElapsedTime(uint16_t u16_msecStartTime);

extern void Shorten_Timer(const uint16_t u16_minute);

extern void Add_MSecCount(void);

/******************************************************************************
******************************************************************************/

#endif /* __SYSTEMTIMERMODULE_H__ */
