set UV=C:\Keil_v5\UV4\UV4.exe
set PROJECTPATH=PATH_TO_PROJECT\MDK-ARM\xxx.uvprojx
set CODEPATH=PATH_TO_PROJECT\MDK-ARM\xxx.uvprojx
set COVERITYPATH=PATH_TO_COVERITY_INTERMEDIATE_PATH
set RULEFILEPATH=PATH_TO_RULE_FILE_PATH


cov-build --dir %COVERITYPATH%/result --tmpdir %COVERITYPATH%/tmp --encoding UTF-8 %UV% -r %PROJECTPATH% -o Build_Output.txt
cov-analyze --dir %COVERITYPATH%/result --strip-path  %CODEPATH%  --all  --coding-standard-config %RULEFILEPATH%/cov-analysis-win64-2024.12.1/config/coding-standards/misrac2012/misrac2012-all.config --jobs max8
cov-commit-defects --host ************ --dataport 443 --on-new-cert trust --stream stream_test --dir %COVERITYPATH%/result  --user <user_name> --password <password>