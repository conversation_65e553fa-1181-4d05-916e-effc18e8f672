


ARM Macro Assembler    Page 1 


    1 00000000         ;/******************************************************
                       ************************
    2 00000000         ;*  
    3 00000000         ;* Copyright (C) 2023, Xiaohua Semiconductor Co., Ltd. A
                       ll rights reserved.
    4 00000000         ;* 
    5 00000000         ;* This software component is licensed by XHSC under BSD
                        3-Clause license
    6 00000000         ;* (the "License"); You may not use this file except in 
                       compliance with the
    7 00000000         ;* License. You may obtain a copy of the License at:
    8 00000000         ;*                    opensource.org/licenses/BSD-3-Clau
                       se
    9 00000000         ;* 
   10 00000000         ;*/
   11 00000000         ;/******************************************************
                       ***********************/
   12 00000000         
   13 00000000         ;/******************************************************
                       ***********************/
   14 00000000         ;/*  Startup for ARM                                    
                                             */
   15 00000000         ;/*  Version     V1.0                                   
                                             */
   16 00000000         ;/*  Date        2023-03-01                             
                                             */
   17 00000000         ;/*  Target-mcu  {MCU_PN_H}                             
                                             */
   18 00000000         ;/******************************************************
                       ***********************/
   19 00000000         
   20 00000000         ; Stack Configuration
   21 00000000         ; Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   22 00000000         
   23 00000000 00001000 
                       Stack_Size
                               EQU              0x00001000
   24 00000000         
   25 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   26 00000000         Stack_Mem
                               SPACE            Stack_Size
   27 00001000         __initial_sp
   28 00001000         
   29 00001000         
   30 00001000         ; Heap Configuration
   31 00001000         ;  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   32 00001000         
   33 00001000 00000400 
                       Heap_Size
                               EQU              0x00000400
   34 00001000         
   35 00001000                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   36 00000000         __heap_base
   37 00000000         Heap_Mem
                               SPACE            Heap_Size
   38 00000400         __heap_limit
   39 00000400         



ARM Macro Assembler    Page 2 


   40 00000400         
   41 00000400                 PRESERVE8
   42 00000400                 THUMB
   43 00000400         
   44 00000400         
   45 00000400         ; Vector Table Mapped to Address 0 at Reset
   46 00000400         
   47 00000400                 AREA             RESET, DATA, READONLY
   48 00000000                 EXPORT           __Vectors
   49 00000000                 EXPORT           __Vectors_End
   50 00000000                 EXPORT           __Vectors_Size
   51 00000000         
   52 00000000         __Vectors
   53 00000000 00000000        DCD              __initial_sp ; Top of Stack
   54 00000004 00000000        DCD              Reset_Handler ; Reset        
   55 00000008 00000000        DCD              NMI_Handler ; NMI
   56 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault
   57 00000010 00000000        DCD              0           ; Reserved
   58 00000014 00000000        DCD              0           ; Reserved
   59 00000018 00000000        DCD              0           ; Reserved
   60 0000001C 00000000        DCD              0           ; Reserved
   61 00000020 00000000        DCD              0           ; Reserved
   62 00000024 00000000        DCD              0           ; Reserved
   63 00000028 00000000        DCD              0           ; Reserved
   64 0000002C 00000000        DCD              SVC_Handler ; SVCall
   65 00000030 00000000        DCD              0           ; Reserved
   66 00000034 00000000        DCD              0           ; Reserved
   67 00000038 00000000        DCD              PendSV_Handler ; PendSV
   68 0000003C 00000000        DCD              SysTick_Handler ; SysTick
   69 00000040         
   70 00000040 00000000        DCD              PORTA_IRQHandler
   71 00000044 00000000        DCD              PORTB_IRQHandler
   72 00000048 00000000        DCD              PORTC_E_IRQHandler
   73 0000004C 00000000        DCD              PORTD_F_IRQHandler
   74 00000050 00000000        DCD              DMAC_IRQHandler
   75 00000054 00000000        DCD              TIM3_IRQHandler
   76 00000058 00000000        DCD              UART0_2_IRQHandler
   77 0000005C 00000000        DCD              UART1_3_IRQHandler
   78 00000060 00000000        DCD              LPUART0_IRQHandler
   79 00000064 00000000        DCD              LPUART1_IRQHandler
   80 00000068 00000000        DCD              SPI0_IRQHandler
   81 0000006C 00000000        DCD              SPI1_IRQHandler
   82 00000070 00000000        DCD              I2C0_IRQHandler
   83 00000074 00000000        DCD              I2C1_IRQHandler
   84 00000078 00000000        DCD              TIM0_IRQHandler
   85 0000007C 00000000        DCD              TIM1_IRQHandler
   86 00000080 00000000        DCD              TIM2_IRQHandler
   87 00000084 00000000        DCD              LPTIM0_1_IRQHandler
   88 00000088 00000000        DCD              TIM4_IRQHandler
   89 0000008C 00000000        DCD              TIM5_IRQHandler
   90 00000090 00000000        DCD              TIM6_IRQHandler
   91 00000094 00000000        DCD              PCA_WWDT_IRQHandler
   92 00000098 00000000        DCD              WDT_IRQHandler
   93 0000009C 00000000        DCD              RTC_IRQHandler
   94 000000A0 00000000        DCD              ADC_DAC_IRQHandler
   95 000000A4 00000000        DCD              PCNT_IRQHandler
   96 000000A8 00000000        DCD              VC0_IRQHandler
   97 000000AC 00000000        DCD              VC1_2_IRQHandler
   98 000000B0 00000000        DCD              LVD_IRQHandler



ARM Macro Assembler    Page 3 


   99 000000B4 00000000        DCD              LCD_IRQHandler
  100 000000B8 00000000        DCD              FLASH_RAM_IRQHandler
  101 000000BC 00000000        DCD              CLKTRIM_CTRIM_IRQHandler
  102 000000C0         
  103 000000C0         
  104 000000C0         
  105 000000C0         __Vectors_End
  106 000000C0         
  107 000000C0 000000C0 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  108 000000C0         
  109 000000C0                 AREA             |.text|, CODE, READONLY
  110 00000000         
  111 00000000         
  112 00000000         ; Reset Handler
  113 00000000         
  114 00000000         Reset_Handler
                               PROC
  115 00000000                 EXPORT           Reset_Handler             [WEAK
]
  116 00000000                 IMPORT           SystemInit
  117 00000000                 IMPORT           __main
  118 00000000         
  119 00000000         ;reset NVIC if in rom debug
  120 00000000 4809            LDR              R0, =0x20000000
  121 00000002 4A0A            LDR              R2, =0x0
  122 00000004 2100            MOVS             R1, #0      ; for warning, 
  123 00000006 A100            ADD              R1, PC,#0   ; for A1609W, 
  124 00000008 4281            CMP              R1, R0
  125 0000000A D900            BLS              RAMCODE
  126 0000000C         
  127 0000000C         ; ram code base address. 
  128 0000000C 4602            MOV              R2, R0
  129 0000000E         RAMCODE
  130 0000000E         ; reset Vector table address.
  131 0000000E 4808            LDR              R0, =0xE000ED08
  132 00000010 6002            STR              R2, [R0]
  133 00000012         
  134 00000012 4808            LDR              R0, =SystemInit
  135 00000014 4780            BLX              R0
  136 00000016 4808            LDR              R0, =__main
  137 00000018 4700            BX               R0
  138 0000001A                 ENDP
  139 0000001A         
  140 0000001A         
  141 0000001A         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  142 0000001A         
  143 0000001A         NMI_Handler
                               PROC
  144 0000001A                 EXPORT           NMI_Handler               [WEAK
]
  145 0000001A E7FE            B                .
  146 0000001C                 ENDP
  147 0000001C         
  148 0000001C         
  150 0000001C         HardFault_Handler
                               PROC



ARM Macro Assembler    Page 4 


  151 0000001C                 EXPORT           HardFault_Handler         [WEAK
]
  152 0000001C E7FE            B                .
  153 0000001E                 ENDP
  154 0000001E         SVC_Handler
                               PROC
  155 0000001E                 EXPORT           SVC_Handler               [WEAK
]
  156 0000001E E7FE            B                .
  157 00000020                 ENDP
  158 00000020         PendSV_Handler
                               PROC
  159 00000020                 EXPORT           PendSV_Handler            [WEAK
]
  160 00000020 E7FE            B                .
  161 00000022                 ENDP
  162 00000022         SysTick_Handler
                               PROC
  163 00000022                 EXPORT           SysTick_Handler           [WEAK
]
  164 00000022 E7FE            B                .
  165 00000024                 ENDP
  166 00000024         
  167 00000024         Default_Handler
                               PROC
  168 00000024                 EXPORT           PORTA_IRQHandler               
 [WEAK]
  169 00000024                 EXPORT           PORTB_IRQHandler               
 [WEAK]
  170 00000024                 EXPORT           PORTC_E_IRQHandler             
 [WEAK]
  171 00000024                 EXPORT           PORTD_F_IRQHandler             
 [WEAK]
  172 00000024                 EXPORT           DMAC_IRQHandler                
 [WEAK]
  173 00000024                 EXPORT           TIM3_IRQHandler                
 [WEAK]
  174 00000024                 EXPORT           UART0_2_IRQHandler             
 [WEAK]
  175 00000024                 EXPORT           UART1_3_IRQHandler             
 [WEAK]
  176 00000024                 EXPORT           LPUART0_IRQHandler             
 [WEAK]
  177 00000024                 EXPORT           LPUART1_IRQHandler             
 [WEAK]
  178 00000024                 EXPORT           SPI0_IRQHandler                
 [WEAK]
  179 00000024                 EXPORT           SPI1_IRQHandler                
 [WEAK]
  180 00000024                 EXPORT           I2C0_IRQHandler                
 [WEAK]
  181 00000024                 EXPORT           I2C1_IRQHandler                
 [WEAK]
  182 00000024                 EXPORT           TIM0_IRQHandler                
 [WEAK]
  183 00000024                 EXPORT           TIM1_IRQHandler                
 [WEAK]
  184 00000024                 EXPORT           TIM2_IRQHandler                
 [WEAK]



ARM Macro Assembler    Page 5 


  185 00000024                 EXPORT           LPTIM0_1_IRQHandler            
 [WEAK]
  186 00000024                 EXPORT           TIM4_IRQHandler                
 [WEAK]
  187 00000024                 EXPORT           TIM5_IRQHandler                
 [WEAK]
  188 00000024                 EXPORT           TIM6_IRQHandler                
 [WEAK]
  189 00000024                 EXPORT           PCA_WWDT_IRQHandler            
 [WEAK]
  190 00000024                 EXPORT           WDT_IRQHandler                 
 [WEAK]
  191 00000024                 EXPORT           RTC_IRQHandler                 
 [WEAK]
  192 00000024                 EXPORT           ADC_DAC_IRQHandler             
 [WEAK]
  193 00000024                 EXPORT           PCNT_IRQHandler                
 [WEAK]
  194 00000024                 EXPORT           VC0_IRQHandler                 
 [WEAK]
  195 00000024                 EXPORT           VC1_2_IRQHandler               
 [WEAK]
  196 00000024                 EXPORT           LVD_IRQHandler                 
 [WEAK]
  197 00000024                 EXPORT           LCD_IRQHandler                 
 [WEAK]
  198 00000024                 EXPORT           FLASH_RAM_IRQHandler           
 [WEAK]
  199 00000024                 EXPORT           CLKTRIM_CTRIM_IRQHandler       
 [WEAK]
  200 00000024         PORTA_IRQHandler
  201 00000024         PORTB_IRQHandler
  202 00000024         PORTC_E_IRQHandler
  203 00000024         PORTD_F_IRQHandler
  204 00000024         DMAC_IRQHandler
  205 00000024         TIM3_IRQHandler
  206 00000024         UART0_2_IRQHandler
  207 00000024         UART1_3_IRQHandler
  208 00000024         LPUART0_IRQHandler
  209 00000024         LPUART1_IRQHandler
  210 00000024         SPI0_IRQHandler
  211 00000024         SPI1_IRQHandler
  212 00000024         I2C0_IRQHandler
  213 00000024         I2C1_IRQHandler
  214 00000024         TIM0_IRQHandler
  215 00000024         TIM1_IRQHandler
  216 00000024         TIM2_IRQHandler
  217 00000024         LPTIM0_1_IRQHandler
  218 00000024         TIM4_IRQHandler
  219 00000024         TIM5_IRQHandler
  220 00000024         TIM6_IRQHandler
  221 00000024         PCA_WWDT_IRQHandler
  222 00000024         WDT_IRQHandler
  223 00000024         RTC_IRQHandler
  224 00000024         ADC_DAC_IRQHandler
  225 00000024         PCNT_IRQHandler
  226 00000024         VC0_IRQHandler
  227 00000024         VC1_2_IRQHandler
  228 00000024         LVD_IRQHandler



ARM Macro Assembler    Page 6 


  229 00000024         LCD_IRQHandler
  230 00000024         FLASH_RAM_IRQHandler
  231 00000024         CLKTRIM_CTRIM_IRQHandler
  232 00000024         
  233 00000024         
  234 00000024 E7FE            B                .
  235 00000026         
  236 00000026                 ENDP
  237 00000026         
  238 00000026         
  239 00000026 00 00           ALIGN
  240 00000028         
  241 00000028         
  242 00000028         ; User Initial Stack & Heap
  243 00000028         
  244 00000028                 IF               :DEF:__MICROLIB
  245 00000028         
  246 00000028                 EXPORT           __initial_sp
  247 00000028                 EXPORT           __heap_base
  248 00000028                 EXPORT           __heap_limit
  249 00000028         
  250 00000028                 ELSE
  264                          ENDIF
  265 00000028         
  266 00000028         
  267 00000028                 END
              20000000 
              00000000 
              E000ED08 
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M0+ --depend=.\o
utput\release\startup_hc32l186.d -o.\output\release\startup_hc32l186.o -I.\RTE\
_MainBoard -Id:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include -Id:\Keil_v5\P
acks\HDSC\HC32L18x\1.0.0\Device\Include --predefine="__MICROLIB SETA 1" --prede
fine="__UVISION_VERSION SETA 535" --predefine="_RTE_ SETA 1" --predefine="HC32L
186KATH SETA 1" --predefine="_RTE_ SETA 1" --list=.\output\release\startup_hc32
l186.lst ..\Startup\startup_hc32l186.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 25 in file ..\Startup\startup_hc32l186.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 26 in file ..\Startup\startup_hc32l186.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00001000

Symbol: __initial_sp
   Definitions
      At line 27 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 53 in file ..\Startup\startup_hc32l186.s
      At line 246 in file ..\Startup\startup_hc32l186.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 35 in file ..\Startup\startup_hc32l186.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 37 in file ..\Startup\startup_hc32l186.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 36 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 247 in file ..\Startup\startup_hc32l186.s
Comment: __heap_base used once
__heap_limit 00000400

Symbol: __heap_limit
   Definitions
      At line 38 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 248 in file ..\Startup\startup_hc32l186.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 47 in file ..\Startup\startup_hc32l186.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 52 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 48 in file ..\Startup\startup_hc32l186.s
      At line 107 in file ..\Startup\startup_hc32l186.s

__Vectors_End 000000C0

Symbol: __Vectors_End
   Definitions
      At line 105 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 49 in file ..\Startup\startup_hc32l186.s
      At line 107 in file ..\Startup\startup_hc32l186.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 109 in file ..\Startup\startup_hc32l186.s
   Uses
      None
Comment: .text unused
ADC_DAC_IRQHandler 00000024

Symbol: ADC_DAC_IRQHandler
   Definitions
      At line 224 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 94 in file ..\Startup\startup_hc32l186.s
      At line 192 in file ..\Startup\startup_hc32l186.s

CLKTRIM_CTRIM_IRQHandler 00000024

Symbol: CLKTRIM_CTRIM_IRQHandler
   Definitions
      At line 231 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 101 in file ..\Startup\startup_hc32l186.s
      At line 199 in file ..\Startup\startup_hc32l186.s

DMAC_IRQHandler 00000024

Symbol: DMAC_IRQHandler
   Definitions
      At line 204 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 74 in file ..\Startup\startup_hc32l186.s
      At line 172 in file ..\Startup\startup_hc32l186.s

Default_Handler 00000024

Symbol: Default_Handler
   Definitions
      At line 167 in file ..\Startup\startup_hc32l186.s
   Uses
      None
Comment: Default_Handler unused
FLASH_RAM_IRQHandler 00000024

Symbol: FLASH_RAM_IRQHandler
   Definitions
      At line 230 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 100 in file ..\Startup\startup_hc32l186.s
      At line 198 in file ..\Startup\startup_hc32l186.s

HardFault_Handler 0000001C

Symbol: HardFault_Handler
   Definitions
      At line 150 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 56 in file ..\Startup\startup_hc32l186.s



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 151 in file ..\Startup\startup_hc32l186.s

I2C0_IRQHandler 00000024

Symbol: I2C0_IRQHandler
   Definitions
      At line 212 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 82 in file ..\Startup\startup_hc32l186.s
      At line 180 in file ..\Startup\startup_hc32l186.s

I2C1_IRQHandler 00000024

Symbol: I2C1_IRQHandler
   Definitions
      At line 213 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 83 in file ..\Startup\startup_hc32l186.s
      At line 181 in file ..\Startup\startup_hc32l186.s

LCD_IRQHandler 00000024

Symbol: LCD_IRQHandler
   Definitions
      At line 229 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 99 in file ..\Startup\startup_hc32l186.s
      At line 197 in file ..\Startup\startup_hc32l186.s

LPTIM0_1_IRQHandler 00000024

Symbol: LPTIM0_1_IRQHandler
   Definitions
      At line 217 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 87 in file ..\Startup\startup_hc32l186.s
      At line 185 in file ..\Startup\startup_hc32l186.s

LPUART0_IRQHandler 00000024

Symbol: LPUART0_IRQHandler
   Definitions
      At line 208 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 78 in file ..\Startup\startup_hc32l186.s
      At line 176 in file ..\Startup\startup_hc32l186.s

LPUART1_IRQHandler 00000024

Symbol: LPUART1_IRQHandler
   Definitions
      At line 209 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 79 in file ..\Startup\startup_hc32l186.s
      At line 177 in file ..\Startup\startup_hc32l186.s

LVD_IRQHandler 00000024

Symbol: LVD_IRQHandler



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 228 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 98 in file ..\Startup\startup_hc32l186.s
      At line 196 in file ..\Startup\startup_hc32l186.s

NMI_Handler 0000001A

Symbol: NMI_Handler
   Definitions
      At line 143 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 55 in file ..\Startup\startup_hc32l186.s
      At line 144 in file ..\Startup\startup_hc32l186.s

PCA_WWDT_IRQHandler 00000024

Symbol: PCA_WWDT_IRQHandler
   Definitions
      At line 221 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 91 in file ..\Startup\startup_hc32l186.s
      At line 189 in file ..\Startup\startup_hc32l186.s

PCNT_IRQHandler 00000024

Symbol: PCNT_IRQHandler
   Definitions
      At line 225 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 95 in file ..\Startup\startup_hc32l186.s
      At line 193 in file ..\Startup\startup_hc32l186.s

PORTA_IRQHandler 00000024

Symbol: PORTA_IRQHandler
   Definitions
      At line 200 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 70 in file ..\Startup\startup_hc32l186.s
      At line 168 in file ..\Startup\startup_hc32l186.s

PORTB_IRQHandler 00000024

Symbol: PORTB_IRQHandler
   Definitions
      At line 201 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 71 in file ..\Startup\startup_hc32l186.s
      At line 169 in file ..\Startup\startup_hc32l186.s

PORTC_E_IRQHandler 00000024

Symbol: PORTC_E_IRQHandler
   Definitions
      At line 202 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 72 in file ..\Startup\startup_hc32l186.s
      At line 170 in file ..\Startup\startup_hc32l186.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols


PORTD_F_IRQHandler 00000024

Symbol: PORTD_F_IRQHandler
   Definitions
      At line 203 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 73 in file ..\Startup\startup_hc32l186.s
      At line 171 in file ..\Startup\startup_hc32l186.s

PendSV_Handler 00000020

Symbol: PendSV_Handler
   Definitions
      At line 158 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 67 in file ..\Startup\startup_hc32l186.s
      At line 159 in file ..\Startup\startup_hc32l186.s

RAMCODE 0000000E

Symbol: RAMCODE
   Definitions
      At line 129 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 125 in file ..\Startup\startup_hc32l186.s
Comment: RAMCODE used once
RTC_IRQHandler 00000024

Symbol: RTC_IRQHandler
   Definitions
      At line 223 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 93 in file ..\Startup\startup_hc32l186.s
      At line 191 in file ..\Startup\startup_hc32l186.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 114 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 54 in file ..\Startup\startup_hc32l186.s
      At line 115 in file ..\Startup\startup_hc32l186.s

SPI0_IRQHandler 00000024

Symbol: SPI0_IRQHandler
   Definitions
      At line 210 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 80 in file ..\Startup\startup_hc32l186.s
      At line 178 in file ..\Startup\startup_hc32l186.s

SPI1_IRQHandler 00000024

Symbol: SPI1_IRQHandler
   Definitions
      At line 211 in file ..\Startup\startup_hc32l186.s



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 81 in file ..\Startup\startup_hc32l186.s
      At line 179 in file ..\Startup\startup_hc32l186.s

SVC_Handler 0000001E

Symbol: SVC_Handler
   Definitions
      At line 154 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 64 in file ..\Startup\startup_hc32l186.s
      At line 155 in file ..\Startup\startup_hc32l186.s

SysTick_Handler 00000022

Symbol: SysTick_Handler
   Definitions
      At line 162 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 68 in file ..\Startup\startup_hc32l186.s
      At line 163 in file ..\Startup\startup_hc32l186.s

TIM0_IRQHandler 00000024

Symbol: TIM0_IRQHandler
   Definitions
      At line 214 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 84 in file ..\Startup\startup_hc32l186.s
      At line 182 in file ..\Startup\startup_hc32l186.s

TIM1_IRQHandler 00000024

Symbol: TIM1_IRQHandler
   Definitions
      At line 215 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 85 in file ..\Startup\startup_hc32l186.s
      At line 183 in file ..\Startup\startup_hc32l186.s

TIM2_IRQHandler 00000024

Symbol: TIM2_IRQHandler
   Definitions
      At line 216 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 86 in file ..\Startup\startup_hc32l186.s
      At line 184 in file ..\Startup\startup_hc32l186.s

TIM3_IRQHandler 00000024

Symbol: TIM3_IRQHandler
   Definitions
      At line 205 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 75 in file ..\Startup\startup_hc32l186.s
      At line 173 in file ..\Startup\startup_hc32l186.s

TIM4_IRQHandler 00000024



ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols


Symbol: TIM4_IRQHandler
   Definitions
      At line 218 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 88 in file ..\Startup\startup_hc32l186.s
      At line 186 in file ..\Startup\startup_hc32l186.s

TIM5_IRQHandler 00000024

Symbol: TIM5_IRQHandler
   Definitions
      At line 219 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 89 in file ..\Startup\startup_hc32l186.s
      At line 187 in file ..\Startup\startup_hc32l186.s

TIM6_IRQHandler 00000024

Symbol: TIM6_IRQHandler
   Definitions
      At line 220 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 90 in file ..\Startup\startup_hc32l186.s
      At line 188 in file ..\Startup\startup_hc32l186.s

UART0_2_IRQHandler 00000024

Symbol: UART0_2_IRQHandler
   Definitions
      At line 206 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 76 in file ..\Startup\startup_hc32l186.s
      At line 174 in file ..\Startup\startup_hc32l186.s

UART1_3_IRQHandler 00000024

Symbol: UART1_3_IRQHandler
   Definitions
      At line 207 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 77 in file ..\Startup\startup_hc32l186.s
      At line 175 in file ..\Startup\startup_hc32l186.s

VC0_IRQHandler 00000024

Symbol: VC0_IRQHandler
   Definitions
      At line 226 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 96 in file ..\Startup\startup_hc32l186.s
      At line 194 in file ..\Startup\startup_hc32l186.s

VC1_2_IRQHandler 00000024

Symbol: VC1_2_IRQHandler
   Definitions
      At line 227 in file ..\Startup\startup_hc32l186.s
   Uses



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

      At line 97 in file ..\Startup\startup_hc32l186.s
      At line 195 in file ..\Startup\startup_hc32l186.s

WDT_IRQHandler 00000024

Symbol: WDT_IRQHandler
   Definitions
      At line 222 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 92 in file ..\Startup\startup_hc32l186.s
      At line 190 in file ..\Startup\startup_hc32l186.s

41 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000400

Symbol: Heap_Size
   Definitions
      At line 33 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 37 in file ..\Startup\startup_hc32l186.s
Comment: Heap_Size used once
Stack_Size 00001000

Symbol: Stack_Size
   Definitions
      At line 23 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 26 in file ..\Startup\startup_hc32l186.s
Comment: Stack_Size used once
__Vectors_Size 000000C0

Symbol: __Vectors_Size
   Definitions
      At line 107 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 50 in file ..\Startup\startup_hc32l186.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 116 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 134 in file ..\Startup\startup_hc32l186.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 117 in file ..\Startup\startup_hc32l186.s
   Uses
      At line 136 in file ..\Startup\startup_hc32l186.s
Comment: __main used once
2 symbols
392 symbols in table
