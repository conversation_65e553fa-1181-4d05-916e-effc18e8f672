## .gitignore使用    
     
### 规则 作用     
+ /SI 过滤整个文件夹     
+ *.zip 过滤所有.zip文件     
+ /SI/do.c 过滤某个具体文件     
+ !/SI/one.txt 追踪（不过滤）某个具体文件    
+ 注意：如果你创建.gitignore文件之前就push了某一文件，那么即使你在.gitignore文件中写入过滤该文件的规则，    
+ 该规则也不会起作用，git仍然会对该文件进行版本管理。

### 配置语法     
+ 以斜杠“/”开头表示目录；         
+ 以星号“*”通配多个字符；          
+ 以问号“?”通配单个字符；         
+ 以方括号“[]”包含单个字符的匹配列表；     
+ 以叹号“!”表示不忽略(跟踪)匹配到的文件或目录。     
+ 注意： git 对于 .gitignore配置文件是按行从上到下进行规则匹配的     

详细的忽略规则可以参考[官方英文文档](https://git-scm.com/docs/gitignore)    

### 使用     
+ 在仓库的根目录创建.gitignore文件

+ 提交之前，修改.gitignore文件，添加需要忽略的文件。然后再做add commit push 等

+ 在使用过程中，需要对.gitignore文件进行再次的修改。     
  这时我们需要清除一下缓存cache，才能使.gitignore 生效。
> git rm -r --cached .        
> git add .       
> git commit -m 'update .gitignore'          
   
 