#ifndef __TEST_CASES_H__
#define __TEST_CASES_H__

/******************************************************************************
  <stdarg.h> , <stddef.h> , <setjmp.h> 这三个标准库头文件必须在 <cmocka.h> 之前
  包含, 这个顺序是强制性的，因为cmocka内部依赖这些标准库的定义
******************************************************************************/
// clang-format off
#include <stdarg.h>
#include <stddef.h>
#include <setjmp.h>
#include <stdint.h>
#include <cmocka.h>
#include <string.h>
// clang-format on
#include "../../Code/StepperMotor/StepperMotorLib.h"

// 测试函数声明
void test_init_stepper_motor(void **state);
void test_set_target(void **state);
void test_pause_resume(void **state);
void test_stop_action(void **state);
void test_get_running_status(void **state);
void test_get_steps_info(void **state);
void test_drive_stepper_motor_isr(void **state);
void test_different_driver_types(void **state);

// 模拟IO控制函数
void mock_stepper_motor_io(const uint8_t io_state);

// 获取测试用例数组
const struct CMUnitTest *get_test_cases(void);
int get_test_cases_count(void);

#endif /* __TEST_CASES_H__ */
