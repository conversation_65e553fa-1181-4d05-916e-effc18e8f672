## 1  **MISRA C** **:2012 规则**

### 1.1 规范文件

**MISRA C 2012 Guidelines.pdf**

### 1.2 中文详解

[MISRA C:2012 标准中文详解](https://xiaomi.f.mioffice.cn/docx/doxk4wXz5mQufKDecjIFKZwMKKd)

### 1.3 大家电适用规则

1. 遵守全部 `强制 (Mandatory)`和 `要求 (Required)`级别的规则
2. 初期遵守全部 `建议 (Advisory)`规则，在项目开发过程中逐步反馈去除不适用的本级别规则

## 2 编译规则

必须启用**编译警告视为错误**的选项，并彻底解决所有编译错误

### 2.1 通用代码库编译规则

通用代码库是指可以被多个产品线项目共享使用的基础代码，它应具有良好的可移植性和稳定性，为此需遵循下述实现规则。

#### 2.1.1 模块化设计

* **独立编译单元** ：每个功能模块应设计为独立的编译单元
* **明确的接口定义** ：通过头文件提供清晰的API接口
* **最小依赖原则** ：减少模块间的相互依赖

#### 2.1.2 编译工具链

* **统一编译工具链** ：使用版本统一的标准编译工具链 (如linux下的 `ARM GCC`、windows下的keil `ARMCC`)
* **统一构建系统** ：使用版本统一的 `CMake`或 `Make`等构建系统
* 强制开启**警告视为错误**

```Makefile
CFLAGS += -Wall -Wextra -Werror
```

### 2.2 产品线项目编译规则

产品线项目是基于通用代码库，针对特定产品定制开发的项目。

#### 2.2.1 项目结构

* **分层架构** ：
* 硬件抽象层(HAL)
* 驱动层(Driver)
* 中间件层(Middleware)
* 应用层(Application)
* **明确的目录结构** ：

```Plain
project/
├── build/          # 构建输出目录
├── config/         # 项目配置文件
├── src/            # 项目源代码
│   ├── app/        # 应用层代码
│   ├── drivers/    # 驱动层代码
│   └── middleware/ # 中间件代码
├── lib/            # 第三方库和通用代码库
├── include/        # 公共头文件
└── tools/          # 构建和调试工具
```

#### 2.2.2 版本控制

* **版本信息编译** ：将版本信息编译到固件中

```C
#define FIRMWARE_VERSION "1.2.3"
#define BUILD_TIMESTAMP __DATE__ " " __TIME__
```

## 3 规范检查

规范检查结果由公司内部已广泛使用的 `Coverity Check`系统保证，一键扫描脚本参见本目录下的coverity_scan.bat
