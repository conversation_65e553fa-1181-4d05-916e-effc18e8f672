## 下载和安装

* 首先是官网的 Doxygen 软件最新版，选择适配自己电脑系统的版本就好。

[Download Doxygen](https://link.zhihu.com/?target=https%3A//doxygen.nl/download.html)

* 接下来是 [httphelper](https://zhida.zhihu.com/search?content_id=250368743&content_type=Article&match_order=1&q=httphelper&zhida_source=entity)，Doxygen 默认是生成 html 文件，如果需要生成 chm 文件，也需要 httphelper 提供的 API：

[Microsoft HTML Help Downloads](https://link.zhihu.com/?target=https%3A//docs.microsoft.com/zh-cn/previous-versions/windows/desktop/htmlhelp/microsoft-html-help-downloads%3Fredirectedfrom%3DMSDN)

* 最后是 Doxygen 生成可视化类图、依赖图的一个环境：

[Graphviz](https://link.zhihu.com/?target=https%3A//gitcode.com/open-source-toolkit/1e1ec/overview%3Futm_source%3Dhighlight_word_gitcode%26word%3DGraphviz%26null)

**建议先安装后面两个环境后，再去安装 Doxygen 软件。安装不需要思考太多，一路 Next 下去就行。**

## Doxygen 配置

这里主要说一下常用的几个配置，其他的可以默认或者有特殊需求再去找，它的配置项很多也都有注释，但是没有语言选择，所以理解起来会有一定难度。

### 启动的时候在搜索里面直接找这个启动应用。

![](https://pic3.zhimg.com/v2-caa992a54ab2e290454243669eb59daa_1440w.jpg)

### Project 配置。

最上面一栏是定义工作站的，工作站主要就是用来存放你已经每次配置好的这些配置项的记录，后续可以直接通过这个配置文件打开，就不用再重新配置了。

![](https://pic3.zhimg.com/v2-9ef0de3e04ba0b6eb075aef21f919fbe_1440w.jpg)

### Mode 配置。

![](https://pic2.zhimg.com/v2-b486be695697095680437704f342ca37_1440w.jpg)

### Output 配置。

![](https://pic2.zhimg.com/v2-405a686fb16e7f2c60495c6df4fa6f8d_1440w.jpg)

### Diagrams 配置。

Dot 下面的选项就是让你选择想要生成的一些图，包括类图、依赖关系图、调用和被调用关系图。

![](https://picx.zhimg.com/v2-6dcdab47898fd0ea428a203bc0ad9175_1440w.jpg)

### Expert 中的一些细节配置：

* Project。 **这一栏里面所有的属性，鼠标悬浮在属性 Label 上后，左下角都会有英文注释** 。

![](https://picx.zhimg.com/v2-329bff0c821e79ef15e5ac1fd3a63287_1440w.jpg)

* Build。这一栏比较关键，先放一张图，红色代表与默认值不同了。

![](https://pic3.zhimg.com/v2-79f54bf0ef985d8cc749072dcfa409d0_1440w.jpg)

[EXTRACT_ALL](https://zhida.zhihu.com/search?content_id=250368743&content_type=Article&match_order=1&q=EXTRACT_ALL&zhida_source=entity) 表示：输出所有的函数，但是private和static函数不属于其管制。

[EXTRACT_PRIVATE](https://zhida.zhihu.com/search?content_id=250368743&content_type=Article&match_order=1&q=EXTRACT_PRIVATE&zhida_source=entity) 表示：输出private函数。

[EXTRACT_STATIC](https://zhida.zhihu.com/search?content_id=250368743&content_type=Article&match_order=1&q=EXTRACT_STATIC&zhida_source=entity) 表示：输出static函数。同时还有几个EXTRACT，相应查看文档即可。

[HIDE_UNDOC_MEMBERS](https://zhida.zhihu.com/search?content_id=250368743&content_type=Article&match_order=1&q=HIDE_UNDOC_MEMBERS&zhida_source=entity) 表示：那些没有使用doxygen格式描述的文档（函数或类等）就不显示了。当然，如果EXTRACT_ALL被启用，那么这个标志其实是被忽略的。

[INTERNAL_DOCS](https://zhida.zhihu.com/search?content_id=250368743&content_type=Article&match_order=1&q=INTERNAL_DOCS&zhida_source=entity) 主要指：是否输出注解中的@internal部分。如果没有被启动，那么注解中所有的@internal部分都

将在目标帮助中不可见。

[CASE_SENSE_NAMES](https://zhida.zhihu.com/search?content_id=250368743&content_type=Article&match_order=1&q=CASE_SENSE_NAMES&zhida_source=entity) 表示：是否关注大小写名称，注意，如果开启了，那么所有的名称都将被小写。对于C/C++这种

字母相关的语言来说，建议永远不要开启。

[HIDE_SCOPE_NAMES](https://zhida.zhihu.com/search?content_id=250368743&content_type=Article&match_order=1&q=HIDE_SCOPE_NAMES&zhida_source=entity) 表示：域隐藏，建议永远不要开启。

SHOW_INCLUDE_FILES 表示：是否显示包含文件，如果开启，帮助中会专门生成一个页面，里面包含所有包含文件的列

表。

[INLINE_INFO](https://zhida.zhihu.com/search?content_id=250368743&content_type=Article&match_order=1&q=INLINE_INFO&zhida_source=entity) ：如果开启，那么在帮助文档中，inline函数前面会有一个inline修饰词来标明。

[SORT_MEMBER_DOCS](https://zhida.zhihu.com/search?content_id=250368743&content_type=Article&match_order=1&q=SORT_MEMBER_DOCS&zhida_source=entity) ：如果开启，那么在帮助文档列表显示的时候，函数名称会排序，否则按照解释的顺序显

示。

GENERATE_TODOLIST ：是否生成TODOLIST页面，如果开启，那么包含在@todo注解中的内容将会单独生成并显

示在一个页面中，其他的GENERATE选项同。

SHOW_USED_FILES ：是否在函数或类等的帮助中，最下面显示函数或类的来源文件。

SHOW_FILES ：是否显示文件列表页面，如果开启，那么帮助中会存在一个一个文件列表索引页面。

* Input。

![](https://pic2.zhimg.com/v2-8f09ebcb91e47928a505ae2cd17750bb_1440w.jpg)

* HTML。

![](https://pic2.zhimg.com/v2-17f2c5187755fece37d23ae74e3bea5f_1440w.jpg)

* Dot。

![](https://pic1.zhimg.com/v2-6283db4452935edd6360ee9fd644eed0_1440w.jpg)

Run！

最后在 Run 界面直接 Run doxygen 就好了~
