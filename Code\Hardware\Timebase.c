/*!
 * @file
 * @brief System time base.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stdbool.h>
#include "Timebase.h"

#include "Adpt_GPIO.h"

#include "SystemTimerModule.h"
#include "TestUsart.h"


static uint8_t u8_Millisec;
static uint8_t u8_OneMsecCount;

void ISR_Timer_1ms(void)
{
    u8_Millisec++;
    Execute_SingleDamperDriver();
}

void ISR_Timer_500us(void)
{
    u8_OneMsecCount++;
}

uint8_t Get_SystemMillisec(void)
{
    return u8_Millisec;
}
