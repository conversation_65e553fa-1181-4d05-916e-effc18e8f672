# Gitlab软件项目子模块管理规范

- 1.在工程目录下添加子模块：
  命令如下：<br />
  git submodule add <url> <path> <br />
  其中，url为子模块的路径，path为该子模块存储的目录路径。实例：<br />
   $ git submodule add http://(url) boot_loader <br />
  执行成功后，git status会看到项目中修改了.gitmodules，并增加了一个新文件（为刚刚添加的路径）。 <br />
  查看修改内容可以看到增加了子模块，并且新文件下为子模块的提交hash摘要。<br />
   $ git diff --cached<br />
  提交，完成子模块的添加。<br />
   $ git commit -m "增加子模块"<br />
  
- 2.子模块初始化：<br />
	$ git submodule update --init --recursive <br />
    
- 3.子模块更新：<br />
	cd到目录下，按照软件项目管理规范执行。<br />

- 4.删除子模块较复杂，步骤如下：<br />
    删除子模块目录及源码<br />
	 $ rm -rf 子模块目录<br /> 
    删除项目目录下.gitmodules文件中子模块相关条目<br />
	 $ vi .gitmodules <br />
    删除配置项中子模块相关条目<br />
	 $ vi .git/config <br />
    删除模块下的子模块目录，每个子模块对应一个目录，注意只删除对应的子模块目录即可<br />
	 $ rm .git/module/*  <br />
	执行完成后，再执行添加子模块命令即可，如果仍然报错，执行如下：<br />
     $ git rm --cached 子模块名称<br />
    删除缓存后，提交到仓库即可。<br />
	
