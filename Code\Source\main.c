/*!
 * @file
 * @brief Main program.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Init_Mcu.h"
#include "SingleDamperModule.h"
#include "SystemTimerModule.h"
#include "syslog.h"
#include <stdint.h>

int main(void)
{
    Init_Mcu();
    Init_SingleDamper();

    uint16_t u16_lastSecondCount = Get_SecondCount();

    while(1)
    {
        // 每秒执行一次控制任务
        if(Get_SecondElapsedTime(u16_lastSecondCount) >= 1)
        {
            Execute_SingleDamperControl();
            u16_lastSecondCount = Get_SecondCount();
        }

        // 等待中断，降低功耗
        // __WFI();
    }

    return (1);
}
