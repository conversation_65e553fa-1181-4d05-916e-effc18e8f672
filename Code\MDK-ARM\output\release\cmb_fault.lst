


ARM Macro Assembler    Page 1 


    1 00000000         ;/*
    2 00000000         ; * This file is part of the CmBacktrace Library.
    3 00000000         ; *
    4 00000000         ; * Copyright (c) 2016, Armink, <<EMAIL>>
    5 00000000         ; *
    6 00000000         ; * Permission is hereby granted, free of charge, to any
                        person obtaining
    7 00000000         ; * a copy of this software and associated documentation
                        files (the
    8 00000000         ; * 'Software'), to deal in the Software without restric
                       tion, including
    9 00000000         ; * without limitation the rights to use, copy, modify, 
                       merge, publish,
   10 00000000         ; * distribute, sublicense, and/or sell copies of the So
                       ftware, and to
   11 00000000         ; * permit persons to whom the Software is furnished to 
                       do so, subject to
   12 00000000         ; * the following conditions:
   13 00000000         ; *
   14 00000000         ; * The above copyright notice and this permission notic
                       e shall be
   15 00000000         ; * included in all copies or substantial portions of th
                       e Software.
   16 00000000         ; *
   17 00000000         ; * THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY O
                       F ANY KIND,
   18 00000000         ; * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
                        WARRANTIES OF
   19 00000000         ; * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AN
                       D NONINFRINGEMENT.
   20 00000000         ; * IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS B
                       E LIABLE FOR ANY
   21 00000000         ; * CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACT
                       ION OF CONTRACT,
   22 00000000         ; * TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNEC
                       TION WITH THE
   23 00000000         ; * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWAR
                       E.
   24 00000000         ; *
   25 00000000         ; * Function: Fault handler by MDK-ARM assembly code
   26 00000000         ; * Created on: 2016-12-16
   27 00000000         ; */
   28 00000000         
   29 00000000                 AREA             |.text|, CODE, READONLY, ALIGN=
2
   30 00000000                 THUMB
   31 00000000                 REQUIRE8
   32 00000000                 PRESERVE8
   33 00000000         
   34 00000000         ; NOTE: If use this file's HardFault_Handler, please com
                       ments the HardFault_Handler code on other file.
   35 00000000                 IMPORT           cm_backtrace_fault
   36 00000000                 EXPORT           HardFault_Handler
   37 00000000         
   38 00000000         HardFault_Handler
                               PROC
   39 00000000 4670            MOV              r0, lr      ; get lr
   40 00000002 4669            MOV              r1, sp      ; get stack pointer
                                                             (current is MSP)



ARM Macro Assembler    Page 2 


   41 00000004 F7FF FFFE       BL               cm_backtrace_fault
   42 00000008         
   43 00000008         Fault_Loop
   44 00000008 F7FF FFFE       BL               Fault_Loop  ;while(1)
   45 0000000C                 ENDP
   46 0000000C         
   47 0000000C                 END
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M0+ --depend=.\o
utput\release\cmb_fault.d -o.\output\release\cmb_fault.o -I.\RTE\_MainBoard -Id
:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include -Id:\Keil_v5\Packs\HDSC\HC32
L18x\1.0.0\Device\Include --predefine="__MICROLIB SETA 1" --predefine="__UVISIO
N_VERSION SETA 535" --predefine="_RTE_ SETA 1" --predefine="HC32L186KATH SETA 1
" --predefine="_RTE_ SETA 1" --list=.\output\release\cmb_fault.lst ..\Debug\cm_
backtrace\fault_handler\keil\cmb_fault.S



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 29 in file ..\Debug\cm_backtrace\fault_handler\keil\cmb_fault.S
   Uses
      None
Comment: .text unused
Fault_Loop 00000008

Symbol: Fault_Loop
   Definitions
      At line 43 in file ..\Debug\cm_backtrace\fault_handler\keil\cmb_fault.S
   Uses
      At line 44 in file ..\Debug\cm_backtrace\fault_handler\keil\cmb_fault.S
Comment: Fault_Loop used once
HardFault_Handler 00000000

Symbol: HardFault_Handler
   Definitions
      At line 38 in file ..\Debug\cm_backtrace\fault_handler\keil\cmb_fault.S
   Uses
      At line 36 in file ..\Debug\cm_backtrace\fault_handler\keil\cmb_fault.S
Comment: HardFault_Handler used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

cm_backtrace_fault 00000000

Symbol: cm_backtrace_fault
   Definitions
      At line 35 in file ..\Debug\cm_backtrace\fault_handler\keil\cmb_fault.S
   Uses
      At line 41 in file ..\Debug\cm_backtrace\fault_handler\keil\cmb_fault.S
Comment: cm_backtrace_fault used once
1 symbol
338 symbols in table
