./output/release/miio_uart.o: ..\Source\Iot\miio\uart\miio_uart.c \
  ..\Source\Iot\miio\miio_define.h ..\Source\Iot\arch\arch_define.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdio.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdlib.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\string.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdarg.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stddef.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\ctype.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdint.h \
  ..\Source\Iot\user\user_config.h ..\Source\Iot\miio\uart\miio_uart.h \
  ..\Source\Iot\arch\arch_os.h ..\Source\Iot\arch\arch_uart.h \
  ..\Source\Iot\IotUsr.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdbool.h \
  ..\Device\base_types.h d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\assert.h
