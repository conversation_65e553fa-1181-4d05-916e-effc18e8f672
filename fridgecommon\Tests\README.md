# Tests目录说明

本目录包含fridgecommon项目的单元测试代码，使用CMocka测试框架进行测试。

## 目录结构

```text
Tests/
├── README.md                    # 本说明文件
├── AllTests.c                   # 测试主程序入口
└── StepperMotor/               # 步进电机模块测试
    ├── test_steppermotor.c     # 步进电机测试实现
    └── test_steppermotor.h     # 步进电机测试头文件
```

## 测试框架

### CMocka测试框架

本项目使用CMocka 1.1.7作为单元测试框架，具有以下特点：

- **轻量级**: 专为C语言设计的轻量级测试框架
- **模拟支持**: 强大的Mock功能，支持函数模拟和参数验证
- **内存检测**: 自动检测内存泄漏和缓冲区溢出
- **跨平台**: 支持多种操作系统和编译器

### 头文件包含顺序

**重要**: CMocka要求特定的头文件包含顺序：

```c
// 必须按此顺序包含，这是强制性的
#include <stdarg.h>
#include <stddef.h>
#include <setjmp.h>
#include <stdint.h>
#include <cmocka.h>
```

## 测试模块

### StepperMotor模块测试

#### 测试覆盖范围

| 测试函数 | 测试内容 | 验证点 |
|---------|----------|--------|
| `test_init_stepper_motor` | 步进电机初始化 | 初始状态、参数设置 |
| `test_set_target` | 目标设置功能 | 目标位置、方向设置 |
| `test_pause_resume` | 暂停/恢复功能 | 状态切换、运行控制 |
| `test_stop_action` | 停止功能 | 立即停止、状态重置 |
| `test_get_running_status` | 状态查询 | 运行状态获取 |
| `test_get_steps_info` | 步数信息 | 当前位置、剩余步数 |
| `test_drive_stepper_motor_isr` | 中断驱动 | ISR调用、时序控制 |
| `test_different_driver_types` | 多驱动支持 | 不同芯片类型支持 |

#### Mock函数

##### mock_stepper_motor_io()
模拟步进电机IO控制函数，用于验证IO输出的正确性。

```c
void mock_stepper_motor_io(const uint8_t io_state);
```

**功能**:
- 捕获IO控制调用
- 验证IO状态的正确性
- 支持期望值设置和验证

## 运行测试

### 构建和运行

```bash
# 构建项目和测试
make

# 运行所有测试
make run-tests

# 清理构建文件
make clean
```

### 测试输出示例

```text
[==========] tests: Running 8 test(s).
[ RUN      ] test_init_stepper_motor
[       OK ] test_init_stepper_motor
[ RUN      ] test_set_target
[       OK ] test_set_target
[ RUN      ] test_pause_resume
[       OK ] test_pause_resume
[ RUN      ] test_stop_action
[       OK ] test_stop_action
[ RUN      ] test_get_running_status
[       OK ] test_get_running_status
[ RUN      ] test_get_steps_info
[       OK ] test_get_steps_info
[ RUN      ] test_drive_stepper_motor_isr
[       OK ] test_drive_stepper_motor_isr
[ RUN      ] test_different_driver_types
[       OK ] test_different_driver_types
[==========] tests: 8 test(s) run.
[  PASSED  ] 8 test(s).
```

## 测试编写指南

### 基本测试结构

```c
void test_function_name(void **state) {
    // 1. 准备测试数据
    StepperMotorConstPara_st config = {
        .u16_StepperMotorStartDelayTimeMs = 100,
        .u8_DriverTypes = U8_DRIVER_TYPES_LB1205M_4BEATS
    };
    
    // 2. 设置Mock期望
    expect_value(mock_stepper_motor_io, io_state, expected_value);
    
    // 3. 执行被测试函数
    InitStepperMotor();
    SetStepperMotorConstPara(&config);
    
    // 4. 验证结果
    assert_int_equal(GetStepperMotorRunningStatus(), EXPECTED_STATUS);
}
```

### 常用断言宏

#### 数值比较
```c
assert_int_equal(actual, expected);      // 整数相等
assert_int_not_equal(actual, expected);  // 整数不等
assert_true(condition);                  // 条件为真
assert_false(condition);                 // 条件为假
```

#### 指针检查
```c
assert_null(pointer);                    // 指针为空
assert_non_null(pointer);                // 指针非空
assert_ptr_equal(ptr1, ptr2);            // 指针相等
```

#### 字符串比较
```c
assert_string_equal(str1, str2);         // 字符串相等
assert_string_not_equal(str1, str2);     // 字符串不等
```

### Mock使用指南

#### 设置期望值
```c
// 期望函数被调用，并验证参数
expect_value(mock_function, parameter_name, expected_value);

// 设置函数返回值
will_return(mock_function, return_value);
```

#### 验证调用
```c
// 在被测试函数中使用mock
uint8_t result = mock();  // 获取预设的返回值
check_expected(parameter); // 验证参数是否符合期望
```

## 测试最佳实践

### 1. 测试独立性
- 每个测试函数应该独立，不依赖其他测试的结果
- 在测试开始时初始化所需的状态
- 在测试结束时清理资源

### 2. 边界条件测试
- 测试正常情况、边界值和异常情况
- 包括最小值、最大值、零值等边界条件
- 测试错误输入的处理

### 3. Mock使用原则
- 只Mock外部依赖，不Mock被测试的模块
- 确保Mock行为与真实实现一致
- 验证Mock函数的调用次数和参数

### 4. 测试命名
- 使用描述性的测试函数名
- 测试名应该清楚表达测试的内容
- 遵循 `test_<module>_<function>_<scenario>` 的命名模式

## 持续集成

### 自动化测试
测试可以集成到CI/CD流水线中：

```bash
#!/bin/bash
# CI脚本示例
make clean
make run-tests
if [ $? -eq 0 ]; then
    echo "All tests passed!"
else
    echo "Tests failed!"
    exit 1
fi
```

### 代码覆盖率
可以使用gcov等工具生成代码覆盖率报告：

```bash
# 编译时添加覆盖率选项
gcc -fprofile-arcs -ftest-coverage ...

# 运行测试后生成报告
gcov *.gcno
```

## 故障排除

### 常见问题

1. **编译错误**: 检查头文件包含顺序
2. **链接错误**: 确保CMocka库正确链接
3. **测试失败**: 检查Mock设置和断言条件
4. **内存错误**: 使用valgrind等工具检测内存问题

### 调试技巧

```c
// 在测试中添加调试输出
printf("Debug: current status = %d\n", status);

// 使用CMocka的打印功能
print_message("Test checkpoint reached\n");
```

## 扩展测试

### 添加新测试

1. 在相应的测试文件中添加测试函数
2. 在`AllTests.c`中注册新的测试函数
3. 更新测试文档

### 性能测试
可以添加性能测试来验证关键函数的执行时间：

```c
void test_performance(void **state) {
    clock_t start = clock();
    
    // 执行被测试函数
    DriveStepperMotorISR();
    
    clock_t end = clock();
    double time_spent = ((double)(end - start)) / CLOCKS_PER_SEC;
    
    // 验证执行时间在合理范围内
    assert_true(time_spent < 0.001); // 小于1ms
}
```
