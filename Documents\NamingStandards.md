### 1  通用命名规则

1. **标识符的命名要清晰、明确，同时使用完整的单词或大家基本可以理解的缩写，避免使人产生误解。**

说明：尽可能给出描述性名称，不要节约空间，让别人很快理解你的代码更重要。

示例：好的命名：

```C++
interror_number;
intnumber_of_completed_connection; 
```

不好的命名：使用模糊的缩写或随意的字符：

```C++
intn;
intnerr;
intn_comp_conns;
```

2. **除了常见的通用缩写以外，不使用单词缩写，不得使用汉语拼音。  **

说明：较短的单词可通过去掉 “元音” 形成缩写，较长的单词可取单词的头几个字母形成缩写，一些单词有大家公认的缩写，常用单词的缩写必须统一。协议中的单词的缩写与协议保持一致。对于某个系统使用的专用缩写应该在注释或者某处做统一说明。

示例：一些常见可以缩写的例子：

```Bash
argument 可缩写为 arg
buffer 可缩写为 buff 
clock 可缩写为 clk 
command 可缩写为 cmd 
compare 可缩写为 cmp
configuration 可缩写为 cfg 
device 可缩写为 dev
error 可缩写为 err
Hexadecimal 可缩写为 hex 
increment 可缩写为 inc、 
initialize 可缩写为 init 
maximum 可缩写为 max 
message 可缩写为 msg
minimum 可缩写为 min 
parameter 可缩写为 para 
previous 可缩写为 prev
register 可缩写为 reg 
semaphore 可缩写为 sem 
statistic 可缩写为 stat
synchronize 可缩写为 sync 
temp 可缩写为 tmp
```

3. ** 基本数据类型前缀**

* 无符号整型：
  * `u8_` - 8位无符号整数 (uint8_t)
  * `u16_` - 16位无符号整数 (uint16_t)
  * `u32_` - 32位无符号整数 (uint32_t)
* 有符号整型：
  * `s8_` - 8位有符号整数 (int8_t)
  * `s16_` - 16位有符号整数 (int16_t)
  * `s32_` - 32位有符号整数 (int32_t)
* 浮点型：
  * `f_` - 浮点数 (float)
  * `d_` - 双精度浮点数 (double)
* 布尔型：
  * `b_` - 布尔类型 (bool)

4. **复杂数据类型前缀**

* 结构体变量：
  * `st_` - 结构体实例
  * `p_` - 指针
* 枚举变量：
  * `em_` - 枚举类型变量
* 数组：
  * `ary_` - 数组

5. **常量和宏定义**

* 常量前缀：
  * 数据类型大写前缀：`U8_`、`U16_`、`S16_`、`FLOAT_`
  * 全大写，下划线分隔单词

6. **特殊常量命名**

* 温度常量：

`CON_8P0_DEGREE`(表示8.0度)

* 时间常量：

`U16_DEFROST_HEATER_MAX_ON_TIME_SECONDS` (表示最大加热时间，单位秒)

7. **结构体类型**

* 后缀 `_st`

`Defrosting_st`

`DefrostCondition_st`

8. **枚举类型**

* 后缀 ` _t`

` DefrostMode_t`

` RunningState_t`

* 后缀 `_em`：`Fan_em`
* 后缀 `_e`：`fireware_func_e`

9. **枚举值命名**

* 前缀 `e`：

`eDefrostMode_Defrosting`

`eEnterState_Normal`

* 全大写：`FAULT_REF_SNR`、`DEVICE_FrzFan`

10. **用正确的反义词组命名具有互斥意义的变量或相反动作的函数等。 **

示例：

```Bash
add/remove                  begin/end                    create/destroy
insert/delete                 first/last                       get/release
increment/decrement             put/get                        add/delete
lock/unlock                   open/close                   min/max
old/new                        start/stop                    next/previous
source/target                show/hide                    send/receive        
source/destination         copy/paste                   up/down
```

11. **尽量避免名字中出现数字编号，除非逻辑上的确需要编号。 **

示例：如下命名，使人产生疑惑。

```C++
#define EXAMPLE_0_TEST_
#define EXAMPLE_1_TEST_
```

应改为有意义的单词命名

```C++
#defineEXAMPLE_UNIT_TEST_
#defineEXAMPLE_ASSERT_TEST_
```

12. **标识符前不应添加模块、项目、产品、部门的名称作为前缀。 **

说明：很多已有代码中已经习惯在文件名中增加模块名，这种写法类似匈牙利命名法，导致文件名不可读，并且带来带来如下问题：

• 第一眼看到的是模块名，而不是真正的文件功能，阻碍阅读；

• 文件名太长；

• 文件名和模块绑定，不利于维护和移植。若foo. c进行重构后，从a模块挪到b模块，若foo. c中有模块名，则需要将文件名从a_module_foo. c改为 b_module_foo. c

13. **平台／** **驱动****等适配代码的标识符命名风格保持和平台／驱动一致。 **

说明：涉及到外购芯片以及配套的驱动，这部分的代码变动（包括为产品做适配的新增代码），应该保持原有的风格。

14. **重构／修改部分代码时，应保持和原有代码的命名风格一致。 **

说明：根据源代码现有的风格继续编写代码，有利于保持总体一致。

### 2  文件命名规则

1. 驱动层：`Driver_*.c/h`
   1. 例如：` Driver_Fan.c Driver_AdSample.c`
2. 适配层：`Adpt_*.c/h`
   1. 例如：` Adpt_PWM.cAdpt_UART.c`
3. 核心层：`Core_*.c/h`
   1. 例如：`Core_CallBackTimer.c Core_Types.h`
4. 应用层：直接使用功能名
   1. 例如：` Defrosting.c CoolingCycle.c FridgeRunner.c`
5. 参数配置：`Parameter_*.c/h`
   1. 例如：`Parameter_TemperatureZone.h Parameter_Device.h`
6. 接口文件：`*Interface.h`
   1. 例如：`DisplayInterface.h`

### 3 变量命名规则

1. **规则1 全局变量应增加“g_”前缀。 **
2. **规则2 静态变量应增加“s_”前缀。**

说明：增加g_前缀或者s_前缀，原因如下：

首先，全局变量十分危险，通过前缀使得全局变量更加醒目，促使开发人员对这些变量的使用更加小心。

其次，从根本上说，应当尽量不使用全局变量，增加g_和s_前缀，会使得全局变量的名字显得很丑陋，从而促使开发人员尽量少使用全局变量 。

3. **禁止使用单字节命名变量，但允许定义i、j、k作为局部循环变量。 **
4. **使用名词或者形容词＋名词方式命名变量。**

### 4 函数命名规则

1. **函数命名应以函数要执行的动作命名，一般采用动词或者动词＋名词的结构。 **

示例：找到当前进程的当前目录

DWORD GetCurrentDirectory ( DWORD BufferLength, LPTSTR Buffer ) ;

2. **函数指针除了前缀，其他按照函数的命名规则命名。**
3. **模块_动作**
   1. `Core_CallbackTimer_Update`
   2. `AdSample_Handle`
4. **动作_对象：**
   1. `Get_SensorValue`
   2. `Set_FanSpeed`
   3. `Update_TempParameter`
5. **状态_动作：**
   1. ` DefrostingState_Defrosting`
   2. `CoolingCycleState_CompOn`
6. **函数动词前缀**

* Get_：获取值
* Set_：设置值
* Update_：更新状态
* Init_：初始化
* Clear_：清除状态
* Handle_：处理事件
* Judge_：判断条件
* Ctrl_：控制设备
* Process_：处理流程
* Vote_：投票决策

7. **静态函数**

* 通常使用 `static` 关键字定义
* 遵循与公共函数相同的命名规则

### 5 宏的命名规则

1. **对于数值或者字符串等等常量的定义，建议采用全大写字母，单词之间加下划线"_"的方式命名（枚举同样建议使用此方式定义）。**

示例：

```C++
#definePI_ROUNDED 3.14
```

2. **除了头文件或编译开关等特殊标识定义，宏定义不能使用下划线"_"开头和结尾。 **

说明：一般来说，"_"开头、结尾的宏都是一些内部的定义，以免产生混淆
