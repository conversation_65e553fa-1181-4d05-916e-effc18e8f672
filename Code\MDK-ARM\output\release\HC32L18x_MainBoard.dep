Dependencies for Project 'HC32L18x', Target 'MainBoard': (DO NOT MODIFY !)
CompilerVersion: 6160000::V6.16::ARMCLANG
F (..\Bsp\Adpt_ADC.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/adpt_adc.o -MD)
I (..\Bsp\Adpt_ADC.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\PeriphDrivers\adc.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
I (..\PeriphDrivers\gpio.h)(0x68620734)
I (..\PeriphDrivers\bgr.h)(0x68620734)
F (..\Bsp\Adpt_Clock.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/adpt_clock.o -MD)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
I (..\PeriphDrivers\flash.h)(0x68620734)
F (..\Bsp\Adpt_GPIO.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/adpt_gpio.o -MD)
I (..\Bsp\Adpt_GPIO.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\PeriphDrivers\gpio.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\Bsp\Adpt_Iwdg.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/adpt_iwdg.o -MD)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (..\PeriphDrivers\wdt.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
I (..\PeriphDrivers\lpm.h)(0x68620734)
I (..\PeriphDrivers\reset.h)(0x68620734)
I (..\PeriphDrivers\wwdt.h)(0x68620734)
I (..\Bsp\Adpt_Iwdg.h)(0x68620734)
I (..\Debug\syslog.h)(0x68620734)
F (..\Bsp\Adpt_PWM.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/adpt_pwm.o -MD)
I (..\Bsp\Adpt_PWM.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\PeriphDrivers\adt.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
I (..\PeriphDrivers\pca.h)(0x68620734)
I (..\PeriphDrivers\bt.h)(0x68620734)
I (..\PeriphDrivers\gpio.h)(0x68620734)
I (..\PeriphDrivers\timer3.h)(0x68620734)
F (..\Bsp\Adpt_TimeBase.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/adpt_timebase.o -MD)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
I (..\PeriphDrivers\bt.h)(0x68620734)
I (..\PeriphDrivers\gpio.h)(0x68620734)
I (..\Hardware\Timebase.h)(0x68620734)
F (..\Bsp\Adpt_ADC.h)(0x68620734)()
F (..\Bsp\Adpt_Clock.h)(0x68620734)()
F (..\Bsp\Adpt_GPIO.h)(0x68620734)()
F (..\Bsp\Adpt_Iwdg.h)(0x68620734)()
F (..\Bsp\Adpt_PWM.h)(0x68620734)()
F (..\Bsp\Adpt_TimeBase.h)(0x68620734)()
F (..\Bsp\Adpt_Reset.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/adpt_reset.o -MD)
I (..\Bsp\Adpt_Reset.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\PeriphDrivers\reset.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\Bsp\Adpt_Reset.h)(0x68620734)()
F (..\Bsp\Adpt_Flash.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/adpt_flash.o -MD)
I (..\Bsp\Adpt_Flash.h)(0x68620734)
I (..\PeriphDrivers\flash.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\Bsp\Adpt_Flash.h)(0x68620734)()
F (..\Device\base_types.h)(0x68620734)()
F (..\Device\board_stkhc32l186.h)(0x68620734)()
F (..\Device\HC32L186.h)(0x68620734)()
F (..\Device\RTE_Components.h)(0x68620734)()
F (..\Device\system_hc32l186.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/system_hc32l186.o -MD)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\HC32L186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\Device\system_hc32l186.h)(0x68620734)()
F (..\Device\interrupts_hc32l186.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/interrupts_hc32l186.o -MD)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\Device\interrupts_hc32l186.h)(0x68620734)()
F (..\Hardware\Init_Mcu.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/init_mcu.o -MD)
I (..\Bsp\Adpt_Clock.h)(0x68620734)
I (..\Bsp\Adpt_GPIO.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\PeriphDrivers\gpio.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
I (..\Bsp\Adpt_Timebase.h)(0x68620734)
I (..\Bsp\Adpt_Iwdg.h)(0x68620734)
I (..\Bsp\Adpt_PWM.h)(0x68620734)
I (..\PeriphDrivers\adt.h)(0x68620734)
I (..\PeriphDrivers\pca.h)(0x68620734)
I (..\PeriphDrivers\bt.h)(0x68620734)
I (..\PeriphDrivers\timer3.h)(0x68620734)
I (..\Bsp\Adpt_ADC.h)(0x68620734)
I (..\PeriphDrivers\adc.h)(0x68620734)
I (..\Bsp\Adpt_Usart.h)(0x68620734)
I (..\PeriphDrivers\lpuart.h)(0x68620734)
I (..\PeriphDrivers\uart.h)(0x68620734)
I (..\Bsp\Adpt_Reset.h)(0x68620734)
I (..\Bsp\Adpt_Flash.h)(0x68620734)
I (..\PeriphDrivers\flash.h)(0x68620734)
F (..\Hardware\Init_Mcu.h)(0x68620734)()
F (..\Hardware\Timebase.c)(0x68622CD6)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/timebase.o -MD)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x603507CA)
I (..\Hardware\Timebase.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Bsp\Adpt_GPIO.h)(0x68620734)
I (..\PeriphDrivers\gpio.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
I (..\Source\Module\SystemTimerModule.h)(0x68620734)
I (..\Hardware\TestUsart.h)(0x68620734)
F (..\Hardware\Timebase.h)(0x68620734)()
F (..\PeriphDrivers\adc.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/adc.o -MD)
I (..\PeriphDrivers\adc.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\adc.h)(0x68620734)()
F (..\PeriphDrivers\adt.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/adt.o -MD)
I (..\PeriphDrivers\adt.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\adt.h)(0x68620734)()
F (..\PeriphDrivers\aes.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/aes.o -MD)
I (..\PeriphDrivers\aes.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\aes.h)(0x68620734)()
F (..\PeriphDrivers\bgr.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/bgr.o -MD)
I (..\PeriphDrivers\bgr.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\bgr.h)(0x68620734)()
F (..\PeriphDrivers\bt.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/bt.o -MD)
I (..\PeriphDrivers\bt.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\bt.h)(0x68620734)()
F (..\PeriphDrivers\crc.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/crc.o -MD)
I (..\PeriphDrivers\crc.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\crc.h)(0x68620734)()
F (..\PeriphDrivers\ctrim.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/ctrim.o -MD)
I (..\PeriphDrivers\ctrim.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\ctrim.h)(0x68620734)()
F (..\PeriphDrivers\dac.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/dac.o -MD)
I (..\PeriphDrivers\dac.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\dac.h)(0x68620734)()
F (..\PeriphDrivers\ddl.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/ddl.o -MD)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\ddl.h)(0x68620734)()
F (..\PeriphDrivers\debug.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/debug.o -MD)
I (..\PeriphDrivers\debug.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\debug.h)(0x68620734)()
F (..\PeriphDrivers\dmac.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/dmac.o -MD)
I (..\PeriphDrivers\dmac.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\dmac.h)(0x68620734)()
F (..\PeriphDrivers\flash.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/flash.o -MD)
I (..\PeriphDrivers\flash.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\flash.h)(0x68620734)()
F (..\PeriphDrivers\gpio.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/gpio.o -MD)
I (..\PeriphDrivers\gpio.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\gpio.h)(0x68620734)()
F (..\PeriphDrivers\i2c.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/i2c.o -MD)
I (..\PeriphDrivers\i2c.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\i2c.h)(0x68620734)()
F (..\PeriphDrivers\lcd.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/lcd.o -MD)
I (..\PeriphDrivers\lcd.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\lcd.h)(0x68620734)()
F (..\PeriphDrivers\lpm.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/lpm.o -MD)
I (..\PeriphDrivers\lpm.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\lpm.h)(0x68620734)()
F (..\PeriphDrivers\lptim.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/lptim.o -MD)
I (..\PeriphDrivers\lptim.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\lptim.h)(0x68620734)()
F (..\PeriphDrivers\lpuart.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/lpuart.o -MD)
I (..\PeriphDrivers\lpuart.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\lpuart.h)(0x68620734)()
F (..\PeriphDrivers\lvd.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/lvd.o -MD)
I (..\PeriphDrivers\lvd.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\lvd.h)(0x68620734)()
F (..\PeriphDrivers\opa.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/opa.o -MD)
I (..\PeriphDrivers\opa.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\opa.h)(0x68620734)()
F (..\PeriphDrivers\pca.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/pca.o -MD)
I (..\PeriphDrivers\pca.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\pca.h)(0x68620734)()
F (..\PeriphDrivers\pcnt.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/pcnt.o -MD)
I (..\PeriphDrivers\pcnt.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\pcnt.h)(0x68620734)()
F (..\PeriphDrivers\ram.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/ram.o -MD)
I (..\PeriphDrivers\ram.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\ram.h)(0x68620734)()
F (..\PeriphDrivers\reset.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/reset.o -MD)
I (..\PeriphDrivers\reset.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\reset.h)(0x68620734)()
F (..\PeriphDrivers\rtc.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/rtc.o -MD)
I (..\PeriphDrivers\rtc.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\rtc.h)(0x68620734)()
F (..\PeriphDrivers\spi.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/spi.o -MD)
I (..\PeriphDrivers\spi.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\spi.h)(0x68620734)()
F (..\PeriphDrivers\sysctrl.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/sysctrl.o -MD)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\sysctrl.h)(0x68620734)()
F (..\PeriphDrivers\timer3.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/timer3.o -MD)
I (..\PeriphDrivers\timer3.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\timer3.h)(0x68620734)()
F (..\PeriphDrivers\trim.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/trim.o -MD)
I (..\PeriphDrivers\trim.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\trim.h)(0x68620734)()
F (..\PeriphDrivers\trng.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/trng.o -MD)
I (..\PeriphDrivers\trng.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\trng.h)(0x68620734)()
F (..\PeriphDrivers\uart.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/uart.o -MD)
I (..\PeriphDrivers\uart.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\uart.h)(0x68620734)()
F (..\PeriphDrivers\vc.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/vc.o -MD)
I (..\PeriphDrivers\vc.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\vc.h)(0x68620734)()
F (..\PeriphDrivers\wdt.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/wdt.o -MD)
I (..\PeriphDrivers\wdt.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\wdt.h)(0x68620734)()
F (..\PeriphDrivers\wwdt.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/wwdt.o -MD)
I (..\PeriphDrivers\wwdt.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\PeriphDrivers\wwdt.h)(0x68620734)()
F (..\Source\Module\SingleDamperModule.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/singledampermodule.o -MD)
I (..\Source\Module\SystemTimerModule.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x603507CA)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Source\Module\SingleDamperModule.h)(0x686234AB)
I (..\..\fridgecommon\Code\StepperMotor\StepperMotorLib.h)(0x6861F292)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\PeriphDrivers\gpio.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
F (..\Source\Module\SystemTimerModule.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/systemtimermodule.o -MD)
I (..\Source\Module\SystemTimerModule.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x603507CA)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Source\Module\Core_Types.h)(0x68620734)
F (..\Source\main.c)(0x686234CC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/main.o -MD)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Hardware\Init_Mcu.h)(0x68620734)
I (..\Source\Module\SingleDamperModule.h)(0x686234AB)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x603507CA)
I (..\Source\Module\SystemTimerModule.h)(0x68620734)
I (..\Debug\syslog.h)(0x68620734)
F (..\Startup\startup_hc32l186.s)(0x68620734)(--cpu Cortex-M0+ -g --pd "__MICROLIB SETA 1"

-I.\RTE\_MainBoard

-Id:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-Id:\Keil_v5\Packs\HDSC\HC32L18x\1.0.0\Device\Include

--pd "__UVISION_VERSION SETA 535"

--pd "_RTE_ SETA 1"

--pd "HC32L186KATH SETA 1"

--pd "_RTE_ SETA 1"

--list .\output\release\startup_hc32l186.lst

--xref -o .\output\release\startup_hc32l186.o

--depend .\output\release\startup_hc32l186.d)
F (..\Debug\ramlog.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/ramlog.o -MD)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (..\Debug\cm_backtrace\cm_backtrace.h)(0x68620734)
I (..\Debug\cm_backtrace\cmb_def.h)(0x68620734)
I (..\Debug\cm_backtrace\cmb_cfg.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (..\Debug\syslog.h)(0x68620734)
I (..\Bsp\Adpt_Usart.h)(0x68620734)
I (..\PeriphDrivers\lpuart.h)(0x68620734)
I (..\PeriphDrivers\ddl.h)(0x68620734)
I (..\Device\base_types.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
I (..\Device\board_stkhc32l186.h)(0x68620734)
I (..\Device\hc32l186.h)(0x68620734)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\core_cm0plus.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (d:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armclang.h)(0x60D3E8E2)
I (..\Device\system_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\sysctrl.h)(0x68620734)
I (..\Device\interrupts_hc32l186.h)(0x68620734)
I (..\PeriphDrivers\ddl_device.h)(0x68620734)
I (..\PeriphDrivers\uart.h)(0x68620734)
F (..\Debug\syslog.h)(0x68620734)()
F (..\Debug\cm_backtrace\cm_backtrace.c)(0x68620734)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/cm_backtrace.o -MD)
I (..\Debug\cm_backtrace\cm_backtrace.h)(0x68620734)
I (..\Debug\cm_backtrace\cmb_def.h)(0x68620734)
I (..\Debug\cm_backtrace\cmb_cfg.h)(0x68620734)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdlib.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x603507CA)
I (d:\Keil_v5\ARM\ARMCLANG\include\string.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdio.h)(0x603507D4)
I (..\Debug\syslog.h)(0x68620734)
I (..\Debug\cm_backtrace\Languages\en-US\cmb_en_US.h)(0x68620734)
F (..\Debug\cm_backtrace\cm_backtrace.h)(0x68620734)()
F (..\Debug\cm_backtrace\cmb_cfg.h)(0x68620734)()
F (..\Debug\cm_backtrace\cmb_def.h)(0x68620734)()
F (..\Debug\cm_backtrace\fault_handler\keil\cmb_fault.S)(0x68620734)(--cpu Cortex-M0+ -g --pd "__MICROLIB SETA 1"

-I.\RTE\_MainBoard

-Id:\Keil_v5\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-Id:\Keil_v5\Packs\HDSC\HC32L18x\1.0.0\Device\Include

--pd "__UVISION_VERSION SETA 535"

--pd "_RTE_ SETA 1"

--pd "HC32L186KATH SETA 1"

--pd "_RTE_ SETA 1"

--list .\output\release\cmb_fault.lst

--xref -o .\output\release\cmb_fault.o

--depend .\output\release\cmb_fault.d)
F (..\..\fridgecommon\Code\StepperMotor\StepperMotorLib.c)(0x68623257)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char

-D__MICROLIB -gdwarf-4 -O1 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Bsp -I ../Device -I ../Hardware -I ../PeriphDrivers -I ../Source/Application -I ../Source/Core -I ../Source/CoreUser -I ../Source/Module -I ../Source -I ../Startup -I ../Device -I ../Debug -I ../Debug/cm_backtrace -I ../Source/Iot -I ../Source/Iot/arch -I ../Source/Iot/miio -I ../Source/Iot/spec -I ../Source/Iot/user -I ../Source/Iot/miio/device/codec -I ../Source/Iot/miio/uart -I ../Source/Iot/miio/util -I ../Source/Iot/user/app -I ../Parameter -I ../../fridgecommon/Code/StepperMotor

-I./RTE/_MainBoard

-Id:/Keil_v5/Packs/ARM/CMSIS/5.8.0/CMSIS/Core/Include

-Id:/Keil_v5/Packs/HDSC/HC32L18x/1.0.0/Device/Include

-D__UVISION_VERSION="535" -D_RTE_ -DHC32L186KATH -D_RTE_

-o ./output/release/steppermotorlib.o -MD)
I (..\..\fridgecommon\Code\StepperMotor\StepperMotorLib.h)(0x6861F292)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdbool.h)(0x603507CA)
I (d:\Keil_v5\ARM\ARMCLANG\include\stdint.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\stddef.h)(0x603507D0)
I (d:\Keil_v5\ARM\ARMCLANG\include\assert.h)(0x603507D0)
