./output/release/iot_spec.o: ..\Source\Iot\spec\Iot_Spec.c \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\string.h \
  ..\Source\Iot\arch\arch_os.h ..\Source\Iot\spec\Iot_Spec.h \
  ..\Source\Iot\IotUsr.h d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdint.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdbool.h \
  ..\Device\base_types.h d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stdio.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\stddef.h \
  d:\Keil_v5\ARM\ARMCLANG\Bin\..\include\assert.h
