Component: ARM Compiler 6.16 Tool: armlink [5dfeaa00]

==============================================================================

Section Cross References

    adpt_adc.o(.text.App_AdcInit) refers to ddl.o(.text.ddl_memclr) for ddl_memclr
    adpt_adc.o(.text.App_AdcInit) refers to sysctrl.o(.text.Sysctrl_SetPeripheralGate) for Sysctrl_SetPeripheralGate
    adpt_adc.o(.text.App_AdcInit) refers to bgr.o(.text.Bgr_BgrEnable) for Bgr_BgrEnable
    adpt_adc.o(.text.App_AdcInit) refers to adc.o(.text.Adc_Init) for Adc_Init
    adpt_adc.o(.ARM.exidx.text.App_AdcInit) refers to adpt_adc.o(.text.App_AdcInit) for [Anonymous Symbol]
    adpt_adc.o(.text.App_AdcSQRCfg) refers to ddl.o(.text.ddl_memclr) for ddl_memclr
    adpt_adc.o(.text.App_AdcSQRCfg) refers to adc.o(.text.Adc_SqrModeCfg) for Adc_SqrModeCfg
    adpt_adc.o(.text.App_AdcSQRCfg) refers to adc.o(.text.Adc_CfgSqrChannel) for Adc_CfgSqrChannel
    adpt_adc.o(.text.App_AdcSQRCfg) refers to adc.o(.text.Adc_EnableIrq) for Adc_EnableIrq
    adpt_adc.o(.text.App_AdcSQRCfg) refers to interrupts_hc32l186.o(.text.EnableNvic) for EnableNvic
    adpt_adc.o(.text.App_AdcSQRCfg) refers to adc.o(.text.Adc_SQR_Start) for Adc_SQR_Start
    adpt_adc.o(.ARM.exidx.text.App_AdcSQRCfg) refers to adpt_adc.o(.text.App_AdcSQRCfg) for [Anonymous Symbol]
    adpt_adc.o(.text.Adc_IRQHandler) refers to adc.o(.text.Adc_GetIrqStatus) for Adc_GetIrqStatus
    adpt_adc.o(.text.Adc_IRQHandler) refers to adc.o(.text.Adc_ClrIrqStatus) for Adc_ClrIrqStatus
    adpt_adc.o(.text.Adc_IRQHandler) refers to adc.o(.text.Adc_GetSqrResult) for Adc_GetSqrResult
    adpt_adc.o(.text.Adc_IRQHandler) refers to adc.o(.text.Adc_SQR_Stop) for Adc_SQR_Stop
    adpt_adc.o(.text.Adc_IRQHandler) refers to adpt_adc.o(.bss.u32AdcRestult) for u32AdcRestult
    adpt_adc.o(.ARM.exidx.text.Adc_IRQHandler) refers to adpt_adc.o(.text.Adc_IRQHandler) for [Anonymous Symbol]
    adpt_adc.o(.text.Board_InitAdc) refers to adpt_adc.o(.text.App_AdcInit) for App_AdcInit
    adpt_adc.o(.text.Board_InitAdc) refers to adpt_adc.o(.text.App_AdcSQRCfg) for App_AdcSQRCfg
    adpt_adc.o(.ARM.exidx.text.Board_InitAdc) refers to adpt_adc.o(.text.Board_InitAdc) for [Anonymous Symbol]
    adpt_adc.o(.text.ADC_Restart) refers to adc.o(.text.Adc_SQR_Start) for Adc_SQR_Start
    adpt_adc.o(.ARM.exidx.text.ADC_Restart) refers to adpt_adc.o(.text.ADC_Restart) for [Anonymous Symbol]
    adpt_adc.o(.text.ADC_GetResult) refers to adpt_adc.o(.bss.u32AdcRestult) for u32AdcRestult
    adpt_adc.o(.ARM.exidx.text.ADC_GetResult) refers to adpt_adc.o(.text.ADC_GetResult) for [Anonymous Symbol]
    adpt_clock.o(.text.App_SystemClkInit_RC48M) refers to flash.o(.text.Flash_WaitCycle) for Flash_WaitCycle
    adpt_clock.o(.text.App_SystemClkInit_RC48M) refers to sysctrl.o(.text.Sysctrl_SetRC48MTrim) for Sysctrl_SetRC48MTrim
    adpt_clock.o(.text.App_SystemClkInit_RC48M) refers to sysctrl.o(.text.Sysctrl_ClkSourceEnable) for Sysctrl_ClkSourceEnable
    adpt_clock.o(.text.App_SystemClkInit_RC48M) refers to sysctrl.o(.text.Sysctrl_SysClkSwitch) for Sysctrl_SysClkSwitch
    adpt_clock.o(.ARM.exidx.text.App_SystemClkInit_RC48M) refers to adpt_clock.o(.text.App_SystemClkInit_RC48M) for [Anonymous Symbol]
    adpt_clock.o(.text.Board_InitClock) refers to adpt_clock.o(.text.App_SystemClkInit_RC48M) for App_SystemClkInit_RC48M
    adpt_clock.o(.ARM.exidx.text.Board_InitClock) refers to adpt_clock.o(.text.Board_InitClock) for [Anonymous Symbol]
    adpt_gpio.o(.text.Board_InitPins) refers to sysctrl.o(.text.Sysctrl_SetPeripheralGate) for Sysctrl_SetPeripheralGate
    adpt_gpio.o(.text.Board_InitPins) refers to gpio.o(.text.Gpio_Init) for Gpio_Init
    adpt_gpio.o(.text.Board_InitPins) refers to gpio.o(.text.Gpio_SetAnalogMode) for Gpio_SetAnalogMode
    adpt_gpio.o(.text.Board_InitPins) refers to gpio.o(.text.Gpio_SetAfMode) for Gpio_SetAfMode
    adpt_gpio.o(.text.Board_InitPins) refers to adpt_gpio.o(.bss.stcGpioCfg) for stcGpioCfg
    adpt_gpio.o(.ARM.exidx.text.Board_InitPins) refers to adpt_gpio.o(.text.Board_InitPins) for [Anonymous Symbol]
    adpt_gpio.o(.text.Board_InitSwdGpio) refers to sysctrl.o(.text.Sysctrl_SetFunc) for Sysctrl_SetFunc
    adpt_gpio.o(.text.Board_InitSwdGpio) refers to gpio.o(.text.Gpio_Init) for Gpio_Init
    adpt_gpio.o(.text.Board_InitSwdGpio) refers to gpio.o(.text.Gpio_SetAfMode) for Gpio_SetAfMode
    adpt_gpio.o(.text.Board_InitSwdGpio) refers to adpt_gpio.o(.bss.stcGpioCfg) for stcGpioCfg
    adpt_gpio.o(.ARM.exidx.text.Board_InitSwdGpio) refers to adpt_gpio.o(.text.Board_InitSwdGpio) for [Anonymous Symbol]
    adpt_iwdg.o(.text.Wdt_IRQHandler) refers to cmb_fault.o(.text) for HardFault_Handler
    adpt_iwdg.o(.ARM.exidx.text.Wdt_IRQHandler) refers to adpt_iwdg.o(.text.Wdt_IRQHandler) for [Anonymous Symbol]
    adpt_iwdg.o(.text.Board_InitIwdg) refers to adpt_iwdg.o(.text.App_WdtInit) for App_WdtInit
    adpt_iwdg.o(.text.Board_InitIwdg) refers to wdt.o(.text.Wdt_Start) for Wdt_Start
    adpt_iwdg.o(.text.Board_InitIwdg) refers to reset.o(.text.Reset_GetFlag) for Reset_GetFlag
    adpt_iwdg.o(.text.Board_InitIwdg) refers to reset.o(.text.Reset_ClearFlag) for Reset_ClearFlag
    adpt_iwdg.o(.ARM.exidx.text.Board_InitIwdg) refers to adpt_iwdg.o(.text.Board_InitIwdg) for [Anonymous Symbol]
    adpt_iwdg.o(.text.App_WdtInit) refers to sysctrl.o(.text.Sysctrl_SetPeripheralGate) for Sysctrl_SetPeripheralGate
    adpt_iwdg.o(.text.App_WdtInit) refers to wdt.o(.text.Wdt_Init) for Wdt_Init
    adpt_iwdg.o(.text.App_WdtInit) refers to wdt.o(.text.Wdt_IrqClr) for Wdt_IrqClr
    adpt_iwdg.o(.text.App_WdtInit) refers to interrupts_hc32l186.o(.text.EnableNvic) for EnableNvic
    adpt_iwdg.o(.ARM.exidx.text.App_WdtInit) refers to adpt_iwdg.o(.text.App_WdtInit) for [Anonymous Symbol]
    adpt_iwdg.o(.text.IWDG_Refesh) refers to wdt.o(.text.Wdt_Feed) for Wdt_Feed
    adpt_iwdg.o(.ARM.exidx.text.IWDG_Refesh) refers to adpt_iwdg.o(.text.IWDG_Refesh) for [Anonymous Symbol]
    adpt_iwdg.o(.text.software_reset) refers to wdt.o(.text.Wdt_GetIrqStatus) for Wdt_GetIrqStatus
    adpt_iwdg.o(.text.software_reset) refers to wdt.o(.text.Wdt_IrqClr) for Wdt_IrqClr
    adpt_iwdg.o(.text.software_reset) refers to puts.o(i.puts) for puts
    adpt_iwdg.o(.text.software_reset) refers to interrupts_hc32l186.o(.text.EnableNvic) for EnableNvic
    adpt_iwdg.o(.text.software_reset) refers to sysctrl.o(.text.Sysctrl_SetPeripheralGate) for Sysctrl_SetPeripheralGate
    adpt_iwdg.o(.text.software_reset) refers to ramlog.o(.text.Panic) for Panic
    adpt_iwdg.o(.text.software_reset) refers to adpt_iwdg.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    adpt_iwdg.o(.ARM.exidx.text.software_reset) refers to adpt_iwdg.o(.text.software_reset) for [Anonymous Symbol]
    adpt_iwdg.o(.ARM.exidx.text.__NVIC_SystemReset) refers to adpt_iwdg.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    adpt_pwm.o(.text.AdtTimer3Init) refers to ddl.o(.text.ddl_memclr) for ddl_memclr
    adpt_pwm.o(.text.AdtTimer3Init) refers to sysctrl.o(.text.Sysctrl_SetPeripheralGate) for Sysctrl_SetPeripheralGate
    adpt_pwm.o(.text.AdtTimer3Init) refers to timer3.o(.text.Tim3_Mode23_Init) for Tim3_Mode23_Init
    adpt_pwm.o(.text.AdtTimer3Init) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    adpt_pwm.o(.text.AdtTimer3Init) refers to timer3.o(.text.Tim3_M23_ARRSet) for Tim3_M23_ARRSet
    adpt_pwm.o(.text.AdtTimer3Init) refers to timer3.o(.text.Tim3_M23_CCR_Set) for Tim3_M23_CCR_Set
    adpt_pwm.o(.text.AdtTimer3Init) refers to timer3.o(.text.Tim3_M23_PortOutput_Cfg) for Tim3_M23_PortOutput_Cfg
    adpt_pwm.o(.text.AdtTimer3Init) refers to timer3.o(.text.Tim3_M23_SetValidPeriod) for Tim3_M23_SetValidPeriod
    adpt_pwm.o(.text.AdtTimer3Init) refers to timer3.o(.text.Tim3_M23_Cnt16Set) for Tim3_M23_Cnt16Set
    adpt_pwm.o(.text.AdtTimer3Init) refers to timer3.o(.text.Tim3_M23_EnPWM_Output) for Tim3_M23_EnPWM_Output
    adpt_pwm.o(.text.AdtTimer3Init) refers to timer3.o(.text.Tim3_M23_Run) for Tim3_M23_Run
    adpt_pwm.o(.text.AdtTimer3Init) refers to system_hc32l186.o(.data.SystemCoreClock) for SystemCoreClock
    adpt_pwm.o(.ARM.exidx.text.AdtTimer3Init) refers to adpt_pwm.o(.text.AdtTimer3Init) for [Anonymous Symbol]
    adpt_pwm.o(.text.Board_InitTim) refers to sysctrl.o(.text.Sysctrl_SetPeripheralGate) for Sysctrl_SetPeripheralGate
    adpt_pwm.o(.text.Board_InitTim) refers to adpt_pwm.o(.text.AdtTimer2Init) for AdtTimer2Init
    adpt_pwm.o(.text.Board_InitTim) refers to adpt_pwm.o(.text.AdtTimer3Init) for AdtTimer3Init
    adpt_pwm.o(.ARM.exidx.text.Board_InitTim) refers to adpt_pwm.o(.text.Board_InitTim) for [Anonymous Symbol]
    adpt_pwm.o(.text.AdtTimer2Init) refers to ddl.o(.text.ddl_memclr) for ddl_memclr
    adpt_pwm.o(.text.AdtTimer2Init) refers to sysctrl.o(.text.Sysctrl_SetPeripheralGate) for Sysctrl_SetPeripheralGate
    adpt_pwm.o(.text.AdtTimer2Init) refers to bt.o(.text.Bt_Mode23_Init) for Bt_Mode23_Init
    adpt_pwm.o(.text.AdtTimer2Init) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    adpt_pwm.o(.text.AdtTimer2Init) refers to bt.o(.text.Bt_M23_ARRSet) for Bt_M23_ARRSet
    adpt_pwm.o(.text.AdtTimer2Init) refers to bt.o(.text.Bt_M23_CCR_Set) for Bt_M23_CCR_Set
    adpt_pwm.o(.text.AdtTimer2Init) refers to bt.o(.text.Bt_M23_PortOutput_Cfg) for Bt_M23_PortOutput_Cfg
    adpt_pwm.o(.text.AdtTimer2Init) refers to bt.o(.text.Bt_M23_SetValidPeriod) for Bt_M23_SetValidPeriod
    adpt_pwm.o(.text.AdtTimer2Init) refers to bt.o(.text.Bt_M23_Cnt16Set) for Bt_M23_Cnt16Set
    adpt_pwm.o(.text.AdtTimer2Init) refers to bt.o(.text.Bt_M23_EnPWM_Output) for Bt_M23_EnPWM_Output
    adpt_pwm.o(.text.AdtTimer2Init) refers to bt.o(.text.Bt_M23_Run) for Bt_M23_Run
    adpt_pwm.o(.text.AdtTimer2Init) refers to system_hc32l186.o(.data.SystemCoreClock) for SystemCoreClock
    adpt_pwm.o(.ARM.exidx.text.AdtTimer2Init) refers to adpt_pwm.o(.text.AdtTimer2Init) for [Anonymous Symbol]
    adpt_timebase.o(.text.Time0Init) refers to ddl.o(.text.ddl_memclr) for ddl_memclr
    adpt_timebase.o(.text.Time0Init) refers to sysctrl.o(.text.Sysctrl_SetPeripheralGate) for Sysctrl_SetPeripheralGate
    adpt_timebase.o(.text.Time0Init) refers to bt.o(.text.Bt_Mode0_Init) for Bt_Mode0_Init
    adpt_timebase.o(.text.Time0Init) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    adpt_timebase.o(.text.Time0Init) refers to bt.o(.text.Bt_M0_ARRSet) for Bt_M0_ARRSet
    adpt_timebase.o(.text.Time0Init) refers to bt.o(.text.Bt_M0_Cnt16Set) for Bt_M0_Cnt16Set
    adpt_timebase.o(.text.Time0Init) refers to bt.o(.text.Bt_ClearIntFlag) for Bt_ClearIntFlag
    adpt_timebase.o(.text.Time0Init) refers to bt.o(.text.Bt_Mode0_EnableIrq) for Bt_Mode0_EnableIrq
    adpt_timebase.o(.text.Time0Init) refers to interrupts_hc32l186.o(.text.EnableNvic) for EnableNvic
    adpt_timebase.o(.text.Time0Init) refers to bt.o(.text.Bt_M0_Run) for Bt_M0_Run
    adpt_timebase.o(.text.Time0Init) refers to system_hc32l186.o(.data.SystemCoreClock) for SystemCoreClock
    adpt_timebase.o(.ARM.exidx.text.Time0Init) refers to adpt_timebase.o(.text.Time0Init) for [Anonymous Symbol]
    adpt_timebase.o(.text.Tim0_IRQHandler) refers to bt.o(.text.Bt_GetIntFlag) for Bt_GetIntFlag
    adpt_timebase.o(.text.Tim0_IRQHandler) refers to timebase.o(.text.ISR_Timer_500us) for ISR_Timer_500us
    adpt_timebase.o(.text.Tim0_IRQHandler) refers to bt.o(.text.Bt_ClearIntFlag) for Bt_ClearIntFlag
    adpt_timebase.o(.ARM.exidx.text.Tim0_IRQHandler) refers to adpt_timebase.o(.text.Tim0_IRQHandler) for [Anonymous Symbol]
    adpt_timebase.o(.text.SysTick_IRQHandler) refers to timebase.o(.text.ISR_Timer_1ms) for ISR_Timer_1ms
    adpt_timebase.o(.ARM.exidx.text.SysTick_IRQHandler) refers to adpt_timebase.o(.text.SysTick_IRQHandler) for [Anonymous Symbol]
    adpt_timebase.o(.text.Board_InitSysTick) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    adpt_timebase.o(.text.Board_InitSysTick) refers to adpt_timebase.o(.text.SysTick_Config) for SysTick_Config
    adpt_timebase.o(.text.Board_InitSysTick) refers to adpt_timebase.o(.text.Time0Init) for Time0Init
    adpt_timebase.o(.text.Board_InitSysTick) refers to system_hc32l186.o(.data.SystemCoreClock) for SystemCoreClock
    adpt_timebase.o(.ARM.exidx.text.Board_InitSysTick) refers to adpt_timebase.o(.text.Board_InitSysTick) for [Anonymous Symbol]
    adpt_timebase.o(.text.SysTick_Config) refers to adpt_timebase.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    adpt_timebase.o(.ARM.exidx.text.SysTick_Config) refers to adpt_timebase.o(.text.SysTick_Config) for [Anonymous Symbol]
    adpt_timebase.o(.ARM.exidx.text.__NVIC_SetPriority) refers to adpt_timebase.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    adpt_reset.o(.text.Board_InitReset) refers to adpt_reset.o(.bss.u32_ResetFlag) for [Anonymous Symbol]
    adpt_reset.o(.ARM.exidx.text.Board_InitReset) refers to adpt_reset.o(.text.Board_InitReset) for [Anonymous Symbol]
    adpt_reset.o(.text.Get_ResetFlag) refers to adpt_reset.o(.bss.u32_ResetFlag) for [Anonymous Symbol]
    adpt_reset.o(.ARM.exidx.text.Get_ResetFlag) refers to adpt_reset.o(.text.Get_ResetFlag) for [Anonymous Symbol]
    adpt_flash.o(.text.Board_InitFlash) refers to flash.o(.text.Flash_Init) for Flash_Init
    adpt_flash.o(.ARM.exidx.text.Board_InitFlash) refers to adpt_flash.o(.text.Board_InitFlash) for [Anonymous Symbol]
    adpt_flash.o(.ARM.exidx.text.FlashSectorNumber) refers to adpt_flash.o(.text.FlashSectorNumber) for [Anonymous Symbol]
    adpt_flash.o(.ARM.exidx.text.FlashReadBytes) refers to adpt_flash.o(.text.FlashReadBytes) for [Anonymous Symbol]
    adpt_flash.o(.text.FlashWriteBytes) refers to flash.o(.text.Flash_OpModeConfig) for Flash_OpModeConfig
    adpt_flash.o(.text.FlashWriteBytes) refers to flash.o(.text.Flash_Write8) for Flash_Write8
    adpt_flash.o(.ARM.exidx.text.FlashWriteBytes) refers to adpt_flash.o(.text.FlashWriteBytes) for [Anonymous Symbol]
    adpt_flash.o(.text.FlashWriteHalfWords) refers to flash.o(.text.Flash_OpModeConfig) for Flash_OpModeConfig
    adpt_flash.o(.text.FlashWriteHalfWords) refers to flash.o(.text.Flash_Write16) for Flash_Write16
    adpt_flash.o(.ARM.exidx.text.FlashWriteHalfWords) refers to adpt_flash.o(.text.FlashWriteHalfWords) for [Anonymous Symbol]
    adpt_flash.o(.text.FlashWriteWords) refers to flash.o(.text.Flash_OpModeConfig) for Flash_OpModeConfig
    adpt_flash.o(.text.FlashWriteWords) refers to flash.o(.text.Flash_Write32) for Flash_Write32
    adpt_flash.o(.ARM.exidx.text.FlashWriteWords) refers to adpt_flash.o(.text.FlashWriteWords) for [Anonymous Symbol]
    adpt_flash.o(.text.FlashSectorErase) refers to flash.o(.text.Flash_OpModeConfig) for Flash_OpModeConfig
    adpt_flash.o(.text.FlashSectorErase) refers to flash.o(.text.Flash_SectorErase) for Flash_SectorErase
    adpt_flash.o(.ARM.exidx.text.FlashSectorErase) refers to adpt_flash.o(.text.FlashSectorErase) for [Anonymous Symbol]
    adpt_flash.o(.text.FlashContWriteBytes) refers to flash.o(.text.Flash_OpModeConfig) for Flash_OpModeConfig
    adpt_flash.o(.text.FlashContWriteBytes) refers to flash.o(ramfunc) for Flash_ContWrite_Byte
    adpt_flash.o(.ARM.exidx.text.FlashContWriteBytes) refers to adpt_flash.o(.text.FlashContWriteBytes) for [Anonymous Symbol]
    adpt_flash.o(.text.FlashContWriteHalfWords) refers to flash.o(.text.Flash_OpModeConfig) for Flash_OpModeConfig
    adpt_flash.o(.text.FlashContWriteHalfWords) refers to flash.o(ramfunc) for Flash_ContWrite_HalfWord
    adpt_flash.o(.ARM.exidx.text.FlashContWriteHalfWords) refers to adpt_flash.o(.text.FlashContWriteHalfWords) for [Anonymous Symbol]
    adpt_flash.o(.text.FlashContWriteWords) refers to flash.o(.text.Flash_OpModeConfig) for Flash_OpModeConfig
    adpt_flash.o(.text.FlashContWriteWords) refers to flash.o(ramfunc) for Flash_ContWrite_Word
    adpt_flash.o(.ARM.exidx.text.FlashContWriteWords) refers to adpt_flash.o(.text.FlashContWriteWords) for [Anonymous Symbol]
    adpt_flash.o(.text.FlashEraseAll) refers to flash.o(ramfunc) for Flash_Chip_Erase
    adpt_flash.o(.ARM.exidx.text.FlashEraseAll) refers to adpt_flash.o(.text.FlashEraseAll) for [Anonymous Symbol]
    system_hc32l186.o(.text.SystemCoreClockUpdate) refers to sysctrl.o(.text.Sysctrl_GetHClkFreq) for Sysctrl_GetHClkFreq
    system_hc32l186.o(.text.SystemCoreClockUpdate) refers to system_hc32l186.o(.data.SystemCoreClock) for SystemCoreClock
    system_hc32l186.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_hc32l186.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    system_hc32l186.o(.text.SystemInit) refers to system_hc32l186.o(.text.SystemCoreClockUpdate) for SystemCoreClockUpdate
    system_hc32l186.o(.ARM.exidx.text.SystemInit) refers to system_hc32l186.o(.text.SystemInit) for [Anonymous Symbol]
    system_hc32l186.o(.text.$Sub$$main) refers to system_hc32l186.o(.text.SystemInit) for SystemInit
    system_hc32l186.o(.text.$Sub$$main) refers to main.o(.text.main) for $Super$$main
    system_hc32l186.o(.ARM.exidx.text.$Sub$$main) refers to system_hc32l186.o(.text.$Sub$$main) for [Anonymous Symbol]
    interrupts_hc32l186.o(.text.EnableNvic) refers to interrupts_hc32l186.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    interrupts_hc32l186.o(.text.EnableNvic) refers to interrupts_hc32l186.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    interrupts_hc32l186.o(.text.EnableNvic) refers to interrupts_hc32l186.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    interrupts_hc32l186.o(.text.EnableNvic) refers to interrupts_hc32l186.o(.text.__NVIC_DisableIRQ) for __NVIC_DisableIRQ
    interrupts_hc32l186.o(.ARM.exidx.text.EnableNvic) refers to interrupts_hc32l186.o(.text.EnableNvic) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to interrupts_hc32l186.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.__NVIC_SetPriority) refers to interrupts_hc32l186.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to interrupts_hc32l186.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.__NVIC_DisableIRQ) refers to interrupts_hc32l186.o(.text.__NVIC_DisableIRQ) for [Anonymous Symbol]
    interrupts_hc32l186.o(.text.SysTick_Handler) refers (Weak) to adpt_timebase.o(.text.SysTick_IRQHandler) for SysTick_IRQHandler
    interrupts_hc32l186.o(.ARM.exidx.text.SysTick_Handler) refers to interrupts_hc32l186.o(.text.SysTick_Handler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.PORTA_IRQHandler) refers to interrupts_hc32l186.o(.text.PORTA_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.PORTB_IRQHandler) refers to interrupts_hc32l186.o(.text.PORTB_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.PORTC_E_IRQHandler) refers to interrupts_hc32l186.o(.text.PORTC_E_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.PORTD_F_IRQHandler) refers to interrupts_hc32l186.o(.text.PORTD_F_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.DMAC_IRQHandler) refers to interrupts_hc32l186.o(.text.DMAC_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.TIM3_IRQHandler) refers to interrupts_hc32l186.o(.text.TIM3_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.UART0_2_IRQHandler) refers to interrupts_hc32l186.o(.text.UART0_2_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.UART1_3_IRQHandler) refers to interrupts_hc32l186.o(.text.UART1_3_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.LPUART0_IRQHandler) refers to interrupts_hc32l186.o(.text.LPUART0_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.LPUART1_IRQHandler) refers to interrupts_hc32l186.o(.text.LPUART1_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.SPI0_IRQHandler) refers to interrupts_hc32l186.o(.text.SPI0_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.SPI1_IRQHandler) refers to interrupts_hc32l186.o(.text.SPI1_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.I2C0_IRQHandler) refers to interrupts_hc32l186.o(.text.I2C0_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.I2C1_IRQHandler) refers to interrupts_hc32l186.o(.text.I2C1_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.text.TIM0_IRQHandler) refers (Weak) to adpt_timebase.o(.text.Tim0_IRQHandler) for Tim0_IRQHandler
    interrupts_hc32l186.o(.ARM.exidx.text.TIM0_IRQHandler) refers to interrupts_hc32l186.o(.text.TIM0_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.TIM1_IRQHandler) refers to interrupts_hc32l186.o(.text.TIM1_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.TIM2_IRQHandler) refers to interrupts_hc32l186.o(.text.TIM2_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.LPTIM0_1_IRQHandler) refers to interrupts_hc32l186.o(.text.LPTIM0_1_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.TIM4_IRQHandler) refers to interrupts_hc32l186.o(.text.TIM4_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.TIM5_IRQHandler) refers to interrupts_hc32l186.o(.text.TIM5_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.TIM6_IRQHandler) refers to interrupts_hc32l186.o(.text.TIM6_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.PCA_WWDT_IRQHandler) refers to interrupts_hc32l186.o(.text.PCA_WWDT_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.text.WDT_IRQHandler) refers (Weak) to adpt_iwdg.o(.text.Wdt_IRQHandler) for Wdt_IRQHandler
    interrupts_hc32l186.o(.ARM.exidx.text.WDT_IRQHandler) refers to interrupts_hc32l186.o(.text.WDT_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.RTC_IRQHandler) refers to interrupts_hc32l186.o(.text.RTC_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.text.ADC_DAC_IRQHandler) refers (Weak) to adpt_adc.o(.text.Adc_IRQHandler) for Adc_IRQHandler
    interrupts_hc32l186.o(.ARM.exidx.text.ADC_DAC_IRQHandler) refers to interrupts_hc32l186.o(.text.ADC_DAC_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.PCNT_IRQHandler) refers to interrupts_hc32l186.o(.text.PCNT_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.VC0_IRQHandler) refers to interrupts_hc32l186.o(.text.VC0_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.VC1_2_IRQHandler) refers to interrupts_hc32l186.o(.text.VC1_2_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.LVD_IRQHandler) refers to interrupts_hc32l186.o(.text.LVD_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.LCD_IRQHandler) refers to interrupts_hc32l186.o(.text.LCD_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.FLASH_RAM_IRQHandler) refers to interrupts_hc32l186.o(.text.FLASH_RAM_IRQHandler) for [Anonymous Symbol]
    interrupts_hc32l186.o(.ARM.exidx.text.CLKTRIM_CTRIM_IRQHandler) refers to interrupts_hc32l186.o(.text.CLKTRIM_CTRIM_IRQHandler) for [Anonymous Symbol]
    init_mcu.o(.text.Init_Mcu) refers to adpt_reset.o(.text.Board_InitReset) for Board_InitReset
    init_mcu.o(.text.Init_Mcu) refers to adpt_clock.o(.text.Board_InitClock) for Board_InitClock
    init_mcu.o(.text.Init_Mcu) refers to adpt_gpio.o(.text.Board_InitPins) for Board_InitPins
    init_mcu.o(.text.Init_Mcu) refers to adpt_timebase.o(.text.Board_InitSysTick) for Board_InitSysTick
    init_mcu.o(.text.Init_Mcu) refers to adpt_pwm.o(.text.Board_InitTim) for Board_InitTim
    init_mcu.o(.ARM.exidx.text.Init_Mcu) refers to init_mcu.o(.text.Init_Mcu) for [Anonymous Symbol]
    timebase.o(.text.ISR_Timer_1ms) refers to systemtimermodule.o(.text.Add_MSecCount) for Add_MSecCount
    timebase.o(.text.ISR_Timer_1ms) refers to timebase.o(.bss.u8_Millisec) for [Anonymous Symbol]
    timebase.o(.ARM.exidx.text.ISR_Timer_1ms) refers to timebase.o(.text.ISR_Timer_1ms) for [Anonymous Symbol]
    timebase.o(.text.ISR_Timer_500us) refers to singledampermodule.o(.text.Execute_SingleDamperDriver) for Execute_SingleDamperDriver
    timebase.o(.text.ISR_Timer_500us) refers to timebase.o(.bss.u8_OneMsecCount) for [Anonymous Symbol]
    timebase.o(.ARM.exidx.text.ISR_Timer_500us) refers to timebase.o(.text.ISR_Timer_500us) for [Anonymous Symbol]
    timebase.o(.text.Get_SystemMillisec) refers to timebase.o(.bss.u8_Millisec) for [Anonymous Symbol]
    timebase.o(.ARM.exidx.text.Get_SystemMillisec) refers to timebase.o(.text.Get_SystemMillisec) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_GetIrqStatus) refers to adc.o(.text.Adc_GetIrqStatus) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_ClrIrqStatus) refers to adc.o(.text.Adc_ClrIrqStatus) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_EnableIrq) refers to adc.o(.text.Adc_EnableIrq) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_DisableIrq) refers to adc.o(.text.Adc_DisableIrq) for [Anonymous Symbol]
    adc.o(.text.Adc_Init) refers to ddl.o(.text.delay10us) for delay10us
    adc.o(.ARM.exidx.text.Adc_Init) refers to adc.o(.text.Adc_Init) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_SglExtTrigCfg) refers to adc.o(.text.Adc_SglExtTrigCfg) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_SqrExtTrigCfg) refers to adc.o(.text.Adc_SqrExtTrigCfg) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_JqrExtTrigCfg) refers to adc.o(.text.Adc_JqrExtTrigCfg) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_SGL_Start) refers to adc.o(.text.Adc_SGL_Start) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_SGL_Stop) refers to adc.o(.text.Adc_SGL_Stop) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_SGL_Always_Start) refers to adc.o(.text.Adc_SGL_Always_Start) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_SGL_Always_Stop) refers to adc.o(.text.Adc_SGL_Always_Stop) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_SQR_Start) refers to adc.o(.text.Adc_SQR_Start) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_SQR_Stop) refers to adc.o(.text.Adc_SQR_Stop) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_JQR_Start) refers to adc.o(.text.Adc_JQR_Start) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_JQR_Stop) refers to adc.o(.text.Adc_JQR_Stop) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_Enable) refers to adc.o(.text.Adc_Enable) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_Disable) refers to adc.o(.text.Adc_Disable) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_SqrModeCfg) refers to adc.o(.text.Adc_SqrModeCfg) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_JqrModeCfg) refers to adc.o(.text.Adc_JqrModeCfg) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_CfgSglChannel) refers to adc.o(.text.Adc_CfgSglChannel) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_CfgSqrChannel) refers to adc.o(.text.Adc_CfgSqrChannel) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_CfgJqrChannel) refers to adc.o(.text.Adc_CfgJqrChannel) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_GetSglResult) refers to adc.o(.text.Adc_GetSglResult) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_GetSqrResult) refers to adc.o(.text.Adc_GetSqrResult) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_GetJqrResult) refers to adc.o(.text.Adc_GetJqrResult) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_GetAccResult) refers to adc.o(.text.Adc_GetAccResult) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_ClrAccResult) refers to adc.o(.text.Adc_ClrAccResult) for [Anonymous Symbol]
    adc.o(.ARM.exidx.text.Adc_ThresholdCfg) refers to adc.o(.text.Adc_ThresholdCfg) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_CfgIrq) refers to adt.o(.text.Adt_CfgIrq) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_GetIrqFlag) refers to adt.o(.text.Adt_GetIrqFlag) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_ClearIrqFlag) refers to adt.o(.text.Adt_ClearIrqFlag) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_ClearAllIrqFlag) refers to adt.o(.text.Adt_ClearAllIrqFlag) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_CfgHwCntUp) refers to adt.o(.text.Adt_CfgHwCntUp) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_ClearHwCntUp) refers to adt.o(.text.Adt_ClearHwCntUp) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_CfgHwCntDwn) refers to adt.o(.text.Adt_CfgHwCntDwn) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_ClearHwCntDwn) refers to adt.o(.text.Adt_ClearHwCntDwn) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_CfgHwStart) refers to adt.o(.text.Adt_CfgHwStart) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_ClearHwStart) refers to adt.o(.text.Adt_ClearHwStart) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_EnableHwStart) refers to adt.o(.text.Adt_EnableHwStart) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_DisableHwStart) refers to adt.o(.text.Adt_DisableHwStart) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_CfgHwStop) refers to adt.o(.text.Adt_CfgHwStop) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_ClearHwStop) refers to adt.o(.text.Adt_ClearHwStop) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_EnableHwStop) refers to adt.o(.text.Adt_EnableHwStop) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_DisableHwStop) refers to adt.o(.text.Adt_DisableHwStop) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_CfgHwClear) refers to adt.o(.text.Adt_CfgHwClear) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_ClearHwClear) refers to adt.o(.text.Adt_ClearHwClear) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_EnableHwClear) refers to adt.o(.text.Adt_EnableHwClear) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_DisableHwClear) refers to adt.o(.text.Adt_DisableHwClear) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_CfgHwCaptureA) refers to adt.o(.text.Adt_CfgHwCaptureA) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_ClearHwCaptureA) refers to adt.o(.text.Adt_ClearHwCaptureA) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_CfgHwCaptureB) refers to adt.o(.text.Adt_CfgHwCaptureB) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_ClearHwCaptureB) refers to adt.o(.text.Adt_ClearHwCaptureB) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_SwSyncStart) refers to adt.o(.text.Adt_SwSyncStart) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_SwSyncStop) refers to adt.o(.text.Adt_SwSyncStop) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_SwSyncClear) refers to adt.o(.text.Adt_SwSyncClear) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_GetSwSyncState) refers to adt.o(.text.Adt_GetSwSyncState) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_AosTrigCfg) refers to adt.o(.text.Adt_AosTrigCfg) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_IrqTrigCfg) refers to adt.o(.text.Adt_IrqTrigCfg) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_PortTrigCfg) refers to adt.o(.text.Adt_PortTrigCfg) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_CHxXPortCfg) refers to adt.o(.text.Adt_CHxXPortCfg) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_EnableBrakePort) refers to adt.o(.text.Adt_EnableBrakePort) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_ClearBrakePort) refers to adt.o(.text.Adt_ClearBrakePort) for [Anonymous Symbol]
    adt.o(.text.Adt_Disable3Cfg) refers to adt.o(.text.Adt_ClearBrakePort) for Adt_ClearBrakePort
    adt.o(.text.Adt_Disable3Cfg) refers to adt.o(.text.Adt_EnableBrakePort) for Adt_EnableBrakePort
    adt.o(.ARM.exidx.text.Adt_Disable3Cfg) refers to adt.o(.text.Adt_Disable3Cfg) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_SwBrake) refers to adt.o(.text.Adt_SwBrake) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_GetPortBrakeFlag) refers to adt.o(.text.Adt_GetPortBrakeFlag) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_ClearPortBrakeFlag) refers to adt.o(.text.Adt_ClearPortBrakeFlag) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_Disable1Cfg) refers to adt.o(.text.Adt_Disable1Cfg) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_GetSameBrakeFlag) refers to adt.o(.text.Adt_GetSameBrakeFlag) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_ClearSameBrakeFlag) refers to adt.o(.text.Adt_ClearSameBrakeFlag) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_PwmDitherCfg) refers to adt.o(.text.Adt_PwmDitherCfg) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_Init) refers to adt.o(.text.Adt_Init) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_DeInit) refers to adt.o(.text.Adt_DeInit) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_StartCount) refers to adt.o(.text.Adt_StartCount) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_StopCount) refers to adt.o(.text.Adt_StopCount) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_SetCount) refers to adt.o(.text.Adt_SetCount) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_GetCount) refers to adt.o(.text.Adt_GetCount) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_ClearCount) refers to adt.o(.text.Adt_ClearCount) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_GetVperNum) refers to adt.o(.text.Adt_GetVperNum) for [Anonymous Symbol]
    adt.o(.text.Adt_GetState) refers to ddl.o(.text.GetBit) for GetBit
    adt.o(.ARM.exidx.text.Adt_GetState) refers to adt.o(.text.Adt_GetState) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_SetPeriod) refers to adt.o(.text.Adt_SetPeriod) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_SetPeriodBuf) refers to adt.o(.text.Adt_SetPeriodBuf) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_ClearPeriodBuf) refers to adt.o(.text.Adt_ClearPeriodBuf) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_SetValidPeriod) refers to adt.o(.text.Adt_SetValidPeriod) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_SetCompareValue) refers to adt.o(.text.Adt_SetCompareValue) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_SetSpecilCompareValue) refers to adt.o(.text.Adt_SetSpecilCompareValue) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_EnableValueBuf) refers to adt.o(.text.Adt_EnableValueBuf) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_ClearValueBuf) refers to adt.o(.text.Adt_ClearValueBuf) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_GetCaptureValue) refers to adt.o(.text.Adt_GetCaptureValue) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_GetCaptureBuf) refers to adt.o(.text.Adt_GetCaptureBuf) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_SetDTUA) refers to adt.o(.text.Adt_SetDTUA) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_SetDTDA) refers to adt.o(.text.Adt_SetDTDA) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_CfgDT) refers to adt.o(.text.Adt_CfgDT) for [Anonymous Symbol]
    adt.o(.ARM.exidx.text.Adt_CfgZMask) refers to adt.o(.text.Adt_CfgZMask) for [Anonymous Symbol]
    aes.o(.ARM.exidx.text.AES_Encrypt) refers to aes.o(.text.AES_Encrypt) for [Anonymous Symbol]
    aes.o(.ARM.exidx.text.AES_Decrypt) refers to aes.o(.text.AES_Decrypt) for [Anonymous Symbol]
    bgr.o(.text.Bgr_BgrEnable) refers to ddl.o(.text.delay10us) for delay10us
    bgr.o(.ARM.exidx.text.Bgr_BgrEnable) refers to bgr.o(.text.Bgr_BgrEnable) for [Anonymous Symbol]
    bgr.o(.ARM.exidx.text.Bgr_BgrDisable) refers to bgr.o(.text.Bgr_BgrDisable) for [Anonymous Symbol]
    bgr.o(.text.Bgr_TempSensorEnable) refers to ddl.o(.text.delay10us) for delay10us
    bgr.o(.ARM.exidx.text.Bgr_TempSensorEnable) refers to bgr.o(.text.Bgr_TempSensorEnable) for [Anonymous Symbol]
    bgr.o(.ARM.exidx.text.Bgr_TempSensorDisable) refers to bgr.o(.text.Bgr_TempSensorDisable) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_GetIntFlag) refers to bt.o(.text.Bt_GetIntFlag) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_ClearIntFlag) refers to bt.o(.text.Bt_ClearIntFlag) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_ClearAllIntFlag) refers to bt.o(.text.Bt_ClearAllIntFlag) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_Mode0_EnableIrq) refers to bt.o(.text.Bt_Mode0_EnableIrq) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_Mode0_DisableIrq) refers to bt.o(.text.Bt_Mode0_DisableIrq) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_Mode1_EnableIrq) refers to bt.o(.text.Bt_Mode1_EnableIrq) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_Mode1_DisableIrq) refers to bt.o(.text.Bt_Mode1_DisableIrq) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_Mode23_EnableIrq) refers to bt.o(.text.Bt_Mode23_EnableIrq) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_Mode23_DisableIrq) refers to bt.o(.text.Bt_Mode23_DisableIrq) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_Mode0_Init) refers to bt.o(.text.Bt_Mode0_Init) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M0_Run) refers to bt.o(.text.Bt_M0_Run) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M0_Stop) refers to bt.o(.text.Bt_M0_Stop) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M0_EnTOG_Output) refers to bt.o(.text.Bt_M0_EnTOG_Output) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M0_Enable_Output) refers to bt.o(.text.Bt_M0_Enable_Output) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M0_Cnt16Set) refers to bt.o(.text.Bt_M0_Cnt16Set) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M0_Cnt16Get) refers to bt.o(.text.Bt_M0_Cnt16Get) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M0_ARRSet) refers to bt.o(.text.Bt_M0_ARRSet) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M0_Cnt32Set) refers to bt.o(.text.Bt_M0_Cnt32Set) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M0_Cnt32Get) refers to bt.o(.text.Bt_M0_Cnt32Get) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_Mode1_Init) refers to bt.o(.text.Bt_Mode1_Init) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M1_Input_Cfg) refers to bt.o(.text.Bt_M1_Input_Cfg) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M1_PWC_Edge_Sel) refers to bt.o(.text.Bt_M1_PWC_Edge_Sel) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M1_Run) refers to bt.o(.text.Bt_M1_Run) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M1_Stop) refers to bt.o(.text.Bt_M1_Stop) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M1_Cnt16Set) refers to bt.o(.text.Bt_M1_Cnt16Set) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M1_Cnt16Get) refers to bt.o(.text.Bt_M1_Cnt16Get) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M1_PWC_CapValueGet) refers to bt.o(.text.Bt_M1_PWC_CapValueGet) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_Mode23_Init) refers to bt.o(.text.Bt_Mode23_Init) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_EnPWM_Output) refers to bt.o(.text.Bt_M23_EnPWM_Output) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_Run) refers to bt.o(.text.Bt_M23_Run) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_Stop) refers to bt.o(.text.Bt_M23_Stop) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_ARRSet) refers to bt.o(.text.Bt_M23_ARRSet) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_Cnt16Set) refers to bt.o(.text.Bt_M23_Cnt16Set) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_Cnt16Get) refers to bt.o(.text.Bt_M23_Cnt16Get) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_CCR_Set) refers to bt.o(.text.Bt_M23_CCR_Set) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_CCR_Get) refers to bt.o(.text.Bt_M23_CCR_Get) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_GateFuncSel) refers to bt.o(.text.Bt_M23_GateFuncSel) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_MasterSlave_Set) refers to bt.o(.text.Bt_M23_MasterSlave_Set) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_PortOutput_Cfg) refers to bt.o(.text.Bt_M23_PortOutput_Cfg) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_PortInput_Cfg) refers to bt.o(.text.Bt_M23_PortInput_Cfg) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_ETRInput_Cfg) refers to bt.o(.text.Bt_M23_ETRInput_Cfg) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_BrakeInput_Cfg) refers to bt.o(.text.Bt_M23_BrakeInput_Cfg) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_TrigADC_Cfg) refers to bt.o(.text.Bt_M23_TrigADC_Cfg) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_DT_Cfg) refers to bt.o(.text.Bt_M23_DT_Cfg) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_SetValidPeriod) refers to bt.o(.text.Bt_M23_SetValidPeriod) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_OCRefClr) refers to bt.o(.text.Bt_M23_OCRefClr) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_EnDMA) refers to bt.o(.text.Bt_M23_EnDMA) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_EnSwTrigCapCmpA) refers to bt.o(.text.Bt_M23_EnSwTrigCapCmpA) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_EnSwTrigCapCmpB) refers to bt.o(.text.Bt_M23_EnSwTrigCapCmpB) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_EnSwUev) refers to bt.o(.text.Bt_M23_EnSwUev) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_EnSwTrig) refers to bt.o(.text.Bt_M23_EnSwTrig) for [Anonymous Symbol]
    bt.o(.ARM.exidx.text.Bt_M23_EnSwBk) refers to bt.o(.text.Bt_M23_EnSwBk) for [Anonymous Symbol]
    crc.o(.ARM.exidx.text.CRC16_Get8) refers to crc.o(.text.CRC16_Get8) for [Anonymous Symbol]
    crc.o(.ARM.exidx.text.CRC16_Get16) refers to crc.o(.text.CRC16_Get16) for [Anonymous Symbol]
    crc.o(.ARM.exidx.text.CRC16_Get32) refers to crc.o(.text.CRC16_Get32) for [Anonymous Symbol]
    crc.o(.ARM.exidx.text.CRC16_Check8) refers to crc.o(.text.CRC16_Check8) for [Anonymous Symbol]
    crc.o(.ARM.exidx.text.CRC16_Check16) refers to crc.o(.text.CRC16_Check16) for [Anonymous Symbol]
    crc.o(.ARM.exidx.text.CRC16_Check32) refers to crc.o(.text.CRC16_Check32) for [Anonymous Symbol]
    crc.o(.ARM.exidx.text.CRC32_Get8) refers to crc.o(.text.CRC32_Get8) for [Anonymous Symbol]
    crc.o(.ARM.exidx.text.CRC32_Get16) refers to crc.o(.text.CRC32_Get16) for [Anonymous Symbol]
    crc.o(.ARM.exidx.text.CRC32_Get32) refers to crc.o(.text.CRC32_Get32) for [Anonymous Symbol]
    crc.o(.ARM.exidx.text.CRC32_Check8) refers to crc.o(.text.CRC32_Check8) for [Anonymous Symbol]
    crc.o(.ARM.exidx.text.CRC32_Check16) refers to crc.o(.text.CRC32_Check16) for [Anonymous Symbol]
    crc.o(.ARM.exidx.text.CRC32_Check32) refers to crc.o(.text.CRC32_Check32) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_CaliInit) refers to ctrim.o(.text.CTRIM_CaliInit) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_TimerInit) refers to ctrim.o(.text.CTRIM_TimerInit) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_GetCounter) refers to ctrim.o(.text.CTRIM_GetCounter) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_SetAutoReload) refers to ctrim.o(.text.CTRIM_SetAutoReload) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_GetAutoReload) refers to ctrim.o(.text.CTRIM_GetAutoReload) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_SetInitialStep) refers to ctrim.o(.text.CTRIM_SetInitialStep) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_GetInitialStep) refers to ctrim.o(.text.CTRIM_GetInitialStep) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_SetEtrClockFilter) refers to ctrim.o(.text.CTRIM_SetEtrClockFilter) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_GetEtrClockFilter) refers to ctrim.o(.text.CTRIM_GetEtrClockFilter) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_SetAccurateClock) refers to ctrim.o(.text.CTRIM_SetAccurateClock) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_GetAccurateClock) refers to ctrim.o(.text.CTRIM_GetAccurateClock) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_SetMode) refers to ctrim.o(.text.CTRIM_SetMode) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_GetMode) refers to ctrim.o(.text.CTRIM_GetMode) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_SetRCHTrimBits) refers to ctrim.o(.text.CTRIM_SetRCHTrimBits) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_GetRCHTrimBits) refers to ctrim.o(.text.CTRIM_GetRCHTrimBits) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_SetRefClockDiv) refers to ctrim.o(.text.CTRIM_SetRefClockDiv) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_GetRefClockDiv) refers to ctrim.o(.text.CTRIM_GetRefClockDiv) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_SetOneShot) refers to ctrim.o(.text.CTRIM_SetOneShot) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_GetOneShot) refers to ctrim.o(.text.CTRIM_GetOneShot) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_GetARRCoutDir) refers to ctrim.o(.text.CTRIM_GetARRCoutDir) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_GetTrimCode) refers to ctrim.o(.text.CTRIM_GetTrimCode) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_GetCountErrorCapture) refers to ctrim.o(.text.CTRIM_GetCountErrorCapture) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_SetCountErrorLimit) refers to ctrim.o(.text.CTRIM_SetCountErrorLimit) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_GetCountErrorLimit) refers to ctrim.o(.text.CTRIM_GetCountErrorLimit) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_Enable) refers to ctrim.o(.text.CTRIM_Enable) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_Disable) refers to ctrim.o(.text.CTRIM_Disable) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_IsEnable) refers to ctrim.o(.text.CTRIM_IsEnable) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_EnableIT) refers to ctrim.o(.text.CTRIM_EnableIT) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_DisableIT) refers to ctrim.o(.text.CTRIM_DisableIT) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_IsEnableIT) refers to ctrim.o(.text.CTRIM_IsEnableIT) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_IsActiveFlag) refers to ctrim.o(.text.CTRIM_IsActiveFlag) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_ClearFlag) refers to ctrim.o(.text.CTRIM_ClearFlag) for [Anonymous Symbol]
    ctrim.o(.ARM.exidx.text.CTRIM_ClearFlag_ALL) refers to ctrim.o(.text.CTRIM_ClearFlag_ALL) for [Anonymous Symbol]
    dac.o(.text.Dac_DmaCmd) refers to ddl.o(.text.SetBit) for SetBit
    dac.o(.ARM.exidx.text.Dac_DmaCmd) refers to dac.o(.text.Dac_DmaCmd) for [Anonymous Symbol]
    dac.o(.text.Dac_DmaITCfg) refers to ddl.o(.text.SetBit) for SetBit
    dac.o(.ARM.exidx.text.Dac_DmaITCfg) refers to dac.o(.text.Dac_DmaITCfg) for [Anonymous Symbol]
    dac.o(.text.Dac_GetITStatus) refers to ddl.o(.text.GetBit) for GetBit
    dac.o(.ARM.exidx.text.Dac_GetITStatus) refers to dac.o(.text.Dac_GetITStatus) for [Anonymous Symbol]
    dac.o(.text.Dac_Cmd) refers to ddl.o(.text.SetBit) for SetBit
    dac.o(.ARM.exidx.text.Dac_Cmd) refers to dac.o(.text.Dac_Cmd) for [Anonymous Symbol]
    dac.o(.text.Dac_SoftwareTriggerCmd) refers to ddl.o(.text.SetBit) for SetBit
    dac.o(.ARM.exidx.text.Dac_SoftwareTriggerCmd) refers to dac.o(.text.Dac_SoftwareTriggerCmd) for [Anonymous Symbol]
    dac.o(.ARM.exidx.text.Dac_Init) refers to dac.o(.text.Dac_Init) for [Anonymous Symbol]
    dac.o(.ARM.exidx.text.Dac_SetChannelData) refers to dac.o(.text.Dac_SetChannelData) for [Anonymous Symbol]
    dac.o(.ARM.exidx.text.Dac_GetDataOutputValue) refers to dac.o(.text.Dac_GetDataOutputValue) for [Anonymous Symbol]
    ddl.o(.ARM.exidx.text.Log2) refers to ddl.o(.text.Log2) for [Anonymous Symbol]
    ddl.o(.text.ddl_memclr) refers to memseta.o(.text) for __aeabi_memclr
    ddl.o(.ARM.exidx.text.ddl_memclr) refers to ddl.o(.text.ddl_memclr) for [Anonymous Symbol]
    ddl.o(.text.delay1ms) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    ddl.o(.text.delay1ms) refers to system_hc32l186.o(.data.SystemCoreClock) for SystemCoreClock
    ddl.o(.ARM.exidx.text.delay1ms) refers to ddl.o(.text.delay1ms) for [Anonymous Symbol]
    ddl.o(.text.delay100us) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    ddl.o(.text.delay100us) refers to system_hc32l186.o(.data.SystemCoreClock) for SystemCoreClock
    ddl.o(.ARM.exidx.text.delay100us) refers to ddl.o(.text.delay100us) for [Anonymous Symbol]
    ddl.o(.text.delay10us) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    ddl.o(.text.delay10us) refers to system_hc32l186.o(.data.SystemCoreClock) for SystemCoreClock
    ddl.o(.ARM.exidx.text.delay10us) refers to ddl.o(.text.delay10us) for [Anonymous Symbol]
    ddl.o(.ARM.exidx.text.SetBit) refers to ddl.o(.text.SetBit) for [Anonymous Symbol]
    ddl.o(.ARM.exidx.text.GetBit) refers to ddl.o(.text.GetBit) for [Anonymous Symbol]
    debug.o(.ARM.exidx.text.Debug_ActiveEnable) refers to debug.o(.text.Debug_ActiveEnable) for [Anonymous Symbol]
    debug.o(.ARM.exidx.text.Debug_ActiveDisable) refers to debug.o(.text.Debug_ActiveDisable) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_InitChannel) refers to dmac.o(.text.Dma_InitChannel) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_Enable) refers to dmac.o(.text.Dma_Enable) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_Disable) refers to dmac.o(.text.Dma_Disable) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_SwStart) refers to dmac.o(.text.Dma_SwStart) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_SwStop) refers to dmac.o(.text.Dma_SwStop) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_EnableChannelIrq) refers to dmac.o(.text.Dma_EnableChannelIrq) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_DisableChannelIrq) refers to dmac.o(.text.Dma_DisableChannelIrq) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_EnableChannelErrIrq) refers to dmac.o(.text.Dma_EnableChannelErrIrq) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_DisableChannelErrIrq) refers to dmac.o(.text.Dma_DisableChannelErrIrq) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_EnableChannel) refers to dmac.o(.text.Dma_EnableChannel) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_DisableChannel) refers to dmac.o(.text.Dma_DisableChannel) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_SetBlockSize) refers to dmac.o(.text.Dma_SetBlockSize) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_SetTransferCnt) refers to dmac.o(.text.Dma_SetTransferCnt) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_EnableContinusTranfer) refers to dmac.o(.text.Dma_EnableContinusTranfer) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_DisableContinusTranfer) refers to dmac.o(.text.Dma_DisableContinusTranfer) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_HaltTranfer) refers to dmac.o(.text.Dma_HaltTranfer) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_RecoverTranfer) refers to dmac.o(.text.Dma_RecoverTranfer) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_PauseChannelTranfer) refers to dmac.o(.text.Dma_PauseChannelTranfer) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_RecoverChannelTranfer) refers to dmac.o(.text.Dma_RecoverChannelTranfer) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_SetTransferWidth) refers to dmac.o(.text.Dma_SetTransferWidth) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_SetChPriority) refers to dmac.o(.text.Dma_SetChPriority) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_GetStat) refers to dmac.o(.text.Dma_GetStat) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_ClrStat) refers to dmac.o(.text.Dma_ClrStat) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_SetSourceAddress) refers to dmac.o(.text.Dma_SetSourceAddress) for [Anonymous Symbol]
    dmac.o(.ARM.exidx.text.Dma_SetDestinationAddress) refers to dmac.o(.text.Dma_SetDestinationAddress) for [Anonymous Symbol]
    flash.o(.ARM.exidx.text.Flash_GetIntFlag) refers to flash.o(.text.Flash_GetIntFlag) for [Anonymous Symbol]
    flash.o(.ARM.exidx.text.Flash_ClearIntFlag) refers to flash.o(.text.Flash_ClearIntFlag) for [Anonymous Symbol]
    flash.o(.ARM.exidx.text.Flash_EnableIrq) refers to flash.o(.text.Flash_EnableIrq) for [Anonymous Symbol]
    flash.o(.ARM.exidx.text.Flash_DisableIrq) refers to flash.o(.text.Flash_DisableIrq) for [Anonymous Symbol]
    flash.o(.text.Flash_Init) refers to memseta.o(.text) for __aeabi_memclr4
    flash.o(.text.Flash_Init) refers to flash.o(.rodata.pu32PcgTimer4M) for pu32PcgTimer4M
    flash.o(.ARM.exidx.text.Flash_Init) refers to flash.o(.text.Flash_Init) for [Anonymous Symbol]
    flash.o(.text.Flash_Write8) refers to flash.o(.text.Flash_UnlockAll) for Flash_UnlockAll
    flash.o(.text.Flash_Write8) refers to flash.o(.text.Flash_LockAll) for Flash_LockAll
    flash.o(.ARM.exidx.text.Flash_Write8) refers to flash.o(.text.Flash_Write8) for [Anonymous Symbol]
    flash.o(.ARM.exidx.text.Flash_UnlockAll) refers to flash.o(.text.Flash_UnlockAll) for [Anonymous Symbol]
    flash.o(.ARM.exidx.text.Flash_LockAll) refers to flash.o(.text.Flash_LockAll) for [Anonymous Symbol]
    flash.o(.text.Flash_Write16) refers to flash.o(.text.Flash_UnlockAll) for Flash_UnlockAll
    flash.o(.text.Flash_Write16) refers to flash.o(.text.Flash_LockAll) for Flash_LockAll
    flash.o(.ARM.exidx.text.Flash_Write16) refers to flash.o(.text.Flash_Write16) for [Anonymous Symbol]
    flash.o(.text.Flash_Write32) refers to flash.o(.text.Flash_UnlockAll) for Flash_UnlockAll
    flash.o(.text.Flash_Write32) refers to flash.o(.text.Flash_LockAll) for Flash_LockAll
    flash.o(.ARM.exidx.text.Flash_Write32) refers to flash.o(.text.Flash_Write32) for [Anonymous Symbol]
    flash.o(.text.Flash_SectorErase) refers to flash.o(.text.Flash_UnlockAll) for Flash_UnlockAll
    flash.o(.text.Flash_SectorErase) refers to flash.o(.text.Flash_LockAll) for Flash_LockAll
    flash.o(.ARM.exidx.text.Flash_SectorErase) refers to flash.o(.text.Flash_SectorErase) for [Anonymous Symbol]
    flash.o(.ARM.exidx.text.Flash_OpModeConfig) refers to flash.o(.text.Flash_OpModeConfig) for [Anonymous Symbol]
    flash.o(.ARM.exidx.text.Flash_WaitCycle) refers to flash.o(.text.Flash_WaitCycle) for [Anonymous Symbol]
    flash.o(.ARM.exidx.text.Flash_EnDpstb) refers to flash.o(.text.Flash_EnDpstb) for [Anonymous Symbol]
    flash.o(.ARM.exidx.text.Flash_LockSet) refers to flash.o(.text.Flash_LockSet) for [Anonymous Symbol]
    flash.o(.ARM.exidxramfunc) refers to flash.o(ramfunc) for [Anonymous Symbol]
    gpio.o(.text.Gpio_Init) refers to ddl.o(.text.SetBit) for SetBit
    gpio.o(.ARM.exidx.text.Gpio_Init) refers to gpio.o(.text.Gpio_Init) for [Anonymous Symbol]
    gpio.o(.text.Gpio_GetInputIO) refers to ddl.o(.text.GetBit) for GetBit
    gpio.o(.ARM.exidx.text.Gpio_GetInputIO) refers to gpio.o(.text.Gpio_GetInputIO) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.Gpio_GetInputData) refers to gpio.o(.text.Gpio_GetInputData) for [Anonymous Symbol]
    gpio.o(.text.Gpio_WriteOutputIO) refers to ddl.o(.text.SetBit) for SetBit
    gpio.o(.ARM.exidx.text.Gpio_WriteOutputIO) refers to gpio.o(.text.Gpio_WriteOutputIO) for [Anonymous Symbol]
    gpio.o(.text.Gpio_ReadOutputIO) refers to ddl.o(.text.GetBit) for GetBit
    gpio.o(.ARM.exidx.text.Gpio_ReadOutputIO) refers to gpio.o(.text.Gpio_ReadOutputIO) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.Gpio_SetPort) refers to gpio.o(.text.Gpio_SetPort) for [Anonymous Symbol]
    gpio.o(.text.Gpio_SetIO) refers to ddl.o(.text.SetBit) for SetBit
    gpio.o(.ARM.exidx.text.Gpio_SetIO) refers to gpio.o(.text.Gpio_SetIO) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.Gpio_ClrPort) refers to gpio.o(.text.Gpio_ClrPort) for [Anonymous Symbol]
    gpio.o(.text.Gpio_ClrIO) refers to ddl.o(.text.SetBit) for SetBit
    gpio.o(.ARM.exidx.text.Gpio_ClrIO) refers to gpio.o(.text.Gpio_ClrIO) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.Gpio_SetClrPort) refers to gpio.o(.text.Gpio_SetClrPort) for [Anonymous Symbol]
    gpio.o(.text.Gpio_SetAnalogMode) refers to ddl.o(.text.SetBit) for SetBit
    gpio.o(.ARM.exidx.text.Gpio_SetAnalogMode) refers to gpio.o(.text.Gpio_SetAnalogMode) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.Gpio_SetAfMode) refers to gpio.o(.text.Gpio_SetAfMode) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.Gpio_SetAfMode_Lite) refers to gpio.o(.text.Gpio_SetAfMode_Lite) for [Anonymous Symbol]
    gpio.o(.text.Gpio_EnableIrq) refers to ddl.o(.text.SetBit) for SetBit
    gpio.o(.ARM.exidx.text.Gpio_EnableIrq) refers to gpio.o(.text.Gpio_EnableIrq) for [Anonymous Symbol]
    gpio.o(.text.Gpio_DisableIrq) refers to ddl.o(.text.SetBit) for SetBit
    gpio.o(.ARM.exidx.text.Gpio_DisableIrq) refers to gpio.o(.text.Gpio_DisableIrq) for [Anonymous Symbol]
    gpio.o(.text.Gpio_GetIrqStatus) refers to ddl.o(.text.GetBit) for GetBit
    gpio.o(.ARM.exidx.text.Gpio_GetIrqStatus) refers to gpio.o(.text.Gpio_GetIrqStatus) for [Anonymous Symbol]
    gpio.o(.text.Gpio_ClearIrq) refers to ddl.o(.text.SetBit) for SetBit
    gpio.o(.ARM.exidx.text.Gpio_ClearIrq) refers to gpio.o(.text.Gpio_ClearIrq) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.Gpio_SfIrPolCfg) refers to gpio.o(.text.Gpio_SfIrPolCfg) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.Gpio_SfHClkOutputCfg) refers to gpio.o(.text.Gpio_SfHClkOutputCfg) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.Gpio_SfPClkOutputCfg) refers to gpio.o(.text.Gpio_SfPClkOutputCfg) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.Gpio_SfExtClkCfg) refers to gpio.o(.text.Gpio_SfExtClkCfg) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.Gpio_SfNssCfg) refers to gpio.o(.text.Gpio_SfNssCfg) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.Gpio_SfTimGCfg) refers to gpio.o(.text.Gpio_SfTimGCfg) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.Gpio_SfTimECfg) refers to gpio.o(.text.Gpio_SfTimECfg) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.Gpio_SfTimCCfg) refers to gpio.o(.text.Gpio_SfTimCCfg) for [Anonymous Symbol]
    gpio.o(.ARM.exidx.text.Gpio_SfPcaCfg) refers to gpio.o(.text.Gpio_SfPcaCfg) for [Anonymous Symbol]
    i2c.o(.ARM.exidx.text.I2C_SetBaud) refers to i2c.o(.text.I2C_SetBaud) for [Anonymous Symbol]
    i2c.o(.text.I2C_SetFunc) refers to ddl.o(.text.SetBit) for SetBit
    i2c.o(.ARM.exidx.text.I2C_SetFunc) refers to i2c.o(.text.I2C_SetFunc) for [Anonymous Symbol]
    i2c.o(.text.I2C_ClearFunc) refers to ddl.o(.text.SetBit) for SetBit
    i2c.o(.ARM.exidx.text.I2C_ClearFunc) refers to i2c.o(.text.I2C_ClearFunc) for [Anonymous Symbol]
    i2c.o(.ARM.exidx.text.I2C_GetIrq) refers to i2c.o(.text.I2C_GetIrq) for [Anonymous Symbol]
    i2c.o(.ARM.exidx.text.I2C_ClearIrq) refers to i2c.o(.text.I2C_ClearIrq) for [Anonymous Symbol]
    i2c.o(.ARM.exidx.text.I2C_GetState) refers to i2c.o(.text.I2C_GetState) for [Anonymous Symbol]
    i2c.o(.ARM.exidx.text.I2C_WriteByte) refers to i2c.o(.text.I2C_WriteByte) for [Anonymous Symbol]
    i2c.o(.ARM.exidx.text.I2C_ReadByte) refers to i2c.o(.text.I2C_ReadByte) for [Anonymous Symbol]
    i2c.o(.ARM.exidx.text.I2C_GetAddMatchState) refers to i2c.o(.text.I2C_GetAddMatchState) for [Anonymous Symbol]
    i2c.o(.text.I2C_Init) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    i2c.o(.text.I2C_Init) refers to i2c.o(.text.I2C_SetFunc) for I2C_SetFunc
    i2c.o(.text.I2C_Init) refers to i2c.o(.text.I2C_SetBaud) for I2C_SetBaud
    i2c.o(.ARM.exidx.text.I2C_Init) refers to i2c.o(.text.I2C_Init) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.Lcd_GetItStatus) refers to lcd.o(.text.Lcd_GetItStatus) for [Anonymous Symbol]
    lcd.o(.text.Lcd_ClearItPendingBit) refers to ddl.o(.text.SetBit) for SetBit
    lcd.o(.ARM.exidx.text.Lcd_ClearItPendingBit) refers to lcd.o(.text.Lcd_ClearItPendingBit) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.Lcd_GetSegCom) refers to lcd.o(.text.Lcd_GetSegCom) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.Lcd_SetSegCom) refers to lcd.o(.text.Lcd_SetSegCom) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.Lcd_Init) refers to lcd.o(.text.Lcd_Init) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.Lcd_FullDisp) refers to lcd.o(.text.Lcd_FullDisp) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.Lcd_ClearDisp) refers to lcd.o(.text.Lcd_ClearDisp) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.Lcd_WriteRam) refers to lcd.o(.text.Lcd_WriteRam) for [Anonymous Symbol]
    lpm.o(.ARM.exidx.text.Lpm_GotoDeepSleep) refers to lpm.o(.text.Lpm_GotoDeepSleep) for [Anonymous Symbol]
    lpm.o(.ARM.exidx.text.Lpm_GotoSleep) refers to lpm.o(.text.Lpm_GotoSleep) for [Anonymous Symbol]
    lptim.o(.text.Lptim_ConfIt) refers to ddl.o(.text.SetBit) for SetBit
    lptim.o(.ARM.exidx.text.Lptim_ConfIt) refers to lptim.o(.text.Lptim_ConfIt) for [Anonymous Symbol]
    lptim.o(.text.Lptim_Cmd) refers to ddl.o(.text.SetBit) for SetBit
    lptim.o(.ARM.exidx.text.Lptim_Cmd) refers to lptim.o(.text.Lptim_Cmd) for [Anonymous Symbol]
    lptim.o(.text.Lptim_GetItStatus) refers to ddl.o(.text.GetBit) for GetBit
    lptim.o(.ARM.exidx.text.Lptim_GetItStatus) refers to lptim.o(.text.Lptim_GetItStatus) for [Anonymous Symbol]
    lptim.o(.text.Lptim_ClrItStatus) refers to ddl.o(.text.SetBit) for SetBit
    lptim.o(.ARM.exidx.text.Lptim_ClrItStatus) refers to lptim.o(.text.Lptim_ClrItStatus) for [Anonymous Symbol]
    lptim.o(.ARM.exidx.text.Lptim_Init) refers to lptim.o(.text.Lptim_Init) for [Anonymous Symbol]
    lpuart.o(.text.LPUart_EnableIrq) refers to ddl.o(.text.SetBit) for SetBit
    lpuart.o(.ARM.exidx.text.LPUart_EnableIrq) refers to lpuart.o(.text.LPUart_EnableIrq) for [Anonymous Symbol]
    lpuart.o(.text.LPUart_DisableIrq) refers to ddl.o(.text.SetBit) for SetBit
    lpuart.o(.ARM.exidx.text.LPUart_DisableIrq) refers to lpuart.o(.text.LPUart_DisableIrq) for [Anonymous Symbol]
    lpuart.o(.ARM.exidx.text.LPUart_SelSclk) refers to lpuart.o(.text.LPUart_SelSclk) for [Anonymous Symbol]
    lpuart.o(.ARM.exidx.text.LPUart_SetMultiMode) refers to lpuart.o(.text.LPUart_SetMultiMode) for [Anonymous Symbol]
    lpuart.o(.ARM.exidx.text.LPUart_HdModeEnable) refers to lpuart.o(.text.LPUart_HdModeEnable) for [Anonymous Symbol]
    lpuart.o(.ARM.exidx.text.LPUart_HdModeDisable) refers to lpuart.o(.text.LPUart_HdModeDisable) for [Anonymous Symbol]
    lpuart.o(.ARM.exidx.text.LPUart_SetSaddr) refers to lpuart.o(.text.LPUart_SetSaddr) for [Anonymous Symbol]
    lpuart.o(.text.LPUart_EnableFunc) refers to ddl.o(.text.SetBit) for SetBit
    lpuart.o(.ARM.exidx.text.LPUart_EnableFunc) refers to lpuart.o(.text.LPUart_EnableFunc) for [Anonymous Symbol]
    lpuart.o(.text.LPUart_DisableFunc) refers to ddl.o(.text.SetBit) for SetBit
    lpuart.o(.ARM.exidx.text.LPUart_DisableFunc) refers to lpuart.o(.text.LPUart_DisableFunc) for [Anonymous Symbol]
    lpuart.o(.ARM.exidx.text.LPUart_GetIsr) refers to lpuart.o(.text.LPUart_GetIsr) for [Anonymous Symbol]
    lpuart.o(.text.LPUart_GetStatus) refers to ddl.o(.text.GetBit) for GetBit
    lpuart.o(.ARM.exidx.text.LPUart_GetStatus) refers to lpuart.o(.text.LPUart_GetStatus) for [Anonymous Symbol]
    lpuart.o(.ARM.exidx.text.LPUart_ClrIsr) refers to lpuart.o(.text.LPUart_ClrIsr) for [Anonymous Symbol]
    lpuart.o(.text.LPUart_ClrStatus) refers to ddl.o(.text.SetBit) for SetBit
    lpuart.o(.ARM.exidx.text.LPUart_ClrStatus) refers to lpuart.o(.text.LPUart_ClrStatus) for [Anonymous Symbol]
    lpuart.o(.text.LPUart_SendData) refers to lpuart.o(.text.LPUart_GetStatus) for LPUart_GetStatus
    lpuart.o(.text.LPUart_SendData) refers to lpuart.o(.text.LPUart_ClrStatus) for LPUart_ClrStatus
    lpuart.o(.ARM.exidx.text.LPUart_SendData) refers to lpuart.o(.text.LPUart_SendData) for [Anonymous Symbol]
    lpuart.o(.text.LPUart_SendDataTimeOut) refers to lpuart.o(.text.LPUart_GetStatus) for LPUart_GetStatus
    lpuart.o(.text.LPUart_SendDataTimeOut) refers to lpuart.o(.text.LPUart_ClrStatus) for LPUart_ClrStatus
    lpuart.o(.ARM.exidx.text.LPUart_SendDataTimeOut) refers to lpuart.o(.text.LPUart_SendDataTimeOut) for [Anonymous Symbol]
    lpuart.o(.text.LPUart_SendDataIt) refers to lpuart.o(.text.LPUart_GetStatus) for LPUart_GetStatus
    lpuart.o(.ARM.exidx.text.LPUart_SendDataIt) refers to lpuart.o(.text.LPUart_SendDataIt) for [Anonymous Symbol]
    lpuart.o(.text.LPUart_MultiModeSendData) refers to lpuart.o(.text.LPUart_GetStatus) for LPUart_GetStatus
    lpuart.o(.text.LPUart_MultiModeSendData) refers to lpuart.o(.text.LPUart_ClrStatus) for LPUart_ClrStatus
    lpuart.o(.ARM.exidx.text.LPUart_MultiModeSendData) refers to lpuart.o(.text.LPUart_MultiModeSendData) for [Anonymous Symbol]
    lpuart.o(.text.LPUart_MultiModeSendDataIt) refers to lpuart.o(.text.LPUart_GetStatus) for LPUart_GetStatus
    lpuart.o(.ARM.exidx.text.LPUart_MultiModeSendDataIt) refers to lpuart.o(.text.LPUart_MultiModeSendDataIt) for [Anonymous Symbol]
    lpuart.o(.ARM.exidx.text.LPUart_ReceiveData) refers to lpuart.o(.text.LPUart_ReceiveData) for [Anonymous Symbol]
    lpuart.o(.ARM.exidx.text.LPUart_GetRb8) refers to lpuart.o(.text.LPUart_GetRb8) for [Anonymous Symbol]
    lpuart.o(.text.LPUart_Init) refers to ffltui.o(.text) for __aeabi_ui2f
    lpuart.o(.text.LPUart_Init) refers to fdiv.o(.text) for __aeabi_fdiv
    lpuart.o(.text.LPUart_Init) refers to fadd.o(.text) for __aeabi_fadd
    lpuart.o(.text.LPUart_Init) refers to ffixi.o(.text) for __aeabi_f2iz
    lpuart.o(.text.LPUart_Init) refers to lpuart.o(.text.LPUart_EnableFunc) for LPUart_EnableFunc
    lpuart.o(.ARM.exidx.text.LPUart_Init) refers to lpuart.o(.text.LPUart_Init) for [Anonymous Symbol]
    lpuart.o(.ARM.exidx.text.LPUart_Xtl_Bsel_Set) refers to lpuart.o(.text.LPUart_Xtl_Bsel_Set) for [Anonymous Symbol]
    lvd.o(.ARM.exidx.text.Lvd_EnableIrq) refers to lvd.o(.text.Lvd_EnableIrq) for [Anonymous Symbol]
    lvd.o(.ARM.exidx.text.Lvd_DisableIrq) refers to lvd.o(.text.Lvd_DisableIrq) for [Anonymous Symbol]
    lvd.o(.ARM.exidx.text.Lvd_Init) refers to lvd.o(.text.Lvd_Init) for [Anonymous Symbol]
    lvd.o(.ARM.exidx.text.Lvd_Enable) refers to lvd.o(.text.Lvd_Enable) for [Anonymous Symbol]
    lvd.o(.ARM.exidx.text.Lvd_Disable) refers to lvd.o(.text.Lvd_Disable) for [Anonymous Symbol]
    lvd.o(.ARM.exidx.text.Lvd_GetIrqStat) refers to lvd.o(.text.Lvd_GetIrqStat) for [Anonymous Symbol]
    lvd.o(.ARM.exidx.text.Lvd_ClearIrq) refers to lvd.o(.text.Lvd_ClearIrq) for [Anonymous Symbol]
    lvd.o(.ARM.exidx.text.Lvd_GetFilterResult) refers to lvd.o(.text.Lvd_GetFilterResult) for [Anonymous Symbol]
    opa.o(.text.Opa_Cmd) refers to ddl.o(.text.SetBit) for SetBit
    opa.o(.ARM.exidx.text.Opa_Cmd) refers to opa.o(.text.Opa_Cmd) for [Anonymous Symbol]
    opa.o(.text.Opa_CmdBuf) refers to ddl.o(.text.SetBit) for SetBit
    opa.o(.ARM.exidx.text.Opa_CmdBuf) refers to opa.o(.text.Opa_CmdBuf) for [Anonymous Symbol]
    opa.o(.text.Opa_CmdOenx) refers to ddl.o(.text.SetBit) for SetBit
    opa.o(.ARM.exidx.text.Opa_CmdOenx) refers to opa.o(.text.Opa_CmdOenx) for [Anonymous Symbol]
    pca.o(.ARM.exidx.text.Pca_GetItStatus) refers to pca.o(.text.Pca_GetItStatus) for [Anonymous Symbol]
    pca.o(.ARM.exidx.text.Pca_ClrItStatus) refers to pca.o(.text.Pca_ClrItStatus) for [Anonymous Symbol]
    pca.o(.text.Pca_StartPca) refers to ddl.o(.text.SetBit) for SetBit
    pca.o(.ARM.exidx.text.Pca_StartPca) refers to pca.o(.text.Pca_StartPca) for [Anonymous Symbol]
    pca.o(.text.Pca_SetCidl) refers to ddl.o(.text.SetBit) for SetBit
    pca.o(.ARM.exidx.text.Pca_SetCidl) refers to pca.o(.text.Pca_SetCidl) for [Anonymous Symbol]
    pca.o(.text.Pca_Set4Wdte) refers to ddl.o(.text.SetBit) for SetBit
    pca.o(.ARM.exidx.text.Pca_Set4Wdte) refers to pca.o(.text.Pca_Set4Wdte) for [Anonymous Symbol]
    pca.o(.text.Pca_ConfPcaIt) refers to ddl.o(.text.SetBit) for SetBit
    pca.o(.ARM.exidx.text.Pca_ConfPcaIt) refers to pca.o(.text.Pca_ConfPcaIt) for [Anonymous Symbol]
    pca.o(.text.Pca_ConfModulexIt) refers to ddl.o(.text.SetBit) for SetBit
    pca.o(.ARM.exidx.text.Pca_ConfModulexIt) refers to pca.o(.text.Pca_ConfModulexIt) for [Anonymous Symbol]
    pca.o(.ARM.exidx.text.Pca_M0Init) refers to pca.o(.text.Pca_M0Init) for [Anonymous Symbol]
    pca.o(.ARM.exidx.text.Pca_M1Init) refers to pca.o(.text.Pca_M1Init) for [Anonymous Symbol]
    pca.o(.ARM.exidx.text.Pca_M2Init) refers to pca.o(.text.Pca_M2Init) for [Anonymous Symbol]
    pca.o(.ARM.exidx.text.Pca_M3Init) refers to pca.o(.text.Pca_M3Init) for [Anonymous Symbol]
    pca.o(.ARM.exidx.text.Pca_M4Init) refers to pca.o(.text.Pca_M4Init) for [Anonymous Symbol]
    pca.o(.ARM.exidx.text.Pca_GetCnt) refers to pca.o(.text.Pca_GetCnt) for [Anonymous Symbol]
    pca.o(.text.Pca_SetCnt) refers to ddl.o(.text.GetBit) for GetBit
    pca.o(.text.Pca_SetCnt) refers to pca.o(.text.Pca_StartPca) for Pca_StartPca
    pca.o(.ARM.exidx.text.Pca_SetCnt) refers to pca.o(.text.Pca_SetCnt) for [Anonymous Symbol]
    pca.o(.text.Pca_GetOut) refers to ddl.o(.text.GetBit) for GetBit
    pca.o(.ARM.exidx.text.Pca_GetOut) refers to pca.o(.text.Pca_GetOut) for [Anonymous Symbol]
    pca.o(.ARM.exidx.text.Pca_SetCcap) refers to pca.o(.text.Pca_SetCcap) for [Anonymous Symbol]
    pca.o(.ARM.exidx.text.Pca_GetCcap) refers to pca.o(.text.Pca_GetCcap) for [Anonymous Symbol]
    pca.o(.ARM.exidx.text.Pca_SetCarr) refers to pca.o(.text.Pca_SetCarr) for [Anonymous Symbol]
    pca.o(.ARM.exidx.text.Pca_GetCarr) refers to pca.o(.text.Pca_GetCarr) for [Anonymous Symbol]
    pca.o(.ARM.exidx.text.Pca_SetCcapHL) refers to pca.o(.text.Pca_SetCcapHL) for [Anonymous Symbol]
    pca.o(.ARM.exidx.text.Pca_GetCcapHL) refers to pca.o(.text.Pca_GetCcapHL) for [Anonymous Symbol]
    pcnt.o(.text.Pcnt_Cmd) refers to ddl.o(.text.SetBit) for SetBit
    pcnt.o(.text.Pcnt_Cmd) refers to ddl.o(.text.GetBit) for GetBit
    pcnt.o(.ARM.exidx.text.Pcnt_Cmd) refers to pcnt.o(.text.Pcnt_Cmd) for [Anonymous Symbol]
    pcnt.o(.ARM.exidx.text.Pcnt_SetB2T) refers to pcnt.o(.text.Pcnt_SetB2T) for [Anonymous Symbol]
    pcnt.o(.ARM.exidx.text.Pcnt_SetB2C) refers to pcnt.o(.text.Pcnt_SetB2C) for [Anonymous Symbol]
    pcnt.o(.ARM.exidx.text.Pcnt_SetT2C) refers to pcnt.o(.text.Pcnt_SetT2C) for [Anonymous Symbol]
    pcnt.o(.ARM.exidx.text.Pcnt_SetBuf) refers to pcnt.o(.text.Pcnt_SetBuf) for [Anonymous Symbol]
    pcnt.o(.ARM.exidx.text.Pcnt_Init) refers to pcnt.o(.text.Pcnt_Init) for [Anonymous Symbol]
    pcnt.o(.ARM.exidx.text.Pcnt_ItCfg) refers to pcnt.o(.text.Pcnt_ItCfg) for [Anonymous Symbol]
    pcnt.o(.ARM.exidx.text.Pcnt_GetItStatus) refers to pcnt.o(.text.Pcnt_GetItStatus) for [Anonymous Symbol]
    pcnt.o(.ARM.exidx.text.Pcnt_ClrItStatus) refers to pcnt.o(.text.Pcnt_ClrItStatus) for [Anonymous Symbol]
    pcnt.o(.ARM.exidx.text.Pcnt_GetCnt) refers to pcnt.o(.text.Pcnt_GetCnt) for [Anonymous Symbol]
    pcnt.o(.ARM.exidx.text.Pcnt_GetTop) refers to pcnt.o(.text.Pcnt_GetTop) for [Anonymous Symbol]
    pcnt.o(.ARM.exidx.text.Pcnt_GetBuf) refers to pcnt.o(.text.Pcnt_GetBuf) for [Anonymous Symbol]
    ram.o(.ARM.exidx.text.Ram_ErrAddrGet) refers to ram.o(.text.Ram_ErrAddrGet) for [Anonymous Symbol]
    ram.o(.ARM.exidx.text.Ram_GetIntFlag) refers to ram.o(.text.Ram_GetIntFlag) for [Anonymous Symbol]
    ram.o(.ARM.exidx.text.Ram_ClearIntFlag) refers to ram.o(.text.Ram_ClearIntFlag) for [Anonymous Symbol]
    ram.o(.ARM.exidx.text.Ram_EnableIrq) refers to ram.o(.text.Ram_EnableIrq) for [Anonymous Symbol]
    ram.o(.ARM.exidx.text.Ram_DisableIrq) refers to ram.o(.text.Ram_DisableIrq) for [Anonymous Symbol]
    reset.o(.ARM.exidx.text.Reset_GetFlag) refers to reset.o(.text.Reset_GetFlag) for [Anonymous Symbol]
    reset.o(.ARM.exidx.text.Reset_ClearFlag) refers to reset.o(.text.Reset_ClearFlag) for [Anonymous Symbol]
    reset.o(.ARM.exidx.text.Reset_ClearFlagAll) refers to reset.o(.text.Reset_ClearFlagAll) for [Anonymous Symbol]
    reset.o(.ARM.exidx.text.Reset_RstPeripheralAll) refers to reset.o(.text.Reset_RstPeripheralAll) for [Anonymous Symbol]
    reset.o(.ARM.exidx.text.Reset_RstPeripheral0) refers to reset.o(.text.Reset_RstPeripheral0) for [Anonymous Symbol]
    reset.o(.ARM.exidx.text.Reset_RstPeripheral1) refers to reset.o(.text.Reset_RstPeripheral1) for [Anonymous Symbol]
    rtc.o(.text.Rtc_Cmd) refers to ddl.o(.text.SetBit) for SetBit
    rtc.o(.ARM.exidx.text.Rtc_Cmd) refers to rtc.o(.text.Rtc_Cmd) for [Anonymous Symbol]
    rtc.o(.ARM.exidx.text.Rtc_StartWait) refers to rtc.o(.text.Rtc_StartWait) for [Anonymous Symbol]
    rtc.o(.text.Rtc_Hz1Cmd) refers to ddl.o(.text.SetBit) for SetBit
    rtc.o(.ARM.exidx.text.Rtc_Hz1Cmd) refers to rtc.o(.text.Rtc_Hz1Cmd) for [Anonymous Symbol]
    rtc.o(.ARM.exidx.text.Rtc_SetCyc) refers to rtc.o(.text.Rtc_SetCyc) for [Anonymous Symbol]
    rtc.o(.text.Rtc_AlmIeCmd) refers to ddl.o(.text.SetBit) for SetBit
    rtc.o(.ARM.exidx.text.Rtc_AlmIeCmd) refers to rtc.o(.text.Rtc_AlmIeCmd) for [Anonymous Symbol]
    rtc.o(.text.Rtc_AlmEnCmd) refers to ddl.o(.text.SetBit) for SetBit
    rtc.o(.ARM.exidx.text.Rtc_AlmEnCmd) refers to rtc.o(.text.Rtc_AlmEnCmd) for [Anonymous Symbol]
    rtc.o(.text.Rtc_GetAlmfItStatus) refers to ddl.o(.text.GetBit) for GetBit
    rtc.o(.ARM.exidx.text.Rtc_GetAlmfItStatus) refers to rtc.o(.text.Rtc_GetAlmfItStatus) for [Anonymous Symbol]
    rtc.o(.text.Rtc_ClearAlmfItStatus) refers to ddl.o(.text.SetBit) for SetBit
    rtc.o(.ARM.exidx.text.Rtc_ClearAlmfItStatus) refers to rtc.o(.text.Rtc_ClearAlmfItStatus) for [Anonymous Symbol]
    rtc.o(.text.Rtc_ClearPrdfItStatus) refers to ddl.o(.text.SetBit) for SetBit
    rtc.o(.ARM.exidx.text.Rtc_ClearPrdfItStatus) refers to rtc.o(.text.Rtc_ClearPrdfItStatus) for [Anonymous Symbol]
    rtc.o(.text.Rtc_GetPridItStatus) refers to ddl.o(.text.GetBit) for GetBit
    rtc.o(.ARM.exidx.text.Rtc_GetPridItStatus) refers to rtc.o(.text.Rtc_GetPridItStatus) for [Anonymous Symbol]
    rtc.o(.ARM.exidx.text.Rtc_CompCfg) refers to rtc.o(.text.Rtc_CompCfg) for [Anonymous Symbol]
    rtc.o(.ARM.exidx.text.Check_BCD_Format) refers to rtc.o(.text.Check_BCD_Format) for [Anonymous Symbol]
    rtc.o(.ARM.exidx.text.Rtc_CheckLeapYear) refers to rtc.o(.text.Rtc_CheckLeapYear) for [Anonymous Symbol]
    rtc.o(.text.Get_Month2_Day) refers to rtc.o(.text.Rtc_CheckLeapYear) for Rtc_CheckLeapYear
    rtc.o(.ARM.exidx.text.Get_Month2_Day) refers to rtc.o(.text.Get_Month2_Day) for [Anonymous Symbol]
    rtc.o(.ARM.exidx.text.Rtc_ReadDateTime) refers to rtc.o(.text.Rtc_ReadDateTime) for [Anonymous Symbol]
    rtc.o(.ARM.exidx.text.Rtc_SetTime) refers to rtc.o(.text.Rtc_SetTime) for [Anonymous Symbol]
    rtc.o(.ARM.exidx.text.Rtc_GetAlarmTime) refers to rtc.o(.text.Rtc_GetAlarmTime) for [Anonymous Symbol]
    rtc.o(.text.Rtc_SetAlarmTime) refers to rtc.o(.text.Rtc_AlmEnCmd) for Rtc_AlmEnCmd
    rtc.o(.text.Rtc_SetAlarmTime) refers to rtc.o(.text.Check_BCD_Format) for Check_BCD_Format
    rtc.o(.ARM.exidx.text.Rtc_SetAlarmTime) refers to rtc.o(.text.Rtc_SetAlarmTime) for [Anonymous Symbol]
    rtc.o(.text.Rtc_Init) refers to rtc.o(.text.Rtc_Cmd) for Rtc_Cmd
    rtc.o(.text.Rtc_Init) refers to rtc.o(.text.Rtc_SetCyc) for Rtc_SetCyc
    rtc.o(.text.Rtc_Init) refers to rtc.o(.text.Rtc_CompCfg) for Rtc_CompCfg
    rtc.o(.text.Rtc_Init) refers to rtc.o(.text.Rtc_SetTime) for Rtc_SetTime
    rtc.o(.ARM.exidx.text.Rtc_Init) refers to rtc.o(.text.Rtc_Init) for [Anonymous Symbol]
    spi.o(.ARM.exidx.text.Spi_GetStatus) refers to spi.o(.text.Spi_GetStatus) for [Anonymous Symbol]
    spi.o(.ARM.exidx.text.Spi_ClearStatus) refers to spi.o(.text.Spi_ClearStatus) for [Anonymous Symbol]
    spi.o(.ARM.exidx.text.Spi_IrqEnable) refers to spi.o(.text.Spi_IrqEnable) for [Anonymous Symbol]
    spi.o(.ARM.exidx.text.Spi_IrqDisable) refers to spi.o(.text.Spi_IrqDisable) for [Anonymous Symbol]
    spi.o(.ARM.exidx.text.Spi_FuncEnable) refers to spi.o(.text.Spi_FuncEnable) for [Anonymous Symbol]
    spi.o(.ARM.exidx.text.Spi_FuncDisable) refers to spi.o(.text.Spi_FuncDisable) for [Anonymous Symbol]
    spi.o(.ARM.exidx.text.Spi_Init) refers to spi.o(.text.Spi_Init) for [Anonymous Symbol]
    spi.o(.ARM.exidx.text.Spi_SetCS) refers to spi.o(.text.Spi_SetCS) for [Anonymous Symbol]
    spi.o(.ARM.exidx.text.Spi_SendData) refers to spi.o(.text.Spi_SendData) for [Anonymous Symbol]
    spi.o(.ARM.exidx.text.Spi_RWByte) refers to spi.o(.text.Spi_RWByte) for [Anonymous Symbol]
    spi.o(.ARM.exidx.text.Spi_Slave_DummyWriteData) refers to spi.o(.text.Spi_Slave_DummyWriteData) for [Anonymous Symbol]
    spi.o(.ARM.exidx.text.Spi_SendBuf) refers to spi.o(.text.Spi_SendBuf) for [Anonymous Symbol]
    spi.o(.ARM.exidx.text.Spi_ReceiveData) refers to spi.o(.text.Spi_ReceiveData) for [Anonymous Symbol]
    spi.o(.ARM.exidx.text.Spi_ReceiveBuf) refers to spi.o(.text.Spi_ReceiveBuf) for [Anonymous Symbol]
    sysctrl.o(.text.Sysctrl_ClkSourceEnable) refers to sysctrl.o(.text._SysctrlUnlock) for _SysctrlUnlock
    sysctrl.o(.text.Sysctrl_ClkSourceEnable) refers to ddl.o(.text.delay1ms) for delay1ms
    sysctrl.o(.text.Sysctrl_ClkSourceEnable) refers to ddl.o(.text.delay10us) for delay10us
    sysctrl.o(.ARM.exidx.text.Sysctrl_ClkSourceEnable) refers to sysctrl.o(.text.Sysctrl_ClkSourceEnable) for [Anonymous Symbol]
    sysctrl.o(.ARM.exidx.text._SysctrlUnlock) refers to sysctrl.o(.text._SysctrlUnlock) for [Anonymous Symbol]
    sysctrl.o(.text.Sysctrl_SysClkSwitch) refers to sysctrl.o(.text._SysctrlUnlock) for _SysctrlUnlock
    sysctrl.o(.text.Sysctrl_SysClkSwitch) refers to system_hc32l186.o(.text.SystemCoreClockUpdate) for SystemCoreClockUpdate
    sysctrl.o(.ARM.exidx.text.Sysctrl_SysClkSwitch) refers to sysctrl.o(.text.Sysctrl_SysClkSwitch) for [Anonymous Symbol]
    sysctrl.o(.ARM.exidx.text.Sysctrl_GetHClkFreq) refers to sysctrl.o(.text.Sysctrl_GetHClkFreq) for [Anonymous Symbol]
    sysctrl.o(.text.Sysctrl_GetPClkFreq) refers to sysctrl.o(.text.Sysctrl_GetHClkFreq) for Sysctrl_GetHClkFreq
    sysctrl.o(.ARM.exidx.text.Sysctrl_GetPClkFreq) refers to sysctrl.o(.text.Sysctrl_GetPClkFreq) for [Anonymous Symbol]
    sysctrl.o(.text.Sysctrl_ClkInit) refers to sysctrl.o(.text.Sysctrl_XTHDriverCfg) for Sysctrl_XTHDriverCfg
    sysctrl.o(.text.Sysctrl_ClkInit) refers to sysctrl.o(.text.Sysctrl_SetXTHStableTime) for Sysctrl_SetXTHStableTime
    sysctrl.o(.text.Sysctrl_ClkInit) refers to sysctrl.o(.text.Sysctrl_SetRCLStableTime) for Sysctrl_SetRCLStableTime
    sysctrl.o(.text.Sysctrl_ClkInit) refers to sysctrl.o(.text.Sysctrl_SetPLLStableTime) for Sysctrl_SetPLLStableTime
    sysctrl.o(.text.Sysctrl_ClkInit) refers to sysctrl.o(.text.Sysctrl_XTLDriverCfg) for Sysctrl_XTLDriverCfg
    sysctrl.o(.text.Sysctrl_ClkInit) refers to sysctrl.o(.text.Sysctrl_SetXTLStableTime) for Sysctrl_SetXTLStableTime
    sysctrl.o(.text.Sysctrl_ClkInit) refers to sysctrl.o(.text.Sysctrl_ClkSourceEnable) for Sysctrl_ClkSourceEnable
    sysctrl.o(.text.Sysctrl_ClkInit) refers to sysctrl.o(.text.Sysctrl_SysClkSwitch) for Sysctrl_SysClkSwitch
    sysctrl.o(.text.Sysctrl_ClkInit) refers to sysctrl.o(.text.Sysctrl_SetHCLKDiv) for Sysctrl_SetHCLKDiv
    sysctrl.o(.text.Sysctrl_ClkInit) refers to sysctrl.o(.text.Sysctrl_SetPCLKDiv) for Sysctrl_SetPCLKDiv
    sysctrl.o(.ARM.exidx.text.Sysctrl_ClkInit) refers to sysctrl.o(.text.Sysctrl_ClkInit) for [Anonymous Symbol]
    sysctrl.o(.ARM.exidx.text.Sysctrl_XTHDriverCfg) refers to sysctrl.o(.text.Sysctrl_XTHDriverCfg) for [Anonymous Symbol]
    sysctrl.o(.ARM.exidx.text.Sysctrl_SetXTHStableTime) refers to sysctrl.o(.text.Sysctrl_SetXTHStableTime) for [Anonymous Symbol]
    sysctrl.o(.ARM.exidx.text.Sysctrl_SetRCLStableTime) refers to sysctrl.o(.text.Sysctrl_SetRCLStableTime) for [Anonymous Symbol]
    sysctrl.o(.ARM.exidx.text.Sysctrl_XTLDriverCfg) refers to sysctrl.o(.text.Sysctrl_XTLDriverCfg) for [Anonymous Symbol]
    sysctrl.o(.ARM.exidx.text.Sysctrl_SetXTLStableTime) refers to sysctrl.o(.text.Sysctrl_SetXTLStableTime) for [Anonymous Symbol]
    sysctrl.o(.ARM.exidx.text.Sysctrl_SetPLLStableTime) refers to sysctrl.o(.text.Sysctrl_SetPLLStableTime) for [Anonymous Symbol]
    sysctrl.o(.text.Sysctrl_SetHCLKDiv) refers to sysctrl.o(.text._SysctrlUnlock) for _SysctrlUnlock
    sysctrl.o(.ARM.exidx.text.Sysctrl_SetHCLKDiv) refers to sysctrl.o(.text.Sysctrl_SetHCLKDiv) for [Anonymous Symbol]
    sysctrl.o(.text.Sysctrl_SetPCLKDiv) refers to sysctrl.o(.text._SysctrlUnlock) for _SysctrlUnlock
    sysctrl.o(.ARM.exidx.text.Sysctrl_SetPCLKDiv) refers to sysctrl.o(.text.Sysctrl_SetPCLKDiv) for [Anonymous Symbol]
    sysctrl.o(.text.Sysctrl_ClkDeInit) refers to sysctrl.o(.text.Sysctrl_SetRCHTrim) for Sysctrl_SetRCHTrim
    sysctrl.o(.text.Sysctrl_ClkDeInit) refers to sysctrl.o(.text.Sysctrl_ClkSourceEnable) for Sysctrl_ClkSourceEnable
    sysctrl.o(.text.Sysctrl_ClkDeInit) refers to sysctrl.o(.text.Sysctrl_SysClkSwitch) for Sysctrl_SysClkSwitch
    sysctrl.o(.text.Sysctrl_ClkDeInit) refers to sysctrl.o(.text.Sysctrl_SetHCLKDiv) for Sysctrl_SetHCLKDiv
    sysctrl.o(.text.Sysctrl_ClkDeInit) refers to sysctrl.o(.text.Sysctrl_SetPCLKDiv) for Sysctrl_SetPCLKDiv
    sysctrl.o(.ARM.exidx.text.Sysctrl_ClkDeInit) refers to sysctrl.o(.text.Sysctrl_ClkDeInit) for [Anonymous Symbol]
    sysctrl.o(.ARM.exidx.text.Sysctrl_SetRCHTrim) refers to sysctrl.o(.text.Sysctrl_SetRCHTrim) for [Anonymous Symbol]
    sysctrl.o(.ARM.exidx.text.Sysctrl_SetRCLTrim) refers to sysctrl.o(.text.Sysctrl_SetRCLTrim) for [Anonymous Symbol]
    sysctrl.o(.ARM.exidx.text.Sysctrl_SetRC48MTrim) refers to sysctrl.o(.text.Sysctrl_SetRC48MTrim) for [Anonymous Symbol]
    sysctrl.o(.ARM.exidx.text.Sysctrl_SetXTHFreq) refers to sysctrl.o(.text.Sysctrl_SetXTHFreq) for [Anonymous Symbol]
    sysctrl.o(.ARM.exidx.text.Sysctrl_SetPLLFreq) refers to sysctrl.o(.text.Sysctrl_SetPLLFreq) for [Anonymous Symbol]
    sysctrl.o(.text.Sysctrl_SetPeripheralGate) refers to ddl.o(.text.SetBit) for SetBit
    sysctrl.o(.ARM.exidx.text.Sysctrl_SetPeripheralGate) refers to sysctrl.o(.text.Sysctrl_SetPeripheralGate) for [Anonymous Symbol]
    sysctrl.o(.text.Sysctrl_GetPeripheralGate) refers to ddl.o(.text.GetBit) for GetBit
    sysctrl.o(.ARM.exidx.text.Sysctrl_GetPeripheralGate) refers to sysctrl.o(.text.Sysctrl_GetPeripheralGate) for [Anonymous Symbol]
    sysctrl.o(.text.Sysctrl_SetFunc) refers to sysctrl.o(.text._SysctrlUnlock) for _SysctrlUnlock
    sysctrl.o(.text.Sysctrl_SetFunc) refers to ddl.o(.text.SetBit) for SetBit
    sysctrl.o(.ARM.exidx.text.Sysctrl_SetFunc) refers to sysctrl.o(.text.Sysctrl_SetFunc) for [Anonymous Symbol]
    sysctrl.o(.text.Sysctrl_SetRTCAdjustClkFreq) refers to sysctrl.o(.text._SysctrlUnlock) for _SysctrlUnlock
    sysctrl.o(.ARM.exidx.text.Sysctrl_SetRTCAdjustClkFreq) refers to sysctrl.o(.text.Sysctrl_SetRTCAdjustClkFreq) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_GetIntFlag) refers to timer3.o(.text.Tim3_GetIntFlag) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_ClearIntFlag) refers to timer3.o(.text.Tim3_ClearIntFlag) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_ClearAllIntFlag) refers to timer3.o(.text.Tim3_ClearAllIntFlag) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_Mode0_EnableIrq) refers to timer3.o(.text.Tim3_Mode0_EnableIrq) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_Mode0_DisableIrq) refers to timer3.o(.text.Tim3_Mode0_DisableIrq) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_Mode1_EnableIrq) refers to timer3.o(.text.Tim3_Mode1_EnableIrq) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_Mode1_DisableIrq) refers to timer3.o(.text.Tim3_Mode1_DisableIrq) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_Mode23_EnableIrq) refers to timer3.o(.text.Tim3_Mode23_EnableIrq) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_Mode23_DisableIrq) refers to timer3.o(.text.Tim3_Mode23_DisableIrq) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_Mode0_Init) refers to timer3.o(.text.Tim3_Mode0_Init) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M0_Run) refers to timer3.o(.text.Tim3_M0_Run) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M0_Stop) refers to timer3.o(.text.Tim3_M0_Stop) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M0_Enable_Output) refers to timer3.o(.text.Tim3_M0_Enable_Output) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M0_EnTOG) refers to timer3.o(.text.Tim3_M0_EnTOG) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M0_Cnt16Set) refers to timer3.o(.text.Tim3_M0_Cnt16Set) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M0_Cnt16Get) refers to timer3.o(.text.Tim3_M0_Cnt16Get) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M0_ARRSet) refers to timer3.o(.text.Tim3_M0_ARRSet) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M0_Cnt32Set) refers to timer3.o(.text.Tim3_M0_Cnt32Set) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M0_Cnt32Get) refers to timer3.o(.text.Tim3_M0_Cnt32Get) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_Mode1_Init) refers to timer3.o(.text.Tim3_Mode1_Init) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M1_Input_Cfg) refers to timer3.o(.text.Tim3_M1_Input_Cfg) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M1_PWC_Edge_Sel) refers to timer3.o(.text.Tim3_M1_PWC_Edge_Sel) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M1_Run) refers to timer3.o(.text.Tim3_M1_Run) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M1_Stop) refers to timer3.o(.text.Tim3_M1_Stop) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M1_Cnt16Set) refers to timer3.o(.text.Tim3_M1_Cnt16Set) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M1_Cnt16Get) refers to timer3.o(.text.Tim3_M1_Cnt16Get) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M1_PWC_CapValueGet) refers to timer3.o(.text.Tim3_M1_PWC_CapValueGet) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_Mode23_Init) refers to timer3.o(.text.Tim3_Mode23_Init) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_EnPWM_Output) refers to timer3.o(.text.Tim3_M23_EnPWM_Output) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_Run) refers to timer3.o(.text.Tim3_M23_Run) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_Stop) refers to timer3.o(.text.Tim3_M23_Stop) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_ARRSet) refers to timer3.o(.text.Tim3_M23_ARRSet) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_Cnt16Set) refers to timer3.o(.text.Tim3_M23_Cnt16Set) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_Cnt16Get) refers to timer3.o(.text.Tim3_M23_Cnt16Get) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_CCR_Set) refers to timer3.o(.text.Tim3_M23_CCR_Set) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_CCR_Get) refers to timer3.o(.text.Tim3_M23_CCR_Get) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_GateFuncSel) refers to timer3.o(.text.Tim3_M23_GateFuncSel) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_MasterSlave_Set) refers to timer3.o(.text.Tim3_M23_MasterSlave_Set) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_PortOutput_Cfg) refers to timer3.o(.text.Tim3_M23_PortOutput_Cfg) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_Ch3_Cfg) refers to timer3.o(.text.Tim3_M23_Ch3_Cfg) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_PortInput_Cfg) refers to timer3.o(.text.Tim3_M23_PortInput_Cfg) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_ETRInput_Cfg) refers to timer3.o(.text.Tim3_M23_ETRInput_Cfg) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_BrakeInput_Cfg) refers to timer3.o(.text.Tim3_M23_BrakeInput_Cfg) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_TrigADC_Cfg) refers to timer3.o(.text.Tim3_M23_TrigADC_Cfg) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_DT_Cfg) refers to timer3.o(.text.Tim3_M23_DT_Cfg) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_SetValidPeriod) refers to timer3.o(.text.Tim3_M23_SetValidPeriod) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_OCRefClr) refers to timer3.o(.text.Tim3_M23_OCRefClr) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_EnDMA) refers to timer3.o(.text.Tim3_M23_EnDMA) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_EnSwTrigCapCmpA) refers to timer3.o(.text.Tim3_M23_EnSwTrigCapCmpA) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_EnSwTrigCapCmpB) refers to timer3.o(.text.Tim3_M23_EnSwTrigCapCmpB) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_EnSwUev) refers to timer3.o(.text.Tim3_M23_EnSwUev) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_EnSwTrig) refers to timer3.o(.text.Tim3_M23_EnSwTrig) for [Anonymous Symbol]
    timer3.o(.ARM.exidx.text.Tim3_M23_EnSwBk) refers to timer3.o(.text.Tim3_M23_EnSwBk) for [Anonymous Symbol]
    trim.o(.ARM.exidx.text.Trim_GetIntFlag) refers to trim.o(.text.Trim_GetIntFlag) for [Anonymous Symbol]
    trim.o(.ARM.exidx.text.Trim_ClearIntFlag) refers to trim.o(.text.Trim_ClearIntFlag) for [Anonymous Symbol]
    trim.o(.ARM.exidx.text.Trim_EnableIrq) refers to trim.o(.text.Trim_EnableIrq) for [Anonymous Symbol]
    trim.o(.ARM.exidx.text.Trim_DisableIrq) refers to trim.o(.text.Trim_DisableIrq) for [Anonymous Symbol]
    trim.o(.ARM.exidx.text.Trim_Init) refers to trim.o(.text.Trim_Init) for [Anonymous Symbol]
    trim.o(.ARM.exidx.text.Trim_Run) refers to trim.o(.text.Trim_Run) for [Anonymous Symbol]
    trim.o(.ARM.exidx.text.Trim_Stop) refers to trim.o(.text.Trim_Stop) for [Anonymous Symbol]
    trim.o(.ARM.exidx.text.Trim_RefCntGet) refers to trim.o(.text.Trim_RefCntGet) for [Anonymous Symbol]
    trim.o(.ARM.exidx.text.Trim_CalCntGet) refers to trim.o(.text.Trim_CalCntGet) for [Anonymous Symbol]
    trng.o(.ARM.exidx.text.Trng_Init) refers to trng.o(.text.Trng_Init) for [Anonymous Symbol]
    trng.o(.ARM.exidx.text.Trng_Generate) refers to trng.o(.text.Trng_Generate) for [Anonymous Symbol]
    trng.o(.ARM.exidx.text.Trng_GetData0) refers to trng.o(.text.Trng_GetData0) for [Anonymous Symbol]
    trng.o(.ARM.exidx.text.Trng_GetData1) refers to trng.o(.text.Trng_GetData1) for [Anonymous Symbol]
    uart.o(.text.Uart_EnableIrq) refers to ddl.o(.text.SetBit) for SetBit
    uart.o(.ARM.exidx.text.Uart_EnableIrq) refers to uart.o(.text.Uart_EnableIrq) for [Anonymous Symbol]
    uart.o(.text.Uart_DisableIrq) refers to ddl.o(.text.SetBit) for SetBit
    uart.o(.ARM.exidx.text.Uart_DisableIrq) refers to uart.o(.text.Uart_DisableIrq) for [Anonymous Symbol]
    uart.o(.ARM.exidx.text.Uart_SetMultiMode) refers to uart.o(.text.Uart_SetMultiMode) for [Anonymous Symbol]
    uart.o(.ARM.exidx.text.Uart_HdModeEnable) refers to uart.o(.text.Uart_HdModeEnable) for [Anonymous Symbol]
    uart.o(.ARM.exidx.text.Uart_HdModeDisable) refers to uart.o(.text.Uart_HdModeDisable) for [Anonymous Symbol]
    uart.o(.ARM.exidx.text.Uart_SetTb8) refers to uart.o(.text.Uart_SetTb8) for [Anonymous Symbol]
    uart.o(.ARM.exidx.text.Uart_GetRb8) refers to uart.o(.text.Uart_GetRb8) for [Anonymous Symbol]
    uart.o(.ARM.exidx.text.Uart_SetSaddr) refers to uart.o(.text.Uart_SetSaddr) for [Anonymous Symbol]
    uart.o(.text.Uart_EnableFunc) refers to ddl.o(.text.SetBit) for SetBit
    uart.o(.ARM.exidx.text.Uart_EnableFunc) refers to uart.o(.text.Uart_EnableFunc) for [Anonymous Symbol]
    uart.o(.text.Uart_DisableFunc) refers to ddl.o(.text.SetBit) for SetBit
    uart.o(.ARM.exidx.text.Uart_DisableFunc) refers to uart.o(.text.Uart_DisableFunc) for [Anonymous Symbol]
    uart.o(.ARM.exidx.text.Uart_GetIsr) refers to uart.o(.text.Uart_GetIsr) for [Anonymous Symbol]
    uart.o(.text.Uart_GetStatus) refers to ddl.o(.text.GetBit) for GetBit
    uart.o(.ARM.exidx.text.Uart_GetStatus) refers to uart.o(.text.Uart_GetStatus) for [Anonymous Symbol]
    uart.o(.ARM.exidx.text.Uart_ClrIsr) refers to uart.o(.text.Uart_ClrIsr) for [Anonymous Symbol]
    uart.o(.text.Uart_ClrStatus) refers to ddl.o(.text.SetBit) for SetBit
    uart.o(.ARM.exidx.text.Uart_ClrStatus) refers to uart.o(.text.Uart_ClrStatus) for [Anonymous Symbol]
    uart.o(.text.Uart_SendDataPoll) refers to uart.o(.text.Uart_GetStatus) for Uart_GetStatus
    uart.o(.text.Uart_SendDataPoll) refers to uart.o(.text.Uart_ClrStatus) for Uart_ClrStatus
    uart.o(.ARM.exidx.text.Uart_SendDataPoll) refers to uart.o(.text.Uart_SendDataPoll) for [Anonymous Symbol]
    uart.o(.text.Uart_SendDataPollTimeOut) refers to uart.o(.text.Uart_GetStatus) for Uart_GetStatus
    uart.o(.text.Uart_SendDataPollTimeOut) refers to uart.o(.text.Uart_ClrStatus) for Uart_ClrStatus
    uart.o(.ARM.exidx.text.Uart_SendDataPollTimeOut) refers to uart.o(.text.Uart_SendDataPollTimeOut) for [Anonymous Symbol]
    uart.o(.ARM.exidx.text.Uart_SendDataIt) refers to uart.o(.text.Uart_SendDataIt) for [Anonymous Symbol]
    uart.o(.ARM.exidx.text.Uart_ReceiveData) refers to uart.o(.text.Uart_ReceiveData) for [Anonymous Symbol]
    uart.o(.text.Uart_Init) refers to ffltui.o(.text) for __aeabi_ui2f
    uart.o(.text.Uart_Init) refers to fdiv.o(.text) for __aeabi_fdiv
    uart.o(.text.Uart_Init) refers to fadd.o(.text) for __aeabi_fadd
    uart.o(.text.Uart_Init) refers to ffixi.o(.text) for __aeabi_f2iz
    uart.o(.text.Uart_Init) refers to uart.o(.text.Uart_EnableFunc) for Uart_EnableFunc
    uart.o(.ARM.exidx.text.Uart_Init) refers to uart.o(.text.Uart_Init) for [Anonymous Symbol]
    vc.o(.ARM.exidx.text.Vc_CfgItType) refers to vc.o(.text.Vc_CfgItType) for [Anonymous Symbol]
    vc.o(.text.Vc_ItCfg) refers to ddl.o(.text.SetBit) for SetBit
    vc.o(.ARM.exidx.text.Vc_ItCfg) refers to vc.o(.text.Vc_ItCfg) for [Anonymous Symbol]
    vc.o(.text.Vc_GetItStatus) refers to ddl.o(.text.GetBit) for GetBit
    vc.o(.ARM.exidx.text.Vc_GetItStatus) refers to vc.o(.text.Vc_GetItStatus) for [Anonymous Symbol]
    vc.o(.text.Vc_ClearItStatus) refers to ddl.o(.text.SetBit) for SetBit
    vc.o(.ARM.exidx.text.Vc_ClearItStatus) refers to vc.o(.text.Vc_ClearItStatus) for [Anonymous Symbol]
    vc.o(.ARM.exidx.text.Vc_DivInit) refers to vc.o(.text.Vc_DivInit) for [Anonymous Symbol]
    vc.o(.ARM.exidx.text.Vc_Init) refers to vc.o(.text.Vc_Init) for [Anonymous Symbol]
    vc.o(.text.Vc_Cmd) refers to ddl.o(.text.SetBit) for SetBit
    vc.o(.ARM.exidx.text.Vc_Cmd) refers to vc.o(.text.Vc_Cmd) for [Anonymous Symbol]
    wdt.o(.ARM.exidx.text.Wdt_WriteWdtLoad) refers to wdt.o(.text.Wdt_WriteWdtLoad) for [Anonymous Symbol]
    wdt.o(.text.Wdt_Init) refers to wdt.o(.text.Wdt_WriteWdtLoad) for Wdt_WriteWdtLoad
    wdt.o(.ARM.exidx.text.Wdt_Init) refers to wdt.o(.text.Wdt_Init) for [Anonymous Symbol]
    wdt.o(.ARM.exidx.text.Wdt_Start) refers to wdt.o(.text.Wdt_Start) for [Anonymous Symbol]
    wdt.o(.ARM.exidx.text.Wdt_Feed) refers to wdt.o(.text.Wdt_Feed) for [Anonymous Symbol]
    wdt.o(.ARM.exidx.text.Wdt_IrqClr) refers to wdt.o(.text.Wdt_IrqClr) for [Anonymous Symbol]
    wdt.o(.ARM.exidx.text.Wdt_ReadWdtValue) refers to wdt.o(.text.Wdt_ReadWdtValue) for [Anonymous Symbol]
    wdt.o(.ARM.exidx.text.Wdt_ReadwdtStatus) refers to wdt.o(.text.Wdt_ReadwdtStatus) for [Anonymous Symbol]
    wdt.o(.ARM.exidx.text.Wdt_GetIrqStatus) refers to wdt.o(.text.Wdt_GetIrqStatus) for [Anonymous Symbol]
    wwdt.o(.ARM.exidx.text.WWDT_Init) refers to wwdt.o(.text.WWDT_Init) for [Anonymous Symbol]
    wwdt.o(.ARM.exidx.text.WWDT_Start) refers to wwdt.o(.text.WWDT_Start) for [Anonymous Symbol]
    wwdt.o(.ARM.exidx.text.WWDT_Feed) refers to wwdt.o(.text.WWDT_Feed) for [Anonymous Symbol]
    wwdt.o(.ARM.exidx.text.WWDT_ClearPreOverFlag) refers to wwdt.o(.text.WWDT_ClearPreOverFlag) for [Anonymous Symbol]
    wwdt.o(.ARM.exidx.text.WWDT_GetPreOverFlag) refers to wwdt.o(.text.WWDT_GetPreOverFlag) for [Anonymous Symbol]
    wwdt.o(.ARM.exidx.text.WWDT_GetRunFlag) refers to wwdt.o(.text.WWDT_GetRunFlag) for [Anonymous Symbol]
    wwdt.o(.ARM.exidx.text.WWDT_GetCnt) refers to wwdt.o(.text.WWDT_GetCnt) for [Anonymous Symbol]
    wwdt.o(.ARM.exidx.text.WWDT_Reset) refers to wwdt.o(.text.WWDT_Reset) for [Anonymous Symbol]
    singledampermodule.o(.text.Init_SingleDamper) refers to singledampermodule.o(.text.Init_SingleDamperParm) for Init_SingleDamperParm
    singledampermodule.o(.ARM.exidx.text.Init_SingleDamper) refers to singledampermodule.o(.text.Init_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.text.Init_SingleDamperParm) refers to steppermotorlib.o(.text.Init_StepperMotor) for Init_StepperMotor
    singledampermodule.o(.text.Init_SingleDamperParm) refers to singledampermodule.o(.bss.ary_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.text.Init_SingleDamperParm) refers to singledampermodule.o(.text.Config_IO_SingleDamperID0) for Config_IO_SingleDamperID0
    singledampermodule.o(.text.Init_SingleDamperParm) refers to singledampermodule.o(.rodata.st_SingleDamperConPara) for [Anonymous Symbol]
    singledampermodule.o(.ARM.exidx.text.Init_SingleDamperParm) refers to singledampermodule.o(.text.Init_SingleDamperParm) for [Anonymous Symbol]
    singledampermodule.o(.text.Execute_SingleDamperDriver) refers to singledampermodule.o(.bss.ary_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.ARM.exidx.text.Execute_SingleDamperDriver) refers to singledampermodule.o(.text.Execute_SingleDamperDriver) for [Anonymous Symbol]
    singledampermodule.o(.text.Execute_SingleDamperControl) refers to singledampermodule.o(.text.Control_SingleDamper) for Control_SingleDamper
    singledampermodule.o(.text.Execute_SingleDamperControl) refers to singledampermodule.o(.text.Antifreeze_SingleDamper) for Antifreeze_SingleDamper
    singledampermodule.o(.text.Execute_SingleDamperControl) refers to singledampermodule.o(.bss.ary_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.ARM.exidx.text.Execute_SingleDamperControl) refers to singledampermodule.o(.text.Execute_SingleDamperControl) for [Anonymous Symbol]
    singledampermodule.o(.text.Control_SingleDamper) refers to systemtimermodule.o(.text.Get_SecondElapsedTime) for Get_SecondElapsedTime
    singledampermodule.o(.text.Control_SingleDamper) refers to systemtimermodule.o(.text.Get_SecondCount) for Get_SecondCount
    singledampermodule.o(.text.Control_SingleDamper) refers to singledampermodule.o(.rodata.ary_SingleDamper_RunSteps) for [Anonymous Symbol]
    singledampermodule.o(.ARM.exidx.text.Control_SingleDamper) refers to singledampermodule.o(.text.Control_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.text.Antifreeze_SingleDamper) refers to systemtimermodule.o(.text.Get_SecondElapsedTime) for Get_SecondElapsedTime
    singledampermodule.o(.text.Antifreeze_SingleDamper) refers to systemtimermodule.o(.text.Get_SecondCount) for Get_SecondCount
    singledampermodule.o(.ARM.exidx.text.Antifreeze_SingleDamper) refers to singledampermodule.o(.text.Antifreeze_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.text.Reset_SingleDamper) refers to singledampermodule.o(.bss.ary_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.ARM.exidx.text.Reset_SingleDamper) refers to singledampermodule.o(.text.Reset_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.text.Force_SingleDamperAction) refers to singledampermodule.o(.bss.ary_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.ARM.exidx.text.Force_SingleDamperAction) refers to singledampermodule.o(.text.Force_SingleDamperAction) for [Anonymous Symbol]
    singledampermodule.o(.text.Force_SingleDamperState) refers to singledampermodule.o(.bss.ary_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.ARM.exidx.text.Force_SingleDamperState) refers to singledampermodule.o(.text.Force_SingleDamperState) for [Anonymous Symbol]
    singledampermodule.o(.text.Set_SingleDamperState) refers to singledampermodule.o(.bss.ary_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.ARM.exidx.text.Set_SingleDamperState) refers to singledampermodule.o(.text.Set_SingleDamperState) for [Anonymous Symbol]
    singledampermodule.o(.text.Pause_SingleDamperAction) refers to systemtimermodule.o(.text.Get_SecondCount) for Get_SecondCount
    singledampermodule.o(.text.Pause_SingleDamperAction) refers to singledampermodule.o(.bss.ary_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.ARM.exidx.text.Pause_SingleDamperAction) refers to singledampermodule.o(.text.Pause_SingleDamperAction) for [Anonymous Symbol]
    singledampermodule.o(.text.Get_SingleDamperResetState) refers to singledampermodule.o(.bss.ary_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.ARM.exidx.text.Get_SingleDamperResetState) refers to singledampermodule.o(.text.Get_SingleDamperResetState) for [Anonymous Symbol]
    singledampermodule.o(.text.Get_SingleDamperPauseState) refers to singledampermodule.o(.bss.ary_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.ARM.exidx.text.Get_SingleDamperPauseState) refers to singledampermodule.o(.text.Get_SingleDamperPauseState) for [Anonymous Symbol]
    singledampermodule.o(.text.Get_SingleDamperRunningState) refers to singledampermodule.o(.bss.ary_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.ARM.exidx.text.Get_SingleDamperRunningState) refers to singledampermodule.o(.text.Get_SingleDamperRunningState) for [Anonymous Symbol]
    singledampermodule.o(.text.Get_SingleDamperState) refers to singledampermodule.o(.bss.ary_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.ARM.exidx.text.Get_SingleDamperState) refers to singledampermodule.o(.text.Get_SingleDamperState) for [Anonymous Symbol]
    singledampermodule.o(.text.Get_SingleDamperHoldTimer) refers to systemtimermodule.o(.text.Get_SecondElapsedTime) for Get_SecondElapsedTime
    singledampermodule.o(.text.Get_SingleDamperHoldTimer) refers to singledampermodule.o(.bss.ary_SingleDamper) for [Anonymous Symbol]
    singledampermodule.o(.ARM.exidx.text.Get_SingleDamperHoldTimer) refers to singledampermodule.o(.text.Get_SingleDamperHoldTimer) for [Anonymous Symbol]
    singledampermodule.o(.text.Config_IO_SingleDamperID0) refers to gpio.o(.text.Gpio_ClrIO) for Gpio_ClrIO
    singledampermodule.o(.text.Config_IO_SingleDamperID0) refers to gpio.o(.text.Gpio_SetIO) for Gpio_SetIO
    singledampermodule.o(.ARM.exidx.text.Config_IO_SingleDamperID0) refers to singledampermodule.o(.text.Config_IO_SingleDamperID0) for [Anonymous Symbol]
    systemtimermodule.o(.text.Init_SystemTimer) refers to systemtimermodule.o(.bss.st_SystemTimer) for st_SystemTimer
    systemtimermodule.o(.text.Init_SystemTimer) refers to systemtimermodule.o(.rodata.Api) for [Anonymous Symbol]
    systemtimermodule.o(.ARM.exidx.text.Init_SystemTimer) refers to systemtimermodule.o(.text.Init_SystemTimer) for [Anonymous Symbol]
    systemtimermodule.o(.text.Get_DayCount) refers to systemtimermodule.o(.bss.st_SystemTimer) for st_SystemTimer
    systemtimermodule.o(.ARM.exidx.text.Get_DayCount) refers to systemtimermodule.o(.text.Get_DayCount) for [Anonymous Symbol]
    systemtimermodule.o(.text.Get_HourCount) refers to systemtimermodule.o(.bss.st_SystemTimer) for st_SystemTimer
    systemtimermodule.o(.ARM.exidx.text.Get_HourCount) refers to systemtimermodule.o(.text.Get_HourCount) for [Anonymous Symbol]
    systemtimermodule.o(.text.Get_MinuteCount) refers to systemtimermodule.o(.bss.st_SystemTimer) for st_SystemTimer
    systemtimermodule.o(.ARM.exidx.text.Get_MinuteCount) refers to systemtimermodule.o(.text.Get_MinuteCount) for [Anonymous Symbol]
    systemtimermodule.o(.text.Get_SecondCount) refers to systemtimermodule.o(.bss.st_SystemTimer) for st_SystemTimer
    systemtimermodule.o(.ARM.exidx.text.Get_SecondCount) refers to systemtimermodule.o(.text.Get_SecondCount) for [Anonymous Symbol]
    systemtimermodule.o(.text.Get_MSecCount) refers to systemtimermodule.o(.bss.st_SystemTimer) for st_SystemTimer
    systemtimermodule.o(.ARM.exidx.text.Get_MSecCount) refers to systemtimermodule.o(.text.Get_MSecCount) for [Anonymous Symbol]
    systemtimermodule.o(.text.Get_DayElapsedTime) refers to systemtimermodule.o(.bss.st_SystemTimer) for st_SystemTimer
    systemtimermodule.o(.ARM.exidx.text.Get_DayElapsedTime) refers to systemtimermodule.o(.text.Get_DayElapsedTime) for [Anonymous Symbol]
    systemtimermodule.o(.text.Get_HourElapsedTime) refers to systemtimermodule.o(.bss.st_SystemTimer) for st_SystemTimer
    systemtimermodule.o(.ARM.exidx.text.Get_HourElapsedTime) refers to systemtimermodule.o(.text.Get_HourElapsedTime) for [Anonymous Symbol]
    systemtimermodule.o(.text.Get_MinuteElapsedTime) refers to systemtimermodule.o(.bss.st_SystemTimer) for st_SystemTimer
    systemtimermodule.o(.ARM.exidx.text.Get_MinuteElapsedTime) refers to systemtimermodule.o(.text.Get_MinuteElapsedTime) for [Anonymous Symbol]
    systemtimermodule.o(.text.Get_SecondElapsedTime) refers to systemtimermodule.o(.bss.st_SystemTimer) for st_SystemTimer
    systemtimermodule.o(.ARM.exidx.text.Get_SecondElapsedTime) refers to systemtimermodule.o(.text.Get_SecondElapsedTime) for [Anonymous Symbol]
    systemtimermodule.o(.text.Get_MSecElapsedTime) refers to systemtimermodule.o(.bss.st_SystemTimer) for st_SystemTimer
    systemtimermodule.o(.ARM.exidx.text.Get_MSecElapsedTime) refers to systemtimermodule.o(.text.Get_MSecElapsedTime) for [Anonymous Symbol]
    systemtimermodule.o(.text.Shorten_Timer) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    systemtimermodule.o(.text.Shorten_Timer) refers to systemtimermodule.o(.bss.st_SystemTimer) for st_SystemTimer
    systemtimermodule.o(.ARM.exidx.text.Shorten_Timer) refers to systemtimermodule.o(.text.Shorten_Timer) for [Anonymous Symbol]
    systemtimermodule.o(.text.Add_MSecCount) refers to systemtimermodule.o(.bss.st_SystemTimer) for st_SystemTimer
    systemtimermodule.o(.ARM.exidx.text.Add_MSecCount) refers to systemtimermodule.o(.text.Add_MSecCount) for [Anonymous Symbol]
    systemtimermodule.o(.rodata.Api) refers to systemtimermodule.o(.text.Get_DayCount) for Get_DayCount
    systemtimermodule.o(.rodata.Api) refers to systemtimermodule.o(.text.Get_HourCount) for Get_HourCount
    systemtimermodule.o(.rodata.Api) refers to systemtimermodule.o(.text.Get_MinuteCount) for Get_MinuteCount
    systemtimermodule.o(.rodata.Api) refers to systemtimermodule.o(.text.Get_SecondCount) for Get_SecondCount
    systemtimermodule.o(.rodata.Api) refers to systemtimermodule.o(.text.Get_MSecCount) for Get_MSecCount
    systemtimermodule.o(.rodata.Api) refers to systemtimermodule.o(.text.Get_DayElapsedTime) for Get_DayElapsedTime
    systemtimermodule.o(.rodata.Api) refers to systemtimermodule.o(.text.Get_HourElapsedTime) for Get_HourElapsedTime
    systemtimermodule.o(.rodata.Api) refers to systemtimermodule.o(.text.Get_MinuteElapsedTime) for Get_MinuteElapsedTime
    systemtimermodule.o(.rodata.Api) refers to systemtimermodule.o(.text.Get_SecondElapsedTime) for Get_SecondElapsedTime
    systemtimermodule.o(.rodata.Api) refers to systemtimermodule.o(.text.Get_MSecElapsedTime) for Get_MSecElapsedTime
    systemtimermodule.o(.rodata.Api) refers to systemtimermodule.o(.text.Shorten_Timer) for Shorten_Timer
    systemtimermodule.o(.rodata.Api) refers to systemtimermodule.o(.text.Add_MSecCount) for Add_MSecCount
    main.o(.text.main) refers to init_mcu.o(.text.Init_Mcu) for Init_Mcu
    main.o(.text.main) refers to singledampermodule.o(.text.Init_SingleDamper) for Init_SingleDamper
    main.o(.text.main) refers to systemtimermodule.o(.text.Get_SecondCount) for Get_SecondCount
    main.o(.text.main) refers to singledampermodule.o(.text.Reset_SingleDamper) for Reset_SingleDamper
    main.o(.text.main) refers to systemtimermodule.o(.text.Get_SecondElapsedTime) for Get_SecondElapsedTime
    main.o(.text.main) refers to singledampermodule.o(.text.Execute_SingleDamperControl) for Execute_SingleDamperControl
    main.o(.text.main) refers to singledampermodule.o(.text.Set_SingleDamperState) for Set_SingleDamperState
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    startup_hc32l186.o(RESET) refers to startup_hc32l186.o(STACK) for __initial_sp
    startup_hc32l186.o(RESET) refers to startup_hc32l186.o(.text) for Reset_Handler
    startup_hc32l186.o(RESET) refers to cmb_fault.o(.text) for HardFault_Handler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.SysTick_Handler) for SysTick_Handler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.PORTA_IRQHandler) for PORTA_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.PORTB_IRQHandler) for PORTB_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.PORTC_E_IRQHandler) for PORTC_E_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.PORTD_F_IRQHandler) for PORTD_F_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.DMAC_IRQHandler) for DMAC_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.UART0_2_IRQHandler) for UART0_2_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.UART1_3_IRQHandler) for UART1_3_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.LPUART0_IRQHandler) for LPUART0_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.LPUART1_IRQHandler) for LPUART1_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.SPI0_IRQHandler) for SPI0_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.SPI1_IRQHandler) for SPI1_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.I2C0_IRQHandler) for I2C0_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.I2C1_IRQHandler) for I2C1_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.TIM0_IRQHandler) for TIM0_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.TIM1_IRQHandler) for TIM1_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.LPTIM0_1_IRQHandler) for LPTIM0_1_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.TIM5_IRQHandler) for TIM5_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.TIM6_IRQHandler) for TIM6_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.PCA_WWDT_IRQHandler) for PCA_WWDT_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.WDT_IRQHandler) for WDT_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.RTC_IRQHandler) for RTC_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.ADC_DAC_IRQHandler) for ADC_DAC_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.PCNT_IRQHandler) for PCNT_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.VC0_IRQHandler) for VC0_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.VC1_2_IRQHandler) for VC1_2_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.LVD_IRQHandler) for LVD_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.LCD_IRQHandler) for LCD_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.FLASH_RAM_IRQHandler) for FLASH_RAM_IRQHandler
    startup_hc32l186.o(RESET) refers to interrupts_hc32l186.o(.text.CLKTRIM_CTRIM_IRQHandler) for CLKTRIM_CTRIM_IRQHandler
    startup_hc32l186.o(.text) refers to system_hc32l186.o(.text.SystemInit) for SystemInit
    startup_hc32l186.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    ramlog.o(.text.fputc) refers to ramlog.o(.text.ramlog_putc) for ramlog_putc
    ramlog.o(.text.fputc) refers to ramlog.o(.text.ramlog_puts) for ramlog_puts
    ramlog.o(.ARM.exidx.text.fputc) refers to ramlog.o(.text.fputc) for [Anonymous Symbol]
    ramlog.o(.ARM.exidx.text.ramlog_putc) refers to ramlog.o(.text.ramlog_putc) for [Anonymous Symbol]
    ramlog.o(.text.ramlog_puts) refers to ramlog.o(.text.ramlog_putc) for ramlog_putc
    ramlog.o(.ARM.exidx.text.ramlog_puts) refers to ramlog.o(.text.ramlog_puts) for [Anonymous Symbol]
    ramlog.o(.ARM.exidx.text.Panic) refers to ramlog.o(.text.Panic) for [Anonymous Symbol]
    ramlog.o(.text.Debug_Init) refers to cm_backtrace.o(.text.cm_backtrace_init) for cm_backtrace_init
    ramlog.o(.text.Debug_Init) refers to ramlog.o(.text.ramlog_init) for ramlog_init
    ramlog.o(.text.Debug_Init) refers to printf8.o(i.__0printf$8) for __2printf
    ramlog.o(.text.Debug_Init) refers to ramlog.o(.rodata.str1.1) for [Anonymous Symbol]
    ramlog.o(.ARM.exidx.text.Debug_Init) refers to ramlog.o(.text.Debug_Init) for [Anonymous Symbol]
    ramlog.o(.text.ramlog_init) refers to memseta.o(.text) for __aeabi_memclr8
    ramlog.o(.ARM.exidx.text.ramlog_init) refers to ramlog.o(.text.ramlog_init) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_init) refers to strncpy.o(.text) for strncpy
    cm_backtrace.o(.text.cm_backtrace_init) refers to printf8.o(i.__0printf$8) for __2printf
    cm_backtrace.o(.text.cm_backtrace_init) refers to puts.o(i.puts) for puts
    cm_backtrace.o(.text.cm_backtrace_init) refers to cm_backtrace.o(.bss.fw_name) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_init) refers to cm_backtrace.o(.bss.hw_ver) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_init) refers to cm_backtrace.o(.bss.sw_ver) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_init) refers to cm_backtrace.o(.bss.main_stack_size) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_init) refers to cm_backtrace.o(.bss.main_stack_start_addr) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_init) refers to cm_backtrace.o(.bss.code_start_addr) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_init) refers to cm_backtrace.o(.bss.code_size) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_init) refers to cm_backtrace.o(.bss.init_ok) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_init) refers to cm_backtrace.o(.rodata.str1.1) for [Anonymous Symbol]
    cm_backtrace.o(.ARM.exidx.text.cm_backtrace_init) refers to cm_backtrace.o(.text.cm_backtrace_init) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_firmware_info) refers to printf8.o(i.__0printf$8) for __2printf
    cm_backtrace.o(.text.cm_backtrace_firmware_info) refers to puts.o(i.puts) for puts
    cm_backtrace.o(.text.cm_backtrace_firmware_info) refers to cm_backtrace.o(.bss.fw_name) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_firmware_info) refers to cm_backtrace.o(.bss.hw_ver) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_firmware_info) refers to cm_backtrace.o(.bss.sw_ver) for [Anonymous Symbol]
    cm_backtrace.o(.ARM.exidx.text.cm_backtrace_firmware_info) refers to cm_backtrace.o(.text.cm_backtrace_firmware_info) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_call_stack_any) refers to cm_backtrace.o(.text.disassembly_ins_is_bl_blx) for disassembly_ins_is_bl_blx
    cm_backtrace.o(.text.cm_backtrace_call_stack_any) refers to cm_backtrace.o(.bss.code_start_addr) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_call_stack_any) refers to cm_backtrace.o(.bss.code_size) for [Anonymous Symbol]
    cm_backtrace.o(.ARM.exidx.text.cm_backtrace_call_stack_any) refers to cm_backtrace.o(.text.cm_backtrace_call_stack_any) for [Anonymous Symbol]
    cm_backtrace.o(.ARM.exidx.text.disassembly_ins_is_bl_blx) refers to cm_backtrace.o(.text.disassembly_ins_is_bl_blx) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_call_stack) refers to cm_backtrace.o(.text.disassembly_ins_is_bl_blx) for disassembly_ins_is_bl_blx
    cm_backtrace.o(.text.cm_backtrace_call_stack) refers to cm_backtrace.o(.bss.main_stack_size) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_call_stack) refers to cm_backtrace.o(.bss.main_stack_start_addr) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_call_stack) refers to cm_backtrace.o(.bss.on_fault) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_call_stack) refers to cm_backtrace.o(.bss.stack_is_overflow) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_call_stack) refers to cm_backtrace.o(.bss.regs.0.6) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_call_stack) refers to cm_backtrace.o(.bss.code_start_addr) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_call_stack) refers to cm_backtrace.o(.bss.regs.0.5) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_call_stack) refers to cm_backtrace.o(.bss.code_size) for [Anonymous Symbol]
    cm_backtrace.o(.ARM.exidx.text.cm_backtrace_call_stack) refers to cm_backtrace.o(.text.cm_backtrace_call_stack) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_assert) refers to puts.o(i.puts) for puts
    cm_backtrace.o(.text.cm_backtrace_assert) refers to cm_backtrace.o(.text.cm_backtrace_firmware_info) for cm_backtrace_firmware_info
    cm_backtrace.o(.text.cm_backtrace_assert) refers to cm_backtrace.o(.text.dump_stack) for dump_stack
    cm_backtrace.o(.text.cm_backtrace_assert) refers to cm_backtrace.o(.text.print_call_stack) for print_call_stack
    cm_backtrace.o(.text.cm_backtrace_assert) refers to printf8.o(i.__0printf$8) for __2printf
    cm_backtrace.o(.text.cm_backtrace_assert) refers to cm_backtrace.o(.bss.init_ok) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_assert) refers to cm_backtrace.o(.rodata.str1.1) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_assert) refers to cm_backtrace.o(.bss.main_stack_start_addr) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_assert) refers to cm_backtrace.o(.bss.main_stack_size) for [Anonymous Symbol]
    cm_backtrace.o(.ARM.exidx.text.cm_backtrace_assert) refers to cm_backtrace.o(.text.cm_backtrace_assert) for [Anonymous Symbol]
    cm_backtrace.o(.text.dump_stack) refers to printf8.o(i.__0printf$8) for __2printf
    cm_backtrace.o(.text.dump_stack) refers to puts.o(i.puts) for puts
    cm_backtrace.o(.text.dump_stack) refers to cm_backtrace.o(.bss.stack_is_overflow) for [Anonymous Symbol]
    cm_backtrace.o(.text.dump_stack) refers to cm_backtrace.o(.rodata.str1.1) for [Anonymous Symbol]
    cm_backtrace.o(.ARM.exidx.text.dump_stack) refers to cm_backtrace.o(.text.dump_stack) for [Anonymous Symbol]
    cm_backtrace.o(.text.print_call_stack) refers to memseta.o(.text) for __aeabi_memclr4
    cm_backtrace.o(.text.print_call_stack) refers to cm_backtrace.o(.text.cm_backtrace_call_stack) for cm_backtrace_call_stack
    cm_backtrace.o(.text.print_call_stack) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    cm_backtrace.o(.text.print_call_stack) refers to printf8.o(i.__0printf$8) for __2printf
    cm_backtrace.o(.text.print_call_stack) refers to puts.o(i.puts) for puts
    cm_backtrace.o(.text.print_call_stack) refers to cm_backtrace.o(.bss.call_stack_info) for [Anonymous Symbol]
    cm_backtrace.o(.text.print_call_stack) refers to cm_backtrace.o(.bss.fw_name) for [Anonymous Symbol]
    cm_backtrace.o(.text.print_call_stack) refers to cm_backtrace.o(.rodata.str1.1) for [Anonymous Symbol]
    cm_backtrace.o(.ARM.exidx.text.print_call_stack) refers to cm_backtrace.o(.text.print_call_stack) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_fault) refers to printf8.o(i.__0printf$8) for __2printf
    cm_backtrace.o(.text.cm_backtrace_fault) refers to puts.o(i.puts) for puts
    cm_backtrace.o(.text.cm_backtrace_fault) refers to cm_backtrace.o(.text.cm_backtrace_firmware_info) for cm_backtrace_firmware_info
    cm_backtrace.o(.text.cm_backtrace_fault) refers to cm_backtrace.o(.text.dump_stack) for dump_stack
    cm_backtrace.o(.text.cm_backtrace_fault) refers to cm_backtrace.o(.text.print_call_stack) for print_call_stack
    cm_backtrace.o(.text.cm_backtrace_fault) refers to adpt_iwdg.o(.text.software_reset) for software_reset
    cm_backtrace.o(.text.cm_backtrace_fault) refers to cm_backtrace.o(.bss.main_stack_size) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_fault) refers to cm_backtrace.o(.bss.main_stack_start_addr) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_fault) refers to cm_backtrace.o(.bss.init_ok) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_fault) refers to cm_backtrace.o(.rodata.str1.1) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_fault) refers to cm_backtrace.o(.bss.on_fault) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_fault) refers to cm_backtrace.o(.bss.stack_is_overflow) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_fault) refers to cm_backtrace.o(.bss.regs.0.7.0) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_fault) refers to cm_backtrace.o(.bss.regs.0.6) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_fault) refers to cm_backtrace.o(.bss.regs.0.5) for [Anonymous Symbol]
    cm_backtrace.o(.text.cm_backtrace_fault) refers to cm_backtrace.o(.bss.regs.0.4) for [Anonymous Symbol]
    cm_backtrace.o(.ARM.exidx.text.cm_backtrace_fault) refers to cm_backtrace.o(.text.cm_backtrace_fault) for [Anonymous Symbol]
    cmb_fault.o(.text) refers to cm_backtrace.o(.text.cm_backtrace_fault) for cm_backtrace_fault
    steppermotorlib.o(.ARM.exidx.text.__aeabi_assert) refers to steppermotorlib.o(.text.__aeabi_assert) for [Anonymous Symbol]
    steppermotorlib.o(.text.Init_StepperMotor) refers to steppermotorlib.o(.rodata.Api) for [Anonymous Symbol]
    steppermotorlib.o(.ARM.exidx.text.Init_StepperMotor) refers to steppermotorlib.o(.text.Init_StepperMotor) for [Anonymous Symbol]
    steppermotorlib.o(.text.Drive_StepperMotorISR) refers to steppermotorlib.o(.rodata.ary_StepperMotorStepState) for [Anonymous Symbol]
    steppermotorlib.o(.ARM.exidx.text.Drive_StepperMotorISR) refers to steppermotorlib.o(.text.Drive_StepperMotorISR) for [Anonymous Symbol]
    steppermotorlib.o(.ARM.exidx.text.Set_StepperMotorTartget) refers to steppermotorlib.o(.text.Set_StepperMotorTartget) for [Anonymous Symbol]
    steppermotorlib.o(.ARM.exidx.text.Stop_StepperMotorAction) refers to steppermotorlib.o(.text.Stop_StepperMotorAction) for [Anonymous Symbol]
    steppermotorlib.o(.ARM.exidx.text.Pause_StepperMotorAction) refers to steppermotorlib.o(.text.Pause_StepperMotorAction) for [Anonymous Symbol]
    steppermotorlib.o(.ARM.exidx.text.Get_StepperMotorIsRunning) refers to steppermotorlib.o(.text.Get_StepperMotorIsRunning) for [Anonymous Symbol]
    steppermotorlib.o(.ARM.exidx.text.Get_StepperMotorIsPausing) refers to steppermotorlib.o(.text.Get_StepperMotorIsPausing) for [Anonymous Symbol]
    steppermotorlib.o(.ARM.exidx.text.Get_StepperMotorRunSteps) refers to steppermotorlib.o(.text.Get_StepperMotorRunSteps) for [Anonymous Symbol]
    steppermotorlib.o(.ARM.exidx.text.Get_StepperMotorRemainingSteps) refers to steppermotorlib.o(.text.Get_StepperMotorRemainingSteps) for [Anonymous Symbol]
    steppermotorlib.o(.rodata.Api) refers to steppermotorlib.o(.text.Drive_StepperMotorISR) for Drive_StepperMotorISR
    steppermotorlib.o(.rodata.Api) refers to steppermotorlib.o(.text.Set_StepperMotorTartget) for Set_StepperMotorTartget
    steppermotorlib.o(.rodata.Api) refers to steppermotorlib.o(.text.Stop_StepperMotorAction) for Stop_StepperMotorAction
    steppermotorlib.o(.rodata.Api) refers to steppermotorlib.o(.text.Pause_StepperMotorAction) for Pause_StepperMotorAction
    steppermotorlib.o(.rodata.Api) refers to steppermotorlib.o(.text.Get_StepperMotorIsRunning) for Get_StepperMotorIsRunning
    steppermotorlib.o(.rodata.Api) refers to steppermotorlib.o(.text.Get_StepperMotorIsPausing) for Get_StepperMotorIsPausing
    steppermotorlib.o(.rodata.Api) refers to steppermotorlib.o(.text.Get_StepperMotorRunSteps) for Get_StepperMotorRunSteps
    steppermotorlib.o(.rodata.Api) refers to steppermotorlib.o(.text.Get_StepperMotorRemainingSteps) for Get_StepperMotorRemainingSteps
    steppermotorlib.o(.rodata.ary_StepperMotorStepState) refers to steppermotorlib.o(.rodata.ary_IO_State_Lb1909m4Beats) for [Anonymous Symbol]
    steppermotorlib.o(.rodata.ary_StepperMotorStepState) refers to steppermotorlib.o(.rodata.ary_IO_State_Lb1205m4Beats) for [Anonymous Symbol]
    steppermotorlib.o(.rodata.ary_StepperMotorStepState) refers to steppermotorlib.o(.rodata.ary_IO_State_Lb1205m8Beats) for [Anonymous Symbol]
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    puts.o(i.puts) refers to ramlog.o(.text.fputc) for fputc
    puts.o(i.puts) refers to stdout.o(.data) for __stdout
    puts_e.o(.text) refers to ramlog.o(.text.fputc) for fputc
    puts_e.o(.text) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to ramlog.o(.text.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to ramlog.o(.text.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to ramlog.o(.text.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to ramlog.o(.text.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to ramlog.o(.text.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to ramlog.o(.text.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to ramlog.o(.text.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to ramlog.o(.text.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to ramlog.o(.text.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to ramlog.o(.text.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to ramlog.o(.text.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to ramlog.o(.text.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to ramlog.o(.text.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to ramlog.o(.text.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to ramlog.o(.text.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to ramlog.o(.text.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to ramlog.o(.text.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to ramlog.o(.text.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to ramlog.o(.text.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to ramlog.o(.text.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to ramlog.o(.text.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to ramlog.o(.text.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to ramlog.o(.text.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to ramlog.o(.text.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to ramlog.o(.text.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to ramlog.o(.text.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to ramlog.o(.text.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to ramlog.o(.text.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to ramlog.o(.text.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to ramlog.o(.text.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to ramlog.o(.text.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to ramlog.o(.text.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf6.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to ramlog.o(.text.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to ramlog.o(.text.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to ramlog.o(.text.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to ramlog.o(.text.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to ramlog.o(.text.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to ramlog.o(.text.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to ramlog.o(.text.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to ramlog.o(.text.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to ramlog.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to ramlog.o(.text.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to ramlog.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to ramlog.o(.text.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_hc32l186.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_hc32l186.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to system_hc32l186.o(.text.$Sub$$main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to system_hc32l186.o(.text.$Sub$$main) for main
    idiv_div0.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing adpt_adc.o(.text), (0 bytes).
    Removing adpt_adc.o(.text.App_AdcInit), (64 bytes).
    Removing adpt_adc.o(.ARM.exidx.text.App_AdcInit), (8 bytes).
    Removing adpt_adc.o(.text.App_AdcSQRCfg), (134 bytes).
    Removing adpt_adc.o(.ARM.exidx.text.App_AdcSQRCfg), (8 bytes).
    Removing adpt_adc.o(.ARM.exidx.text.Adc_IRQHandler), (8 bytes).
    Removing adpt_adc.o(.text.Board_InitAdc), (12 bytes).
    Removing adpt_adc.o(.ARM.exidx.text.Board_InitAdc), (8 bytes).
    Removing adpt_adc.o(.text.ADC_Restart), (8 bytes).
    Removing adpt_adc.o(.ARM.exidx.text.ADC_Restart), (8 bytes).
    Removing adpt_adc.o(.text.ADC_GetResult), (16 bytes).
    Removing adpt_adc.o(.ARM.exidx.text.ADC_GetResult), (8 bytes).
    Removing adpt_clock.o(.text), (0 bytes).
    Removing adpt_clock.o(.ARM.exidx.text.App_SystemClkInit_RC48M), (8 bytes).
    Removing adpt_clock.o(.ARM.exidx.text.Board_InitClock), (8 bytes).
    Removing adpt_gpio.o(.text), (0 bytes).
    Removing adpt_gpio.o(.ARM.exidx.text.Board_InitPins), (8 bytes).
    Removing adpt_gpio.o(.text.Board_InitSwdGpio), (76 bytes).
    Removing adpt_gpio.o(.ARM.exidx.text.Board_InitSwdGpio), (8 bytes).
    Removing adpt_iwdg.o(.text), (0 bytes).
    Removing adpt_iwdg.o(.ARM.exidx.text.Wdt_IRQHandler), (8 bytes).
    Removing adpt_iwdg.o(.text.Board_InitIwdg), (28 bytes).
    Removing adpt_iwdg.o(.ARM.exidx.text.Board_InitIwdg), (8 bytes).
    Removing adpt_iwdg.o(.text.App_WdtInit), (36 bytes).
    Removing adpt_iwdg.o(.ARM.exidx.text.App_WdtInit), (8 bytes).
    Removing adpt_iwdg.o(.text.IWDG_Refesh), (8 bytes).
    Removing adpt_iwdg.o(.ARM.exidx.text.IWDG_Refesh), (8 bytes).
    Removing adpt_iwdg.o(.ARM.exidx.text.software_reset), (8 bytes).
    Removing adpt_iwdg.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing adpt_pwm.o(.text), (0 bytes).
    Removing adpt_pwm.o(.ARM.exidx.text.AdtTimer3Init), (8 bytes).
    Removing adpt_pwm.o(.ARM.exidx.text.Board_InitTim), (8 bytes).
    Removing adpt_pwm.o(.ARM.exidx.text.AdtTimer2Init), (8 bytes).
    Removing adpt_timebase.o(.text), (0 bytes).
    Removing adpt_timebase.o(.ARM.exidx.text.Time0Init), (8 bytes).
    Removing adpt_timebase.o(.ARM.exidx.text.Tim0_IRQHandler), (8 bytes).
    Removing adpt_timebase.o(.ARM.exidx.text.SysTick_IRQHandler), (8 bytes).
    Removing adpt_timebase.o(.ARM.exidx.text.Board_InitSysTick), (8 bytes).
    Removing adpt_timebase.o(.ARM.exidx.text.SysTick_Config), (8 bytes).
    Removing adpt_timebase.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing adpt_reset.o(.text), (0 bytes).
    Removing adpt_reset.o(.ARM.exidx.text.Board_InitReset), (8 bytes).
    Removing adpt_reset.o(.text.Get_ResetFlag), (12 bytes).
    Removing adpt_reset.o(.ARM.exidx.text.Get_ResetFlag), (8 bytes).
    Removing adpt_flash.o(.text), (0 bytes).
    Removing adpt_flash.o(.text.Board_InitFlash), (12 bytes).
    Removing adpt_flash.o(.ARM.exidx.text.Board_InitFlash), (8 bytes).
    Removing adpt_flash.o(.text.FlashSectorNumber), (14 bytes).
    Removing adpt_flash.o(.ARM.exidx.text.FlashSectorNumber), (8 bytes).
    Removing adpt_flash.o(.text.FlashReadBytes), (18 bytes).
    Removing adpt_flash.o(.ARM.exidx.text.FlashReadBytes), (8 bytes).
    Removing adpt_flash.o(.text.FlashWriteBytes), (26 bytes).
    Removing adpt_flash.o(.ARM.exidx.text.FlashWriteBytes), (8 bytes).
    Removing adpt_flash.o(.text.FlashWriteHalfWords), (26 bytes).
    Removing adpt_flash.o(.ARM.exidx.text.FlashWriteHalfWords), (8 bytes).
    Removing adpt_flash.o(.text.FlashWriteWords), (26 bytes).
    Removing adpt_flash.o(.ARM.exidx.text.FlashWriteWords), (8 bytes).
    Removing adpt_flash.o(.text.FlashSectorErase), (32 bytes).
    Removing adpt_flash.o(.ARM.exidx.text.FlashSectorErase), (8 bytes).
    Removing adpt_flash.o(.text.FlashContWriteBytes), (26 bytes).
    Removing adpt_flash.o(.ARM.exidx.text.FlashContWriteBytes), (8 bytes).
    Removing adpt_flash.o(.text.FlashContWriteHalfWords), (26 bytes).
    Removing adpt_flash.o(.ARM.exidx.text.FlashContWriteHalfWords), (8 bytes).
    Removing adpt_flash.o(.text.FlashContWriteWords), (26 bytes).
    Removing adpt_flash.o(.ARM.exidx.text.FlashContWriteWords), (8 bytes).
    Removing adpt_flash.o(.text.FlashEraseAll), (8 bytes).
    Removing adpt_flash.o(.ARM.exidx.text.FlashEraseAll), (8 bytes).
    Removing system_hc32l186.o(.text), (0 bytes).
    Removing system_hc32l186.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing system_hc32l186.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_hc32l186.o(.ARM.exidx.text.$Sub$$main), (8 bytes).
    Removing interrupts_hc32l186.o(.text), (0 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.EnableNvic), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.__NVIC_DisableIRQ), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.PORTA_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.PORTB_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.PORTC_E_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.PORTD_F_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.DMAC_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.TIM3_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.UART0_2_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.UART1_3_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.LPUART0_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.LPUART1_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.SPI0_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.SPI1_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.I2C0_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.I2C1_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.TIM0_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.TIM1_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.TIM2_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.LPTIM0_1_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.TIM4_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.TIM5_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.TIM6_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.PCA_WWDT_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.WDT_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.RTC_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.ADC_DAC_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.PCNT_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.VC0_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.VC1_2_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.LVD_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.LCD_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.FLASH_RAM_IRQHandler), (8 bytes).
    Removing interrupts_hc32l186.o(.ARM.exidx.text.CLKTRIM_CTRIM_IRQHandler), (8 bytes).
    Removing init_mcu.o(.text), (0 bytes).
    Removing init_mcu.o(.ARM.exidx.text.Init_Mcu), (8 bytes).
    Removing timebase.o(.text), (0 bytes).
    Removing timebase.o(.ARM.exidx.text.ISR_Timer_1ms), (8 bytes).
    Removing timebase.o(.ARM.exidx.text.ISR_Timer_500us), (8 bytes).
    Removing timebase.o(.text.Get_SystemMillisec), (12 bytes).
    Removing timebase.o(.ARM.exidx.text.Get_SystemMillisec), (8 bytes).
    Removing adc.o(.text), (0 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_GetIrqStatus), (8 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_ClrIrqStatus), (8 bytes).
    Removing adc.o(.text.Adc_EnableIrq), (20 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_EnableIrq), (8 bytes).
    Removing adc.o(.text.Adc_DisableIrq), (20 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_DisableIrq), (8 bytes).
    Removing adc.o(.text.Adc_Init), (96 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_Init), (8 bytes).
    Removing adc.o(.text.Adc_SglExtTrigCfg), (28 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_SglExtTrigCfg), (8 bytes).
    Removing adc.o(.text.Adc_SqrExtTrigCfg), (28 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_SqrExtTrigCfg), (8 bytes).
    Removing adc.o(.text.Adc_JqrExtTrigCfg), (28 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_JqrExtTrigCfg), (8 bytes).
    Removing adc.o(.text.Adc_SGL_Start), (12 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_SGL_Start), (8 bytes).
    Removing adc.o(.text.Adc_SGL_Stop), (12 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_SGL_Stop), (8 bytes).
    Removing adc.o(.text.Adc_SGL_Always_Start), (12 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_SGL_Always_Start), (8 bytes).
    Removing adc.o(.text.Adc_SGL_Always_Stop), (12 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_SGL_Always_Stop), (8 bytes).
    Removing adc.o(.text.Adc_SQR_Start), (12 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_SQR_Start), (8 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_SQR_Stop), (8 bytes).
    Removing adc.o(.text.Adc_JQR_Start), (12 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_JQR_Start), (8 bytes).
    Removing adc.o(.text.Adc_JQR_Stop), (12 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_JQR_Stop), (8 bytes).
    Removing adc.o(.text.Adc_Enable), (16 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_Enable), (8 bytes).
    Removing adc.o(.text.Adc_Disable), (16 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_Disable), (8 bytes).
    Removing adc.o(.text.Adc_SqrModeCfg), (88 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_SqrModeCfg), (8 bytes).
    Removing adc.o(.text.Adc_JqrModeCfg), (64 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_JqrModeCfg), (8 bytes).
    Removing adc.o(.text.Adc_CfgSglChannel), (28 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_CfgSglChannel), (8 bytes).
    Removing adc.o(.text.Adc_CfgSqrChannel), (352 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_CfgSqrChannel), (8 bytes).
    Removing adc.o(.text.Adc_CfgJqrChannel), (100 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_CfgJqrChannel), (8 bytes).
    Removing adc.o(.text.Adc_GetSglResult), (12 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_GetSglResult), (8 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_GetSqrResult), (8 bytes).
    Removing adc.o(.text.Adc_GetJqrResult), (12 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_GetJqrResult), (8 bytes).
    Removing adc.o(.text.Adc_GetAccResult), (12 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_GetAccResult), (8 bytes).
    Removing adc.o(.text.Adc_ClrAccResult), (20 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_ClrAccResult), (8 bytes).
    Removing adc.o(.text.Adc_ThresholdCfg), (92 bytes).
    Removing adc.o(.ARM.exidx.text.Adc_ThresholdCfg), (8 bytes).
    Removing adt.o(.text), (0 bytes).
    Removing adt.o(.text.Adt_CfgIrq), (22 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_CfgIrq), (8 bytes).
    Removing adt.o(.text.Adt_GetIrqFlag), (12 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_GetIrqFlag), (8 bytes).
    Removing adt.o(.text.Adt_ClearIrqFlag), (16 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_ClearIrqFlag), (8 bytes).
    Removing adt.o(.text.Adt_ClearAllIrqFlag), (12 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_ClearAllIrqFlag), (8 bytes).
    Removing adt.o(.text.Adt_CfgHwCntUp), (24 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_CfgHwCntUp), (8 bytes).
    Removing adt.o(.text.Adt_ClearHwCntUp), (10 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_ClearHwCntUp), (8 bytes).
    Removing adt.o(.text.Adt_CfgHwCntDwn), (24 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_CfgHwCntDwn), (8 bytes).
    Removing adt.o(.text.Adt_ClearHwCntDwn), (10 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_ClearHwCntDwn), (8 bytes).
    Removing adt.o(.text.Adt_CfgHwStart), (22 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_CfgHwStart), (8 bytes).
    Removing adt.o(.text.Adt_ClearHwStart), (8 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_ClearHwStart), (8 bytes).
    Removing adt.o(.text.Adt_EnableHwStart), (14 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_EnableHwStart), (8 bytes).
    Removing adt.o(.text.Adt_DisableHwStart), (14 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_DisableHwStart), (8 bytes).
    Removing adt.o(.text.Adt_CfgHwStop), (22 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_CfgHwStop), (8 bytes).
    Removing adt.o(.text.Adt_ClearHwStop), (8 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_ClearHwStop), (8 bytes).
    Removing adt.o(.text.Adt_EnableHwStop), (14 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_EnableHwStop), (8 bytes).
    Removing adt.o(.text.Adt_DisableHwStop), (14 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_DisableHwStop), (8 bytes).
    Removing adt.o(.text.Adt_CfgHwClear), (22 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_CfgHwClear), (8 bytes).
    Removing adt.o(.text.Adt_ClearHwClear), (8 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_ClearHwClear), (8 bytes).
    Removing adt.o(.text.Adt_EnableHwClear), (14 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_EnableHwClear), (8 bytes).
    Removing adt.o(.text.Adt_DisableHwClear), (14 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_DisableHwClear), (8 bytes).
    Removing adt.o(.text.Adt_CfgHwCaptureA), (34 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_CfgHwCaptureA), (8 bytes).
    Removing adt.o(.text.Adt_ClearHwCaptureA), (10 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_ClearHwCaptureA), (8 bytes).
    Removing adt.o(.text.Adt_CfgHwCaptureB), (34 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_CfgHwCaptureB), (8 bytes).
    Removing adt.o(.text.Adt_ClearHwCaptureB), (10 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_ClearHwCaptureB), (8 bytes).
    Removing adt.o(.text.Adt_SwSyncStart), (44 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_SwSyncStart), (8 bytes).
    Removing adt.o(.text.Adt_SwSyncStop), (44 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_SwSyncStop), (8 bytes).
    Removing adt.o(.text.Adt_SwSyncClear), (44 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_SwSyncClear), (8 bytes).
    Removing adt.o(.text.Adt_GetSwSyncState), (44 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_GetSwSyncState), (8 bytes).
    Removing adt.o(.text.Adt_AosTrigCfg), (84 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_AosTrigCfg), (8 bytes).
    Removing adt.o(.text.Adt_IrqTrigCfg), (274 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_IrqTrigCfg), (8 bytes).
    Removing adt.o(.text.Adt_PortTrigCfg), (268 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_PortTrigCfg), (8 bytes).
    Removing adt.o(.text.Adt_CHxXPortCfg), (388 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_CHxXPortCfg), (8 bytes).
    Removing adt.o(.text.Adt_EnableBrakePort), (44 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_EnableBrakePort), (8 bytes).
    Removing adt.o(.text.Adt_ClearBrakePort), (12 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_ClearBrakePort), (8 bytes).
    Removing adt.o(.text.Adt_Disable3Cfg), (92 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_Disable3Cfg), (8 bytes).
    Removing adt.o(.text.Adt_SwBrake), (24 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_SwBrake), (8 bytes).
    Removing adt.o(.text.Adt_GetPortBrakeFlag), (16 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_GetPortBrakeFlag), (8 bytes).
    Removing adt.o(.text.Adt_ClearPortBrakeFlag), (16 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_ClearPortBrakeFlag), (8 bytes).
    Removing adt.o(.text.Adt_Disable1Cfg), (120 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_Disable1Cfg), (8 bytes).
    Removing adt.o(.text.Adt_GetSameBrakeFlag), (16 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_GetSameBrakeFlag), (8 bytes).
    Removing adt.o(.text.Adt_ClearSameBrakeFlag), (16 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_ClearSameBrakeFlag), (8 bytes).
    Removing adt.o(.text.Adt_PwmDitherCfg), (68 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_PwmDitherCfg), (8 bytes).
    Removing adt.o(.text.Adt_Init), (68 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_Init), (8 bytes).
    Removing adt.o(.text.Adt_DeInit), (108 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_DeInit), (8 bytes).
    Removing adt.o(.text.Adt_StartCount), (12 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_StartCount), (8 bytes).
    Removing adt.o(.text.Adt_StopCount), (12 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_StopCount), (8 bytes).
    Removing adt.o(.text.Adt_SetCount), (20 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_SetCount), (8 bytes).
    Removing adt.o(.text.Adt_GetCount), (6 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_GetCount), (8 bytes).
    Removing adt.o(.text.Adt_ClearCount), (16 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_ClearCount), (8 bytes).
    Removing adt.o(.text.Adt_GetVperNum), (8 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_GetVperNum), (8 bytes).
    Removing adt.o(.text.Adt_GetState), (10 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_GetState), (8 bytes).
    Removing adt.o(.text.Adt_SetPeriod), (6 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_SetPeriod), (8 bytes).
    Removing adt.o(.text.Adt_SetPeriodBuf), (16 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_SetPeriodBuf), (8 bytes).
    Removing adt.o(.text.Adt_ClearPeriodBuf), (18 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_ClearPeriodBuf), (8 bytes).
    Removing adt.o(.text.Adt_SetValidPeriod), (112 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_SetValidPeriod), (8 bytes).
    Removing adt.o(.text.Adt_SetCompareValue), (56 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_SetCompareValue), (8 bytes).
    Removing adt.o(.text.Adt_SetSpecilCompareValue), (36 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_SetSpecilCompareValue), (8 bytes).
    Removing adt.o(.text.Adt_EnableValueBuf), (44 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_EnableValueBuf), (8 bytes).
    Removing adt.o(.text.Adt_ClearValueBuf), (26 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_ClearValueBuf), (8 bytes).
    Removing adt.o(.text.Adt_GetCaptureValue), (26 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_GetCaptureValue), (8 bytes).
    Removing adt.o(.text.Adt_GetCaptureBuf), (26 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_GetCaptureBuf), (8 bytes).
    Removing adt.o(.text.Adt_SetDTUA), (6 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_SetDTUA), (8 bytes).
    Removing adt.o(.text.Adt_SetDTDA), (6 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_SetDTDA), (8 bytes).
    Removing adt.o(.text.Adt_CfgDT), (32 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_CfgDT), (8 bytes).
    Removing adt.o(.text.Adt_CfgZMask), (66 bytes).
    Removing adt.o(.ARM.exidx.text.Adt_CfgZMask), (8 bytes).
    Removing aes.o(.text), (0 bytes).
    Removing aes.o(.text.AES_Encrypt), (172 bytes).
    Removing aes.o(.ARM.exidx.text.AES_Encrypt), (8 bytes).
    Removing aes.o(.text.AES_Decrypt), (172 bytes).
    Removing aes.o(.ARM.exidx.text.AES_Decrypt), (8 bytes).
    Removing bgr.o(.text), (0 bytes).
    Removing bgr.o(.text.Bgr_BgrEnable), (24 bytes).
    Removing bgr.o(.ARM.exidx.text.Bgr_BgrEnable), (8 bytes).
    Removing bgr.o(.text.Bgr_BgrDisable), (16 bytes).
    Removing bgr.o(.ARM.exidx.text.Bgr_BgrDisable), (8 bytes).
    Removing bgr.o(.text.Bgr_TempSensorEnable), (24 bytes).
    Removing bgr.o(.ARM.exidx.text.Bgr_TempSensorEnable), (8 bytes).
    Removing bgr.o(.text.Bgr_TempSensorDisable), (16 bytes).
    Removing bgr.o(.ARM.exidx.text.Bgr_TempSensorDisable), (8 bytes).
    Removing bt.o(.text), (0 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_GetIntFlag), (8 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_ClearIntFlag), (8 bytes).
    Removing bt.o(.text.Bt_ClearAllIntFlag), (16 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_ClearAllIntFlag), (8 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_Mode0_EnableIrq), (8 bytes).
    Removing bt.o(.text.Bt_Mode0_DisableIrq), (24 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_Mode0_DisableIrq), (8 bytes).
    Removing bt.o(.text.Bt_Mode1_EnableIrq), (48 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_Mode1_EnableIrq), (8 bytes).
    Removing bt.o(.text.Bt_Mode1_DisableIrq), (52 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_Mode1_DisableIrq), (8 bytes).
    Removing bt.o(.text.Bt_Mode23_EnableIrq), (156 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_Mode23_EnableIrq), (8 bytes).
    Removing bt.o(.text.Bt_Mode23_DisableIrq), (180 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_Mode23_DisableIrq), (8 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_Mode0_Init), (8 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M0_Run), (8 bytes).
    Removing bt.o(.text.Bt_M0_Stop), (20 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M0_Stop), (8 bytes).
    Removing bt.o(.text.Bt_M0_EnTOG_Output), (28 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M0_EnTOG_Output), (8 bytes).
    Removing bt.o(.text.Bt_M0_Enable_Output), (32 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M0_Enable_Output), (8 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M0_Cnt16Set), (8 bytes).
    Removing bt.o(.text.Bt_M0_Cnt16Get), (16 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M0_Cnt16Get), (8 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M0_ARRSet), (8 bytes).
    Removing bt.o(.text.Bt_M0_Cnt32Set), (16 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M0_Cnt32Set), (8 bytes).
    Removing bt.o(.text.Bt_M0_Cnt32Get), (12 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M0_Cnt32Get), (8 bytes).
    Removing bt.o(.text.Bt_Mode1_Init), (84 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_Mode1_Init), (8 bytes).
    Removing bt.o(.text.Bt_M1_Input_Cfg), (160 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M1_Input_Cfg), (8 bytes).
    Removing bt.o(.text.Bt_M1_PWC_Edge_Sel), (112 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M1_PWC_Edge_Sel), (8 bytes).
    Removing bt.o(.text.Bt_M1_Run), (20 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M1_Run), (8 bytes).
    Removing bt.o(.text.Bt_M1_Stop), (20 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M1_Stop), (8 bytes).
    Removing bt.o(.text.Bt_M1_Cnt16Set), (28 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M1_Cnt16Set), (8 bytes).
    Removing bt.o(.text.Bt_M1_Cnt16Get), (16 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M1_Cnt16Get), (8 bytes).
    Removing bt.o(.text.Bt_M1_PWC_CapValueGet), (16 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M1_PWC_CapValueGet), (8 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_Mode23_Init), (8 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_EnPWM_Output), (8 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_Run), (8 bytes).
    Removing bt.o(.text.Bt_M23_Stop), (20 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_Stop), (8 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_ARRSet), (8 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_Cnt16Set), (8 bytes).
    Removing bt.o(.text.Bt_M23_Cnt16Get), (16 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_Cnt16Get), (8 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_CCR_Set), (8 bytes).
    Removing bt.o(.text.Bt_M23_CCR_Get), (36 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_CCR_Get), (8 bytes).
    Removing bt.o(.text.Bt_M23_GateFuncSel), (64 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_GateFuncSel), (8 bytes).
    Removing bt.o(.text.Bt_M23_MasterSlave_Set), (76 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_MasterSlave_Set), (8 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_PortOutput_Cfg), (8 bytes).
    Removing bt.o(.text.Bt_M23_PortInput_Cfg), (128 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_PortInput_Cfg), (8 bytes).
    Removing bt.o(.text.Bt_M23_ETRInput_Cfg), (48 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_ETRInput_Cfg), (8 bytes).
    Removing bt.o(.text.Bt_M23_BrakeInput_Cfg), (156 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_BrakeInput_Cfg), (8 bytes).
    Removing bt.o(.text.Bt_M23_TrigADC_Cfg), (76 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_TrigADC_Cfg), (8 bytes).
    Removing bt.o(.text.Bt_M23_DT_Cfg), (44 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_DT_Cfg), (8 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_SetValidPeriod), (8 bytes).
    Removing bt.o(.text.Bt_M23_OCRefClr), (48 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_OCRefClr), (8 bytes).
    Removing bt.o(.text.Bt_M23_EnDMA), (112 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_EnDMA), (8 bytes).
    Removing bt.o(.text.Bt_M23_EnSwTrigCapCmpA), (24 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_EnSwTrigCapCmpA), (8 bytes).
    Removing bt.o(.text.Bt_M23_EnSwTrigCapCmpB), (24 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_EnSwTrigCapCmpB), (8 bytes).
    Removing bt.o(.text.Bt_M23_EnSwUev), (24 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_EnSwUev), (8 bytes).
    Removing bt.o(.text.Bt_M23_EnSwTrig), (24 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_EnSwTrig), (8 bytes).
    Removing bt.o(.text.Bt_M23_EnSwBk), (24 bytes).
    Removing bt.o(.ARM.exidx.text.Bt_M23_EnSwBk), (8 bytes).
    Removing crc.o(.text), (0 bytes).
    Removing crc.o(.text.CRC16_Get8), (52 bytes).
    Removing crc.o(.ARM.exidx.text.CRC16_Get8), (8 bytes).
    Removing crc.o(.text.CRC16_Get16), (56 bytes).
    Removing crc.o(.ARM.exidx.text.CRC16_Get16), (8 bytes).
    Removing crc.o(.text.CRC16_Get32), (56 bytes).
    Removing crc.o(.ARM.exidx.text.CRC16_Get32), (8 bytes).
    Removing crc.o(.text.CRC16_Check8), (64 bytes).
    Removing crc.o(.ARM.exidx.text.CRC16_Check8), (8 bytes).
    Removing crc.o(.text.CRC16_Check16), (60 bytes).
    Removing crc.o(.ARM.exidx.text.CRC16_Check16), (8 bytes).
    Removing crc.o(.text.CRC16_Check32), (56 bytes).
    Removing crc.o(.ARM.exidx.text.CRC16_Check32), (8 bytes).
    Removing crc.o(.text.CRC32_Get8), (48 bytes).
    Removing crc.o(.ARM.exidx.text.CRC32_Get8), (8 bytes).
    Removing crc.o(.text.CRC32_Get16), (52 bytes).
    Removing crc.o(.ARM.exidx.text.CRC32_Get16), (8 bytes).
    Removing crc.o(.text.CRC32_Get32), (52 bytes).
    Removing crc.o(.ARM.exidx.text.CRC32_Get32), (8 bytes).
    Removing crc.o(.text.CRC32_Check8), (68 bytes).
    Removing crc.o(.ARM.exidx.text.CRC32_Check8), (8 bytes).
    Removing crc.o(.text.CRC32_Check16), (60 bytes).
    Removing crc.o(.ARM.exidx.text.CRC32_Check16), (8 bytes).
    Removing crc.o(.text.CRC32_Check32), (56 bytes).
    Removing crc.o(.ARM.exidx.text.CRC32_Check32), (8 bytes).
    Removing ctrim.o(.text), (0 bytes).
    Removing ctrim.o(.text.CTRIM_CaliInit), (92 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_CaliInit), (8 bytes).
    Removing ctrim.o(.text.CTRIM_TimerInit), (60 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_TimerInit), (8 bytes).
    Removing ctrim.o(.text.CTRIM_GetCounter), (12 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_GetCounter), (8 bytes).
    Removing ctrim.o(.text.CTRIM_SetAutoReload), (24 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_SetAutoReload), (8 bytes).
    Removing ctrim.o(.text.CTRIM_GetAutoReload), (12 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_GetAutoReload), (8 bytes).
    Removing ctrim.o(.text.CTRIM_SetInitialStep), (20 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_SetInitialStep), (8 bytes).
    Removing ctrim.o(.text.CTRIM_GetInitialStep), (16 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_GetInitialStep), (8 bytes).
    Removing ctrim.o(.text.CTRIM_SetEtrClockFilter), (20 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_SetEtrClockFilter), (8 bytes).
    Removing ctrim.o(.text.CTRIM_GetEtrClockFilter), (16 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_GetEtrClockFilter), (8 bytes).
    Removing ctrim.o(.text.CTRIM_SetAccurateClock), (20 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_SetAccurateClock), (8 bytes).
    Removing ctrim.o(.text.CTRIM_GetAccurateClock), (16 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_GetAccurateClock), (8 bytes).
    Removing ctrim.o(.text.CTRIM_SetMode), (20 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_SetMode), (8 bytes).
    Removing ctrim.o(.text.CTRIM_GetMode), (16 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_GetMode), (8 bytes).
    Removing ctrim.o(.text.CTRIM_SetRCHTrimBits), (20 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_SetRCHTrimBits), (8 bytes).
    Removing ctrim.o(.text.CTRIM_GetRCHTrimBits), (16 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_GetRCHTrimBits), (8 bytes).
    Removing ctrim.o(.text.CTRIM_SetRefClockDiv), (20 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_SetRefClockDiv), (8 bytes).
    Removing ctrim.o(.text.CTRIM_GetRefClockDiv), (16 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_GetRefClockDiv), (8 bytes).
    Removing ctrim.o(.text.CTRIM_SetOneShot), (20 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_SetOneShot), (8 bytes).
    Removing ctrim.o(.text.CTRIM_GetOneShot), (16 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_GetOneShot), (8 bytes).
    Removing ctrim.o(.text.CTRIM_GetARRCoutDir), (16 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_GetARRCoutDir), (8 bytes).
    Removing ctrim.o(.text.CTRIM_GetTrimCode), (12 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_GetTrimCode), (8 bytes).
    Removing ctrim.o(.text.CTRIM_GetCountErrorCapture), (12 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_GetCountErrorCapture), (8 bytes).
    Removing ctrim.o(.text.CTRIM_SetCountErrorLimit), (24 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_SetCountErrorLimit), (8 bytes).
    Removing ctrim.o(.text.CTRIM_GetCountErrorLimit), (20 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_GetCountErrorLimit), (8 bytes).
    Removing ctrim.o(.text.CTRIM_Enable), (16 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_Enable), (8 bytes).
    Removing ctrim.o(.text.CTRIM_Disable), (16 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_Disable), (8 bytes).
    Removing ctrim.o(.text.CTRIM_IsEnable), (16 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_IsEnable), (8 bytes).
    Removing ctrim.o(.text.CTRIM_EnableIT), (16 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_EnableIT), (8 bytes).
    Removing ctrim.o(.text.CTRIM_DisableIT), (16 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_DisableIT), (8 bytes).
    Removing ctrim.o(.text.CTRIM_IsEnableIT), (20 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_IsEnableIT), (8 bytes).
    Removing ctrim.o(.text.CTRIM_IsActiveFlag), (20 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_IsActiveFlag), (8 bytes).
    Removing ctrim.o(.text.CTRIM_ClearFlag), (16 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_ClearFlag), (8 bytes).
    Removing ctrim.o(.text.CTRIM_ClearFlag_ALL), (12 bytes).
    Removing ctrim.o(.ARM.exidx.text.CTRIM_ClearFlag_ALL), (8 bytes).
    Removing dac.o(.text), (0 bytes).
    Removing dac.o(.text.Dac_DmaCmd), (20 bytes).
    Removing dac.o(.ARM.exidx.text.Dac_DmaCmd), (8 bytes).
    Removing dac.o(.text.Dac_DmaITCfg), (20 bytes).
    Removing dac.o(.ARM.exidx.text.Dac_DmaITCfg), (8 bytes).
    Removing dac.o(.text.Dac_GetITStatus), (16 bytes).
    Removing dac.o(.ARM.exidx.text.Dac_GetITStatus), (8 bytes).
    Removing dac.o(.text.Dac_Cmd), (20 bytes).
    Removing dac.o(.ARM.exidx.text.Dac_Cmd), (8 bytes).
    Removing dac.o(.text.Dac_SoftwareTriggerCmd), (20 bytes).
    Removing dac.o(.ARM.exidx.text.Dac_SoftwareTriggerCmd), (8 bytes).
    Removing dac.o(.text.Dac_Init), (184 bytes).
    Removing dac.o(.ARM.exidx.text.Dac_Init), (8 bytes).
    Removing dac.o(.text.Dac_SetChannelData), (88 bytes).
    Removing dac.o(.ARM.exidx.text.Dac_SetChannelData), (8 bytes).
    Removing dac.o(.text.Dac_GetDataOutputValue), (20 bytes).
    Removing dac.o(.ARM.exidx.text.Dac_GetDataOutputValue), (8 bytes).
    Removing ddl.o(.text), (0 bytes).
    Removing ddl.o(.text.Log2), (24 bytes).
    Removing ddl.o(.ARM.exidx.text.Log2), (8 bytes).
    Removing ddl.o(.ARM.exidx.text.ddl_memclr), (8 bytes).
    Removing ddl.o(.ARM.exidx.text.delay1ms), (8 bytes).
    Removing ddl.o(.text.delay100us), (68 bytes).
    Removing ddl.o(.ARM.exidx.text.delay100us), (8 bytes).
    Removing ddl.o(.ARM.exidx.text.delay10us), (8 bytes).
    Removing ddl.o(.ARM.exidx.text.SetBit), (8 bytes).
    Removing ddl.o(.text.GetBit), (10 bytes).
    Removing ddl.o(.ARM.exidx.text.GetBit), (8 bytes).
    Removing debug.o(.text), (0 bytes).
    Removing debug.o(.text.Debug_ActiveEnable), (16 bytes).
    Removing debug.o(.ARM.exidx.text.Debug_ActiveEnable), (8 bytes).
    Removing debug.o(.text.Debug_ActiveDisable), (16 bytes).
    Removing debug.o(.ARM.exidx.text.Debug_ActiveDisable), (8 bytes).
    Removing dmac.o(.text), (0 bytes).
    Removing dmac.o(.text.Dma_InitChannel), (116 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_InitChannel), (8 bytes).
    Removing dmac.o(.text.Dma_Enable), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_Enable), (8 bytes).
    Removing dmac.o(.text.Dma_Disable), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_Disable), (8 bytes).
    Removing dmac.o(.text.Dma_SwStart), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_SwStart), (8 bytes).
    Removing dmac.o(.text.Dma_SwStop), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_SwStop), (8 bytes).
    Removing dmac.o(.text.Dma_EnableChannelIrq), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_EnableChannelIrq), (8 bytes).
    Removing dmac.o(.text.Dma_DisableChannelIrq), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_DisableChannelIrq), (8 bytes).
    Removing dmac.o(.text.Dma_EnableChannelErrIrq), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_EnableChannelErrIrq), (8 bytes).
    Removing dmac.o(.text.Dma_DisableChannelErrIrq), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_DisableChannelErrIrq), (8 bytes).
    Removing dmac.o(.text.Dma_EnableChannel), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_EnableChannel), (8 bytes).
    Removing dmac.o(.text.Dma_DisableChannel), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_DisableChannel), (8 bytes).
    Removing dmac.o(.text.Dma_SetBlockSize), (32 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_SetBlockSize), (8 bytes).
    Removing dmac.o(.text.Dma_SetTransferCnt), (28 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_SetTransferCnt), (8 bytes).
    Removing dmac.o(.text.Dma_EnableContinusTranfer), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_EnableContinusTranfer), (8 bytes).
    Removing dmac.o(.text.Dma_DisableContinusTranfer), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_DisableContinusTranfer), (8 bytes).
    Removing dmac.o(.text.Dma_HaltTranfer), (24 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_HaltTranfer), (8 bytes).
    Removing dmac.o(.text.Dma_RecoverTranfer), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_RecoverTranfer), (8 bytes).
    Removing dmac.o(.text.Dma_PauseChannelTranfer), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_PauseChannelTranfer), (8 bytes).
    Removing dmac.o(.text.Dma_RecoverChannelTranfer), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_RecoverChannelTranfer), (8 bytes).
    Removing dmac.o(.text.Dma_SetTransferWidth), (24 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_SetTransferWidth), (8 bytes).
    Removing dmac.o(.text.Dma_SetChPriority), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_SetChPriority), (8 bytes).
    Removing dmac.o(.text.Dma_GetStat), (16 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_GetStat), (8 bytes).
    Removing dmac.o(.text.Dma_ClrStat), (20 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_ClrStat), (8 bytes).
    Removing dmac.o(.text.Dma_SetSourceAddress), (12 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_SetSourceAddress), (8 bytes).
    Removing dmac.o(.text.Dma_SetDestinationAddress), (12 bytes).
    Removing dmac.o(.ARM.exidx.text.Dma_SetDestinationAddress), (8 bytes).
    Removing flash.o(.text), (0 bytes).
    Removing flash.o(.text.Flash_GetIntFlag), (20 bytes).
    Removing flash.o(.ARM.exidx.text.Flash_GetIntFlag), (8 bytes).
    Removing flash.o(.text.Flash_ClearIntFlag), (16 bytes).
    Removing flash.o(.ARM.exidx.text.Flash_ClearIntFlag), (8 bytes).
    Removing flash.o(.text.Flash_EnableIrq), (44 bytes).
    Removing flash.o(.ARM.exidx.text.Flash_EnableIrq), (8 bytes).
    Removing flash.o(.text.Flash_DisableIrq), (48 bytes).
    Removing flash.o(.ARM.exidx.text.Flash_DisableIrq), (8 bytes).
    Removing flash.o(.text.Flash_Init), (248 bytes).
    Removing flash.o(.ARM.exidx.text.Flash_Init), (8 bytes).
    Removing flash.o(.text.Flash_Write8), (156 bytes).
    Removing flash.o(.ARM.exidx.text.Flash_Write8), (8 bytes).
    Removing flash.o(.text.Flash_UnlockAll), (80 bytes).
    Removing flash.o(.ARM.exidx.text.Flash_UnlockAll), (8 bytes).
    Removing flash.o(.text.Flash_LockAll), (76 bytes).
    Removing flash.o(.ARM.exidx.text.Flash_LockAll), (8 bytes).
    Removing flash.o(.text.Flash_Write16), (160 bytes).
    Removing flash.o(.ARM.exidx.text.Flash_Write16), (8 bytes).
    Removing flash.o(.text.Flash_Write32), (160 bytes).
    Removing flash.o(.ARM.exidx.text.Flash_Write32), (8 bytes).
    Removing flash.o(.text.Flash_SectorErase), (108 bytes).
    Removing flash.o(.ARM.exidx.text.Flash_SectorErase), (8 bytes).
    Removing flash.o(.text.Flash_OpModeConfig), (92 bytes).
    Removing flash.o(.ARM.exidx.text.Flash_OpModeConfig), (8 bytes).
    Removing flash.o(.ARM.exidx.text.Flash_WaitCycle), (8 bytes).
    Removing flash.o(.text.Flash_EnDpstb), (84 bytes).
    Removing flash.o(.ARM.exidx.text.Flash_EnDpstb), (8 bytes).
    Removing flash.o(.text.Flash_LockSet), (40 bytes).
    Removing flash.o(.ARM.exidx.text.Flash_LockSet), (8 bytes).
    Removing flash.o(ramfunc), (788 bytes).
    Removing flash.o(.ARM.exidxramfunc), (32 bytes).
    Removing flash.o(.rodata.pu32PcgTimer4M), (60 bytes).
    Removing gpio.o(.text), (0 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_Init), (8 bytes).
    Removing gpio.o(.text.Gpio_GetInputIO), (16 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_GetInputIO), (8 bytes).
    Removing gpio.o(.text.Gpio_GetInputData), (12 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_GetInputData), (8 bytes).
    Removing gpio.o(.text.Gpio_WriteOutputIO), (20 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_WriteOutputIO), (8 bytes).
    Removing gpio.o(.text.Gpio_ReadOutputIO), (16 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_ReadOutputIO), (8 bytes).
    Removing gpio.o(.text.Gpio_SetPort), (12 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_SetPort), (8 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_SetIO), (8 bytes).
    Removing gpio.o(.text.Gpio_ClrPort), (12 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_ClrPort), (8 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_ClrIO), (8 bytes).
    Removing gpio.o(.text.Gpio_SetClrPort), (12 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_SetClrPort), (8 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_SetAnalogMode), (8 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_SetAfMode), (8 bytes).
    Removing gpio.o(.text.Gpio_SetAfMode_Lite), (28 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_SetAfMode_Lite), (8 bytes).
    Removing gpio.o(.text.Gpio_EnableIrq), (24 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_EnableIrq), (8 bytes).
    Removing gpio.o(.text.Gpio_DisableIrq), (28 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_DisableIrq), (8 bytes).
    Removing gpio.o(.text.Gpio_GetIrqStatus), (16 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_GetIrqStatus), (8 bytes).
    Removing gpio.o(.text.Gpio_ClearIrq), (24 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_ClearIrq), (8 bytes).
    Removing gpio.o(.text.Gpio_SfIrPolCfg), (28 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_SfIrPolCfg), (8 bytes).
    Removing gpio.o(.text.Gpio_SfHClkOutputCfg), (44 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_SfHClkOutputCfg), (8 bytes).
    Removing gpio.o(.text.Gpio_SfPClkOutputCfg), (44 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_SfPClkOutputCfg), (8 bytes).
    Removing gpio.o(.text.Gpio_SfExtClkCfg), (24 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_SfExtClkCfg), (8 bytes).
    Removing gpio.o(.text.Gpio_SfNssCfg), (52 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_SfNssCfg), (8 bytes).
    Removing gpio.o(.text.Gpio_SfTimGCfg), (60 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_SfTimGCfg), (8 bytes).
    Removing gpio.o(.text.Gpio_SfTimECfg), (60 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_SfTimECfg), (8 bytes).
    Removing gpio.o(.text.Gpio_SfTimCCfg), (32 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_SfTimCCfg), (8 bytes).
    Removing gpio.o(.text.Gpio_SfPcaCfg), (32 bytes).
    Removing gpio.o(.ARM.exidx.text.Gpio_SfPcaCfg), (8 bytes).
    Removing i2c.o(.text), (0 bytes).
    Removing i2c.o(.text.I2C_SetBaud), (6 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C_SetBaud), (8 bytes).
    Removing i2c.o(.text.I2C_SetFunc), (14 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C_SetFunc), (8 bytes).
    Removing i2c.o(.text.I2C_ClearFunc), (16 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C_ClearFunc), (8 bytes).
    Removing i2c.o(.text.I2C_GetIrq), (8 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C_GetIrq), (8 bytes).
    Removing i2c.o(.text.I2C_ClearIrq), (12 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C_ClearIrq), (8 bytes).
    Removing i2c.o(.text.I2C_GetState), (6 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C_GetState), (8 bytes).
    Removing i2c.o(.text.I2C_WriteByte), (6 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C_WriteByte), (8 bytes).
    Removing i2c.o(.text.I2C_ReadByte), (6 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C_ReadByte), (8 bytes).
    Removing i2c.o(.text.I2C_GetAddMatchState), (6 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C_GetAddMatchState), (8 bytes).
    Removing i2c.o(.text.I2C_Init), (148 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C_Init), (8 bytes).
    Removing lcd.o(.text), (0 bytes).
    Removing lcd.o(.text.Lcd_GetItStatus), (16 bytes).
    Removing lcd.o(.ARM.exidx.text.Lcd_GetItStatus), (8 bytes).
    Removing lcd.o(.text.Lcd_ClearItPendingBit), (20 bytes).
    Removing lcd.o(.ARM.exidx.text.Lcd_ClearItPendingBit), (8 bytes).
    Removing lcd.o(.text.Lcd_GetSegCom), (196 bytes).
    Removing lcd.o(.ARM.exidx.text.Lcd_GetSegCom), (8 bytes).
    Removing lcd.o(.text.Lcd_SetSegCom), (16 bytes).
    Removing lcd.o(.ARM.exidx.text.Lcd_SetSegCom), (8 bytes).
    Removing lcd.o(.text.Lcd_Init), (188 bytes).
    Removing lcd.o(.ARM.exidx.text.Lcd_Init), (8 bytes).
    Removing lcd.o(.text.Lcd_FullDisp), (24 bytes).
    Removing lcd.o(.ARM.exidx.text.Lcd_FullDisp), (8 bytes).
    Removing lcd.o(.text.Lcd_ClearDisp), (24 bytes).
    Removing lcd.o(.ARM.exidx.text.Lcd_ClearDisp), (8 bytes).
    Removing lcd.o(.text.Lcd_WriteRam), (24 bytes).
    Removing lcd.o(.ARM.exidx.text.Lcd_WriteRam), (8 bytes).
    Removing lpm.o(.text), (0 bytes).
    Removing lpm.o(.text.Lpm_GotoDeepSleep), (36 bytes).
    Removing lpm.o(.ARM.exidx.text.Lpm_GotoDeepSleep), (8 bytes).
    Removing lpm.o(.text.Lpm_GotoSleep), (36 bytes).
    Removing lpm.o(.ARM.exidx.text.Lpm_GotoSleep), (8 bytes).
    Removing lptim.o(.text), (0 bytes).
    Removing lptim.o(.text.Lptim_ConfIt), (14 bytes).
    Removing lptim.o(.ARM.exidx.text.Lptim_ConfIt), (8 bytes).
    Removing lptim.o(.text.Lptim_Cmd), (14 bytes).
    Removing lptim.o(.ARM.exidx.text.Lptim_Cmd), (8 bytes).
    Removing lptim.o(.text.Lptim_GetItStatus), (12 bytes).
    Removing lptim.o(.ARM.exidx.text.Lptim_GetItStatus), (8 bytes).
    Removing lptim.o(.text.Lptim_ClrItStatus), (14 bytes).
    Removing lptim.o(.ARM.exidx.text.Lptim_ClrItStatus), (8 bytes).
    Removing lptim.o(.text.Lptim_Init), (160 bytes).
    Removing lptim.o(.ARM.exidx.text.Lptim_Init), (8 bytes).
    Removing lpuart.o(.text), (0 bytes).
    Removing lpuart.o(.text.LPUart_EnableIrq), (14 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_EnableIrq), (8 bytes).
    Removing lpuart.o(.text.LPUart_DisableIrq), (16 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_DisableIrq), (8 bytes).
    Removing lpuart.o(.text.LPUart_SelSclk), (20 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_SelSclk), (8 bytes).
    Removing lpuart.o(.text.LPUart_SetMultiMode), (28 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_SetMultiMode), (8 bytes).
    Removing lpuart.o(.text.LPUart_HdModeEnable), (12 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_HdModeEnable), (8 bytes).
    Removing lpuart.o(.text.LPUart_HdModeDisable), (12 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_HdModeDisable), (8 bytes).
    Removing lpuart.o(.text.LPUart_SetSaddr), (6 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_SetSaddr), (8 bytes).
    Removing lpuart.o(.text.LPUart_EnableFunc), (14 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_EnableFunc), (8 bytes).
    Removing lpuart.o(.text.LPUart_DisableFunc), (16 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_DisableFunc), (8 bytes).
    Removing lpuart.o(.text.LPUart_GetIsr), (6 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_GetIsr), (8 bytes).
    Removing lpuart.o(.text.LPUart_GetStatus), (10 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_GetStatus), (8 bytes).
    Removing lpuart.o(.text.LPUart_ClrIsr), (8 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_ClrIsr), (8 bytes).
    Removing lpuart.o(.text.LPUart_ClrStatus), (16 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_ClrStatus), (8 bytes).
    Removing lpuart.o(.text.LPUart_SendData), (52 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_SendData), (8 bytes).
    Removing lpuart.o(.text.LPUart_SendDataTimeOut), (102 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_SendDataTimeOut), (8 bytes).
    Removing lpuart.o(.text.LPUart_SendDataIt), (32 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_SendDataIt), (8 bytes).
    Removing lpuart.o(.text.LPUart_MultiModeSendData), (80 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_MultiModeSendData), (8 bytes).
    Removing lpuart.o(.text.LPUart_MultiModeSendDataIt), (24 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_MultiModeSendDataIt), (8 bytes).
    Removing lpuart.o(.text.LPUart_ReceiveData), (6 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_ReceiveData), (8 bytes).
    Removing lpuart.o(.text.LPUart_GetRb8), (8 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_GetRb8), (8 bytes).
    Removing lpuart.o(.text.LPUart_Init), (124 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_Init), (8 bytes).
    Removing lpuart.o(.text.LPUart_Xtl_Bsel_Set), (60 bytes).
    Removing lpuart.o(.ARM.exidx.text.LPUart_Xtl_Bsel_Set), (8 bytes).
    Removing lvd.o(.text), (0 bytes).
    Removing lvd.o(.text.Lvd_EnableIrq), (20 bytes).
    Removing lvd.o(.ARM.exidx.text.Lvd_EnableIrq), (8 bytes).
    Removing lvd.o(.text.Lvd_DisableIrq), (20 bytes).
    Removing lvd.o(.ARM.exidx.text.Lvd_DisableIrq), (8 bytes).
    Removing lvd.o(.text.Lvd_Init), (32 bytes).
    Removing lvd.o(.ARM.exidx.text.Lvd_Init), (8 bytes).
    Removing lvd.o(.text.Lvd_Enable), (16 bytes).
    Removing lvd.o(.ARM.exidx.text.Lvd_Enable), (8 bytes).
    Removing lvd.o(.text.Lvd_Disable), (16 bytes).
    Removing lvd.o(.ARM.exidx.text.Lvd_Disable), (8 bytes).
    Removing lvd.o(.text.Lvd_GetIrqStat), (16 bytes).
    Removing lvd.o(.ARM.exidx.text.Lvd_GetIrqStat), (8 bytes).
    Removing lvd.o(.text.Lvd_ClearIrq), (16 bytes).
    Removing lvd.o(.ARM.exidx.text.Lvd_ClearIrq), (8 bytes).
    Removing lvd.o(.text.Lvd_GetFilterResult), (16 bytes).
    Removing lvd.o(.ARM.exidx.text.Lvd_GetFilterResult), (8 bytes).
    Removing opa.o(.text), (0 bytes).
    Removing opa.o(.text.Opa_Cmd), (20 bytes).
    Removing opa.o(.ARM.exidx.text.Opa_Cmd), (8 bytes).
    Removing opa.o(.text.Opa_CmdBuf), (20 bytes).
    Removing opa.o(.ARM.exidx.text.Opa_CmdBuf), (8 bytes).
    Removing opa.o(.text.Opa_CmdOenx), (20 bytes).
    Removing opa.o(.ARM.exidx.text.Opa_CmdOenx), (8 bytes).
    Removing pca.o(.text), (0 bytes).
    Removing pca.o(.text.Pca_GetItStatus), (16 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_GetItStatus), (8 bytes).
    Removing pca.o(.text.Pca_ClrItStatus), (20 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_ClrItStatus), (8 bytes).
    Removing pca.o(.text.Pca_StartPca), (20 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_StartPca), (8 bytes).
    Removing pca.o(.text.Pca_SetCidl), (20 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_SetCidl), (8 bytes).
    Removing pca.o(.text.Pca_Set4Wdte), (20 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_Set4Wdte), (8 bytes).
    Removing pca.o(.text.Pca_ConfPcaIt), (20 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_ConfPcaIt), (8 bytes).
    Removing pca.o(.text.Pca_ConfModulexIt), (28 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_ConfModulexIt), (8 bytes).
    Removing pca.o(.text.Pca_M0Init), (220 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_M0Init), (8 bytes).
    Removing pca.o(.text.Pca_M1Init), (220 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_M1Init), (8 bytes).
    Removing pca.o(.text.Pca_M2Init), (220 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_M2Init), (8 bytes).
    Removing pca.o(.text.Pca_M3Init), (220 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_M3Init), (8 bytes).
    Removing pca.o(.text.Pca_M4Init), (220 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_M4Init), (8 bytes).
    Removing pca.o(.text.Pca_GetCnt), (12 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_GetCnt), (8 bytes).
    Removing pca.o(.text.Pca_SetCnt), (64 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_SetCnt), (8 bytes).
    Removing pca.o(.text.Pca_GetOut), (16 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_GetOut), (8 bytes).
    Removing pca.o(.text.Pca_SetCcap), (112 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_SetCcap), (8 bytes).
    Removing pca.o(.text.Pca_GetCcap), (80 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_GetCcap), (8 bytes).
    Removing pca.o(.text.Pca_SetCarr), (24 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_SetCarr), (8 bytes).
    Removing pca.o(.text.Pca_GetCarr), (12 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_GetCarr), (8 bytes).
    Removing pca.o(.text.Pca_SetCcapHL), (160 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_SetCcapHL), (8 bytes).
    Removing pca.o(.text.Pca_GetCcapHL), (88 bytes).
    Removing pca.o(.ARM.exidx.text.Pca_GetCcapHL), (8 bytes).
    Removing pcnt.o(.text), (0 bytes).
    Removing pcnt.o(.text.Pcnt_Cmd), (32 bytes).
    Removing pcnt.o(.ARM.exidx.text.Pcnt_Cmd), (8 bytes).
    Removing pcnt.o(.text.Pcnt_SetB2T), (40 bytes).
    Removing pcnt.o(.ARM.exidx.text.Pcnt_SetB2T), (8 bytes).
    Removing pcnt.o(.text.Pcnt_SetB2C), (40 bytes).
    Removing pcnt.o(.ARM.exidx.text.Pcnt_SetB2C), (8 bytes).
    Removing pcnt.o(.text.Pcnt_SetT2C), (40 bytes).
    Removing pcnt.o(.ARM.exidx.text.Pcnt_SetT2C), (8 bytes).
    Removing pcnt.o(.text.Pcnt_SetBuf), (24 bytes).
    Removing pcnt.o(.ARM.exidx.text.Pcnt_SetBuf), (8 bytes).
    Removing pcnt.o(.text.Pcnt_Init), (208 bytes).
    Removing pcnt.o(.ARM.exidx.text.Pcnt_Init), (8 bytes).
    Removing pcnt.o(.text.Pcnt_ItCfg), (36 bytes).
    Removing pcnt.o(.ARM.exidx.text.Pcnt_ItCfg), (8 bytes).
    Removing pcnt.o(.text.Pcnt_GetItStatus), (16 bytes).
    Removing pcnt.o(.ARM.exidx.text.Pcnt_GetItStatus), (8 bytes).
    Removing pcnt.o(.text.Pcnt_ClrItStatus), (20 bytes).
    Removing pcnt.o(.ARM.exidx.text.Pcnt_ClrItStatus), (8 bytes).
    Removing pcnt.o(.text.Pcnt_GetCnt), (12 bytes).
    Removing pcnt.o(.ARM.exidx.text.Pcnt_GetCnt), (8 bytes).
    Removing pcnt.o(.text.Pcnt_GetTop), (12 bytes).
    Removing pcnt.o(.ARM.exidx.text.Pcnt_GetTop), (8 bytes).
    Removing pcnt.o(.text.Pcnt_GetBuf), (12 bytes).
    Removing pcnt.o(.ARM.exidx.text.Pcnt_GetBuf), (8 bytes).
    Removing ram.o(.text), (0 bytes).
    Removing ram.o(.text.Ram_ErrAddrGet), (12 bytes).
    Removing ram.o(.ARM.exidx.text.Ram_ErrAddrGet), (8 bytes).
    Removing ram.o(.text.Ram_GetIntFlag), (16 bytes).
    Removing ram.o(.ARM.exidx.text.Ram_GetIntFlag), (8 bytes).
    Removing ram.o(.text.Ram_ClearIntFlag), (12 bytes).
    Removing ram.o(.ARM.exidx.text.Ram_ClearIntFlag), (8 bytes).
    Removing ram.o(.text.Ram_EnableIrq), (16 bytes).
    Removing ram.o(.ARM.exidx.text.Ram_EnableIrq), (8 bytes).
    Removing ram.o(.text.Ram_DisableIrq), (16 bytes).
    Removing ram.o(.ARM.exidx.text.Ram_DisableIrq), (8 bytes).
    Removing reset.o(.text), (0 bytes).
    Removing reset.o(.text.Reset_GetFlag), (20 bytes).
    Removing reset.o(.ARM.exidx.text.Reset_GetFlag), (8 bytes).
    Removing reset.o(.text.Reset_ClearFlag), (16 bytes).
    Removing reset.o(.ARM.exidx.text.Reset_ClearFlag), (8 bytes).
    Removing reset.o(.text.Reset_ClearFlagAll), (12 bytes).
    Removing reset.o(.ARM.exidx.text.Reset_ClearFlagAll), (8 bytes).
    Removing reset.o(.text.Reset_RstPeripheralAll), (20 bytes).
    Removing reset.o(.ARM.exidx.text.Reset_RstPeripheralAll), (8 bytes).
    Removing reset.o(.text.Reset_RstPeripheral0), (20 bytes).
    Removing reset.o(.ARM.exidx.text.Reset_RstPeripheral0), (8 bytes).
    Removing reset.o(.text.Reset_RstPeripheral1), (20 bytes).
    Removing reset.o(.ARM.exidx.text.Reset_RstPeripheral1), (8 bytes).
    Removing rtc.o(.text), (0 bytes).
    Removing rtc.o(.text.Rtc_Cmd), (20 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_Cmd), (8 bytes).
    Removing rtc.o(.text.Rtc_StartWait), (36 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_StartWait), (8 bytes).
    Removing rtc.o(.text.Rtc_Hz1Cmd), (32 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_Hz1Cmd), (8 bytes).
    Removing rtc.o(.text.Rtc_SetCyc), (84 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_SetCyc), (8 bytes).
    Removing rtc.o(.text.Rtc_AlmIeCmd), (44 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_AlmIeCmd), (8 bytes).
    Removing rtc.o(.text.Rtc_AlmEnCmd), (20 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_AlmEnCmd), (8 bytes).
    Removing rtc.o(.text.Rtc_GetAlmfItStatus), (16 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_GetAlmfItStatus), (8 bytes).
    Removing rtc.o(.text.Rtc_ClearAlmfItStatus), (20 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_ClearAlmfItStatus), (8 bytes).
    Removing rtc.o(.text.Rtc_ClearPrdfItStatus), (20 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_ClearPrdfItStatus), (8 bytes).
    Removing rtc.o(.text.Rtc_GetPridItStatus), (16 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_GetPridItStatus), (8 bytes).
    Removing rtc.o(.text.Rtc_CompCfg), (60 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_CompCfg), (8 bytes).
    Removing rtc.o(.text.Check_BCD_Format), (54 bytes).
    Removing rtc.o(.ARM.exidx.text.Check_BCD_Format), (8 bytes).
    Removing rtc.o(.text.Rtc_CheckLeapYear), (68 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_CheckLeapYear), (8 bytes).
    Removing rtc.o(.text.Get_Month2_Day), (18 bytes).
    Removing rtc.o(.ARM.exidx.text.Get_Month2_Day), (8 bytes).
    Removing rtc.o(.text.Rtc_ReadDateTime), (108 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_ReadDateTime), (8 bytes).
    Removing rtc.o(.text.Rtc_SetTime), (96 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_SetTime), (8 bytes).
    Removing rtc.o(.text.Rtc_GetAlarmTime), (24 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_GetAlarmTime), (8 bytes).
    Removing rtc.o(.text.Rtc_SetAlarmTime), (124 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_SetAlarmTime), (8 bytes).
    Removing rtc.o(.text.Rtc_Init), (76 bytes).
    Removing rtc.o(.ARM.exidx.text.Rtc_Init), (8 bytes).
    Removing rtc.o(.rodata.Leap_Month_Base), (12 bytes).
    Removing rtc.o(.rodata.NonLeap_Month_Base), (12 bytes).
    Removing rtc.o(.rodata.Cnst_Month_Tbl), (12 bytes).
    Removing spi.o(.text), (0 bytes).
    Removing spi.o(.text.Spi_GetStatus), (10 bytes).
    Removing spi.o(.ARM.exidx.text.Spi_GetStatus), (8 bytes).
    Removing spi.o(.text.Spi_ClearStatus), (8 bytes).
    Removing spi.o(.ARM.exidx.text.Spi_ClearStatus), (8 bytes).
    Removing spi.o(.text.Spi_IrqEnable), (12 bytes).
    Removing spi.o(.ARM.exidx.text.Spi_IrqEnable), (8 bytes).
    Removing spi.o(.text.Spi_IrqDisable), (12 bytes).
    Removing spi.o(.ARM.exidx.text.Spi_IrqDisable), (8 bytes).
    Removing spi.o(.text.Spi_FuncEnable), (10 bytes).
    Removing spi.o(.ARM.exidx.text.Spi_FuncEnable), (8 bytes).
    Removing spi.o(.text.Spi_FuncDisable), (10 bytes).
    Removing spi.o(.ARM.exidx.text.Spi_FuncDisable), (8 bytes).
    Removing spi.o(.text.Spi_Init), (32 bytes).
    Removing spi.o(.ARM.exidx.text.Spi_Init), (8 bytes).
    Removing spi.o(.text.Spi_SetCS), (4 bytes).
    Removing spi.o(.ARM.exidx.text.Spi_SetCS), (8 bytes).
    Removing spi.o(.text.Spi_SendData), (6 bytes).
    Removing spi.o(.ARM.exidx.text.Spi_SendData), (8 bytes).
    Removing spi.o(.text.Spi_RWByte), (20 bytes).
    Removing spi.o(.ARM.exidx.text.Spi_RWByte), (8 bytes).
    Removing spi.o(.text.Spi_Slave_DummyWriteData), (10 bytes).
    Removing spi.o(.ARM.exidx.text.Spi_Slave_DummyWriteData), (8 bytes).
    Removing spi.o(.text.Spi_SendBuf), (60 bytes).
    Removing spi.o(.ARM.exidx.text.Spi_SendBuf), (8 bytes).
    Removing spi.o(.text.Spi_ReceiveData), (6 bytes).
    Removing spi.o(.ARM.exidx.text.Spi_ReceiveData), (8 bytes).
    Removing spi.o(.text.Spi_ReceiveBuf), (44 bytes).
    Removing spi.o(.ARM.exidx.text.Spi_ReceiveBuf), (8 bytes).
    Removing sysctrl.o(.text), (0 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_ClkSourceEnable), (8 bytes).
    Removing sysctrl.o(.ARM.exidx.text._SysctrlUnlock), (8 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_SysClkSwitch), (8 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_GetHClkFreq), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_GetPClkFreq), (24 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_GetPClkFreq), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_ClkInit), (104 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_ClkInit), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_XTHDriverCfg), (24 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_XTHDriverCfg), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_SetXTHStableTime), (24 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_SetXTHStableTime), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_SetRCLStableTime), (28 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_SetRCLStableTime), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_XTLDriverCfg), (40 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_XTLDriverCfg), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_SetXTLStableTime), (24 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_SetXTLStableTime), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_SetPLLStableTime), (28 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_SetPLLStableTime), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_SetHCLKDiv), (36 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_SetHCLKDiv), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_SetPCLKDiv), (36 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_SetPCLKDiv), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_ClkDeInit), (74 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_ClkDeInit), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_SetRCHTrim), (44 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_SetRCHTrim), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_SetRCLTrim), (44 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_SetRCLTrim), (8 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_SetRC48MTrim), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_SetXTHFreq), (24 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_SetXTHFreq), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_SetPLLFreq), (256 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_SetPLLFreq), (8 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_SetPeripheralGate), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_GetPeripheralGate), (32 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_GetPeripheralGate), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_SetFunc), (28 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_SetFunc), (8 bytes).
    Removing sysctrl.o(.text.Sysctrl_SetRTCAdjustClkFreq), (36 bytes).
    Removing sysctrl.o(.ARM.exidx.text.Sysctrl_SetRTCAdjustClkFreq), (8 bytes).
    Removing timer3.o(.text), (0 bytes).
    Removing timer3.o(.text.Tim3_GetIntFlag), (16 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_GetIntFlag), (8 bytes).
    Removing timer3.o(.text.Tim3_ClearIntFlag), (20 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_ClearIntFlag), (8 bytes).
    Removing timer3.o(.text.Tim3_ClearAllIntFlag), (12 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_ClearAllIntFlag), (8 bytes).
    Removing timer3.o(.text.Tim3_Mode0_EnableIrq), (20 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_Mode0_EnableIrq), (8 bytes).
    Removing timer3.o(.text.Tim3_Mode0_DisableIrq), (20 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_Mode0_DisableIrq), (8 bytes).
    Removing timer3.o(.text.Tim3_Mode1_EnableIrq), (40 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_Mode1_EnableIrq), (8 bytes).
    Removing timer3.o(.text.Tim3_Mode1_DisableIrq), (44 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_Mode1_DisableIrq), (8 bytes).
    Removing timer3.o(.text.Tim3_Mode23_EnableIrq), (176 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_Mode23_EnableIrq), (8 bytes).
    Removing timer3.o(.text.Tim3_Mode23_DisableIrq), (176 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_Mode23_DisableIrq), (8 bytes).
    Removing timer3.o(.text.Tim3_Mode0_Init), (128 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_Mode0_Init), (8 bytes).
    Removing timer3.o(.text.Tim3_M0_Run), (20 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M0_Run), (8 bytes).
    Removing timer3.o(.text.Tim3_M0_Stop), (20 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M0_Stop), (8 bytes).
    Removing timer3.o(.text.Tim3_M0_Enable_Output), (28 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M0_Enable_Output), (8 bytes).
    Removing timer3.o(.text.Tim3_M0_EnTOG), (24 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M0_EnTOG), (8 bytes).
    Removing timer3.o(.text.Tim3_M0_Cnt16Set), (24 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M0_Cnt16Set), (8 bytes).
    Removing timer3.o(.text.Tim3_M0_Cnt16Get), (12 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M0_Cnt16Get), (8 bytes).
    Removing timer3.o(.text.Tim3_M0_ARRSet), (24 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M0_ARRSet), (8 bytes).
    Removing timer3.o(.text.Tim3_M0_Cnt32Set), (16 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M0_Cnt32Set), (8 bytes).
    Removing timer3.o(.text.Tim3_M0_Cnt32Get), (12 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M0_Cnt32Get), (8 bytes).
    Removing timer3.o(.text.Tim3_Mode1_Init), (80 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_Mode1_Init), (8 bytes).
    Removing timer3.o(.text.Tim3_M1_Input_Cfg), (148 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M1_Input_Cfg), (8 bytes).
    Removing timer3.o(.text.Tim3_M1_PWC_Edge_Sel), (96 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M1_PWC_Edge_Sel), (8 bytes).
    Removing timer3.o(.text.Tim3_M1_Run), (20 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M1_Run), (8 bytes).
    Removing timer3.o(.text.Tim3_M1_Stop), (20 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M1_Stop), (8 bytes).
    Removing timer3.o(.text.Tim3_M1_Cnt16Set), (24 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M1_Cnt16Set), (8 bytes).
    Removing timer3.o(.text.Tim3_M1_Cnt16Get), (12 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M1_Cnt16Get), (8 bytes).
    Removing timer3.o(.text.Tim3_M1_PWC_CapValueGet), (12 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M1_PWC_CapValueGet), (8 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_Mode23_Init), (8 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_EnPWM_Output), (8 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_Run), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_Stop), (20 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_Stop), (8 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_ARRSet), (8 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_Cnt16Set), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_Cnt16Get), (12 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_Cnt16Get), (8 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_CCR_Set), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_CCR_Get), (12 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_CCR_Get), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_GateFuncSel), (64 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_GateFuncSel), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_MasterSlave_Set), (76 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_MasterSlave_Set), (8 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_PortOutput_Cfg), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_Ch3_Cfg), (144 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_Ch3_Cfg), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_PortInput_Cfg), (368 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_PortInput_Cfg), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_ETRInput_Cfg), (48 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_ETRInput_Cfg), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_BrakeInput_Cfg), (220 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_BrakeInput_Cfg), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_TrigADC_Cfg), (140 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_TrigADC_Cfg), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_DT_Cfg), (44 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_DT_Cfg), (8 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_SetValidPeriod), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_OCRefClr), (48 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_OCRefClr), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_EnDMA), (156 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_EnDMA), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_EnSwTrigCapCmpA), (68 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_EnSwTrigCapCmpA), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_EnSwTrigCapCmpB), (68 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_EnSwTrigCapCmpB), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_EnSwUev), (20 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_EnSwUev), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_EnSwTrig), (20 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_EnSwTrig), (8 bytes).
    Removing timer3.o(.text.Tim3_M23_EnSwBk), (20 bytes).
    Removing timer3.o(.ARM.exidx.text.Tim3_M23_EnSwBk), (8 bytes).
    Removing trim.o(.text), (0 bytes).
    Removing trim.o(.text.Trim_GetIntFlag), (20 bytes).
    Removing trim.o(.ARM.exidx.text.Trim_GetIntFlag), (8 bytes).
    Removing trim.o(.text.Trim_ClearIntFlag), (16 bytes).
    Removing trim.o(.ARM.exidx.text.Trim_ClearIntFlag), (8 bytes).
    Removing trim.o(.text.Trim_EnableIrq), (16 bytes).
    Removing trim.o(.ARM.exidx.text.Trim_EnableIrq), (8 bytes).
    Removing trim.o(.text.Trim_DisableIrq), (16 bytes).
    Removing trim.o(.ARM.exidx.text.Trim_DisableIrq), (8 bytes).
    Removing trim.o(.text.Trim_Init), (36 bytes).
    Removing trim.o(.ARM.exidx.text.Trim_Init), (8 bytes).
    Removing trim.o(.text.Trim_Run), (16 bytes).
    Removing trim.o(.ARM.exidx.text.Trim_Run), (8 bytes).
    Removing trim.o(.text.Trim_Stop), (16 bytes).
    Removing trim.o(.ARM.exidx.text.Trim_Stop), (8 bytes).
    Removing trim.o(.text.Trim_RefCntGet), (12 bytes).
    Removing trim.o(.ARM.exidx.text.Trim_RefCntGet), (8 bytes).
    Removing trim.o(.text.Trim_CalCntGet), (12 bytes).
    Removing trim.o(.ARM.exidx.text.Trim_CalCntGet), (8 bytes).
    Removing trng.o(.text), (0 bytes).
    Removing trng.o(.text.Trng_Init), (104 bytes).
    Removing trng.o(.ARM.exidx.text.Trng_Init), (8 bytes).
    Removing trng.o(.text.Trng_Generate), (108 bytes).
    Removing trng.o(.ARM.exidx.text.Trng_Generate), (8 bytes).
    Removing trng.o(.text.Trng_GetData0), (12 bytes).
    Removing trng.o(.ARM.exidx.text.Trng_GetData0), (8 bytes).
    Removing trng.o(.text.Trng_GetData1), (12 bytes).
    Removing trng.o(.ARM.exidx.text.Trng_GetData1), (8 bytes).
    Removing uart.o(.text), (0 bytes).
    Removing uart.o(.text.Uart_EnableIrq), (14 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_EnableIrq), (8 bytes).
    Removing uart.o(.text.Uart_DisableIrq), (16 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_DisableIrq), (8 bytes).
    Removing uart.o(.text.Uart_SetMultiMode), (28 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_SetMultiMode), (8 bytes).
    Removing uart.o(.text.Uart_HdModeEnable), (12 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_HdModeEnable), (8 bytes).
    Removing uart.o(.text.Uart_HdModeDisable), (12 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_HdModeDisable), (8 bytes).
    Removing uart.o(.text.Uart_SetTb8), (16 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_SetTb8), (8 bytes).
    Removing uart.o(.text.Uart_GetRb8), (8 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_GetRb8), (8 bytes).
    Removing uart.o(.text.Uart_SetSaddr), (6 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_SetSaddr), (8 bytes).
    Removing uart.o(.text.Uart_EnableFunc), (14 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_EnableFunc), (8 bytes).
    Removing uart.o(.text.Uart_DisableFunc), (16 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_DisableFunc), (8 bytes).
    Removing uart.o(.text.Uart_GetIsr), (6 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_GetIsr), (8 bytes).
    Removing uart.o(.text.Uart_GetStatus), (10 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_GetStatus), (8 bytes).
    Removing uart.o(.text.Uart_ClrIsr), (8 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_ClrIsr), (8 bytes).
    Removing uart.o(.text.Uart_ClrStatus), (16 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_ClrStatus), (8 bytes).
    Removing uart.o(.text.Uart_SendDataPoll), (52 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_SendDataPoll), (8 bytes).
    Removing uart.o(.text.Uart_SendDataPollTimeOut), (106 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_SendDataPollTimeOut), (8 bytes).
    Removing uart.o(.text.Uart_SendDataIt), (14 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_SendDataIt), (8 bytes).
    Removing uart.o(.text.Uart_ReceiveData), (6 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_ReceiveData), (8 bytes).
    Removing uart.o(.text.Uart_Init), (116 bytes).
    Removing uart.o(.ARM.exidx.text.Uart_Init), (8 bytes).
    Removing vc.o(.text), (0 bytes).
    Removing vc.o(.text.Vc_CfgItType), (108 bytes).
    Removing vc.o(.ARM.exidx.text.Vc_CfgItType), (8 bytes).
    Removing vc.o(.text.Vc_ItCfg), (36 bytes).
    Removing vc.o(.ARM.exidx.text.Vc_ItCfg), (8 bytes).
    Removing vc.o(.text.Vc_GetItStatus), (16 bytes).
    Removing vc.o(.ARM.exidx.text.Vc_GetItStatus), (8 bytes).
    Removing vc.o(.text.Vc_ClearItStatus), (20 bytes).
    Removing vc.o(.ARM.exidx.text.Vc_ClearItStatus), (8 bytes).
    Removing vc.o(.text.Vc_DivInit), (72 bytes).
    Removing vc.o(.ARM.exidx.text.Vc_DivInit), (8 bytes).
    Removing vc.o(.text.Vc_Init), (356 bytes).
    Removing vc.o(.ARM.exidx.text.Vc_Init), (8 bytes).
    Removing vc.o(.text.Vc_Cmd), (36 bytes).
    Removing vc.o(.ARM.exidx.text.Vc_Cmd), (8 bytes).
    Removing wdt.o(.text), (0 bytes).
    Removing wdt.o(.text.Wdt_WriteWdtLoad), (20 bytes).
    Removing wdt.o(.ARM.exidx.text.Wdt_WriteWdtLoad), (8 bytes).
    Removing wdt.o(.text.Wdt_Init), (36 bytes).
    Removing wdt.o(.ARM.exidx.text.Wdt_Init), (8 bytes).
    Removing wdt.o(.text.Wdt_Start), (16 bytes).
    Removing wdt.o(.ARM.exidx.text.Wdt_Start), (8 bytes).
    Removing wdt.o(.text.Wdt_Feed), (16 bytes).
    Removing wdt.o(.ARM.exidx.text.Wdt_Feed), (8 bytes).
    Removing wdt.o(.ARM.exidx.text.Wdt_IrqClr), (8 bytes).
    Removing wdt.o(.text.Wdt_ReadWdtValue), (16 bytes).
    Removing wdt.o(.ARM.exidx.text.Wdt_ReadWdtValue), (8 bytes).
    Removing wdt.o(.text.Wdt_ReadwdtStatus), (16 bytes).
    Removing wdt.o(.ARM.exidx.text.Wdt_ReadwdtStatus), (8 bytes).
    Removing wdt.o(.ARM.exidx.text.Wdt_GetIrqStatus), (8 bytes).
    Removing wwdt.o(.text), (0 bytes).
    Removing wwdt.o(.text.WWDT_Init), (28 bytes).
    Removing wwdt.o(.ARM.exidx.text.WWDT_Init), (8 bytes).
    Removing wwdt.o(.text.WWDT_Start), (16 bytes).
    Removing wwdt.o(.ARM.exidx.text.WWDT_Start), (8 bytes).
    Removing wwdt.o(.text.WWDT_Feed), (16 bytes).
    Removing wwdt.o(.ARM.exidx.text.WWDT_Feed), (8 bytes).
    Removing wwdt.o(.text.WWDT_ClearPreOverFlag), (12 bytes).
    Removing wwdt.o(.ARM.exidx.text.WWDT_ClearPreOverFlag), (8 bytes).
    Removing wwdt.o(.text.WWDT_GetPreOverFlag), (16 bytes).
    Removing wwdt.o(.ARM.exidx.text.WWDT_GetPreOverFlag), (8 bytes).
    Removing wwdt.o(.text.WWDT_GetRunFlag), (16 bytes).
    Removing wwdt.o(.ARM.exidx.text.WWDT_GetRunFlag), (8 bytes).
    Removing wwdt.o(.text.WWDT_GetCnt), (16 bytes).
    Removing wwdt.o(.ARM.exidx.text.WWDT_GetCnt), (8 bytes).
    Removing wwdt.o(.text.WWDT_Reset), (12 bytes).
    Removing wwdt.o(.ARM.exidx.text.WWDT_Reset), (8 bytes).
    Removing singledampermodule.o(.text), (0 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Init_SingleDamper), (8 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Init_SingleDamperParm), (8 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Execute_SingleDamperDriver), (8 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Execute_SingleDamperControl), (8 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Control_SingleDamper), (8 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Antifreeze_SingleDamper), (8 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Reset_SingleDamper), (8 bytes).
    Removing singledampermodule.o(.text.Force_SingleDamperAction), (88 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Force_SingleDamperAction), (8 bytes).
    Removing singledampermodule.o(.text.Force_SingleDamperState), (72 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Force_SingleDamperState), (8 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Set_SingleDamperState), (8 bytes).
    Removing singledampermodule.o(.text.Pause_SingleDamperAction), (64 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Pause_SingleDamperAction), (8 bytes).
    Removing singledampermodule.o(.text.Get_SingleDamperResetState), (12 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Get_SingleDamperResetState), (8 bytes).
    Removing singledampermodule.o(.text.Get_SingleDamperPauseState), (12 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Get_SingleDamperPauseState), (8 bytes).
    Removing singledampermodule.o(.text.Get_SingleDamperRunningState), (12 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Get_SingleDamperRunningState), (8 bytes).
    Removing singledampermodule.o(.text.Get_SingleDamperState), (12 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Get_SingleDamperState), (8 bytes).
    Removing singledampermodule.o(.text.Get_SingleDamperHoldTimer), (16 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Get_SingleDamperHoldTimer), (8 bytes).
    Removing singledampermodule.o(.ARM.exidx.text.Config_IO_SingleDamperID0), (8 bytes).
    Removing systemtimermodule.o(.text), (0 bytes).
    Removing systemtimermodule.o(.text.Init_SystemTimer), (28 bytes).
    Removing systemtimermodule.o(.ARM.exidx.text.Init_SystemTimer), (8 bytes).
    Removing systemtimermodule.o(.text.Get_DayCount), (12 bytes).
    Removing systemtimermodule.o(.ARM.exidx.text.Get_DayCount), (8 bytes).
    Removing systemtimermodule.o(.text.Get_HourCount), (12 bytes).
    Removing systemtimermodule.o(.ARM.exidx.text.Get_HourCount), (8 bytes).
    Removing systemtimermodule.o(.text.Get_MinuteCount), (12 bytes).
    Removing systemtimermodule.o(.ARM.exidx.text.Get_MinuteCount), (8 bytes).
    Removing systemtimermodule.o(.ARM.exidx.text.Get_SecondCount), (8 bytes).
    Removing systemtimermodule.o(.text.Get_MSecCount), (12 bytes).
    Removing systemtimermodule.o(.ARM.exidx.text.Get_MSecCount), (8 bytes).
    Removing systemtimermodule.o(.text.Get_DayElapsedTime), (16 bytes).
    Removing systemtimermodule.o(.ARM.exidx.text.Get_DayElapsedTime), (8 bytes).
    Removing systemtimermodule.o(.text.Get_HourElapsedTime), (16 bytes).
    Removing systemtimermodule.o(.ARM.exidx.text.Get_HourElapsedTime), (8 bytes).
    Removing systemtimermodule.o(.text.Get_MinuteElapsedTime), (16 bytes).
    Removing systemtimermodule.o(.ARM.exidx.text.Get_MinuteElapsedTime), (8 bytes).
    Removing systemtimermodule.o(.ARM.exidx.text.Get_SecondElapsedTime), (8 bytes).
    Removing systemtimermodule.o(.text.Get_MSecElapsedTime), (16 bytes).
    Removing systemtimermodule.o(.ARM.exidx.text.Get_MSecElapsedTime), (8 bytes).
    Removing systemtimermodule.o(.text.Shorten_Timer), (104 bytes).
    Removing systemtimermodule.o(.ARM.exidx.text.Shorten_Timer), (8 bytes).
    Removing systemtimermodule.o(.ARM.exidx.text.Add_MSecCount), (8 bytes).
    Removing systemtimermodule.o(.rodata.Api), (48 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing startup_hc32l186.o(HEAP), (1024 bytes).
    Removing ramlog.o(.text), (0 bytes).
    Removing ramlog.o(.ARM.exidx.text.fputc), (8 bytes).
    Removing ramlog.o(.ARM.exidx.text.ramlog_putc), (8 bytes).
    Removing ramlog.o(.ARM.exidx.text.ramlog_puts), (8 bytes).
    Removing ramlog.o(.ARM.exidx.text.Panic), (8 bytes).
    Removing ramlog.o(.text.Debug_Init), (96 bytes).
    Removing ramlog.o(.ARM.exidx.text.Debug_Init), (8 bytes).
    Removing ramlog.o(.text.ramlog_init), (52 bytes).
    Removing ramlog.o(.ARM.exidx.text.ramlog_init), (8 bytes).
    Removing ramlog.o(.rodata.str1.1), (4 bytes).
    Removing cm_backtrace.o(.text), (0 bytes).
    Removing cm_backtrace.o(.text.cm_backtrace_init), (140 bytes).
    Removing cm_backtrace.o(.ARM.exidx.text.cm_backtrace_init), (8 bytes).
    Removing cm_backtrace.o(.ARM.exidx.text.cm_backtrace_firmware_info), (8 bytes).
    Removing cm_backtrace.o(.text.cm_backtrace_call_stack_any), (144 bytes).
    Removing cm_backtrace.o(.ARM.exidx.text.cm_backtrace_call_stack_any), (8 bytes).
    Removing cm_backtrace.o(.ARM.exidx.text.disassembly_ins_is_bl_blx), (8 bytes).
    Removing cm_backtrace.o(.ARM.exidx.text.cm_backtrace_call_stack), (8 bytes).
    Removing cm_backtrace.o(.text.cm_backtrace_assert), (132 bytes).
    Removing cm_backtrace.o(.ARM.exidx.text.cm_backtrace_assert), (8 bytes).
    Removing cm_backtrace.o(.ARM.exidx.text.dump_stack), (8 bytes).
    Removing cm_backtrace.o(.ARM.exidx.text.print_call_stack), (8 bytes).
    Removing cm_backtrace.o(.ARM.exidx.text.cm_backtrace_fault), (8 bytes).
    Removing steppermotorlib.o(.text), (0 bytes).
    Removing steppermotorlib.o(.text.__aeabi_assert), (2 bytes).
    Removing steppermotorlib.o(.ARM.exidx.text.__aeabi_assert), (8 bytes).
    Removing steppermotorlib.o(.ARM.exidx.text.Init_StepperMotor), (8 bytes).
    Removing steppermotorlib.o(.ARM.exidx.text.Drive_StepperMotorISR), (8 bytes).
    Removing steppermotorlib.o(.ARM.exidx.text.Set_StepperMotorTartget), (8 bytes).
    Removing steppermotorlib.o(.ARM.exidx.text.Stop_StepperMotorAction), (8 bytes).
    Removing steppermotorlib.o(.ARM.exidx.text.Pause_StepperMotorAction), (8 bytes).
    Removing steppermotorlib.o(.ARM.exidx.text.Get_StepperMotorIsRunning), (8 bytes).
    Removing steppermotorlib.o(.ARM.exidx.text.Get_StepperMotorIsPausing), (8 bytes).
    Removing steppermotorlib.o(.ARM.exidx.text.Get_StepperMotorRunSteps), (8 bytes).
    Removing steppermotorlib.o(.ARM.exidx.text.Get_StepperMotorRemainingSteps), (8 bytes).
    Removing fadd.o(.text), (178 bytes).
    Removing fdiv.o(.text), (124 bytes).
    Removing ffltui.o(.text), (14 bytes).
    Removing ffixi.o(.text), (50 bytes).
    Removing fepilogue.o(.text), (130 bytes).
    Removing dadd.o(.text), (356 bytes).
    Removing dmul.o(.text), (208 bytes).
    Removing ddiv.o(.text), (240 bytes).
    Removing dfixul.o(.text), (64 bytes).
    Removing cdrcmple.o(.text), (40 bytes).
    Removing depilogue.o(.text), (190 bytes).
    Removing depilogue.o(i.__ARM_clz), (46 bytes).

1308 unused section(s) (total 32598 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  printfstubs.o ABSOLUTE
    ../clib/microlib/stdio/puts.c            0x00000000   Number         0  puts.o ABSOLUTE
    ../clib/microlib/stdio/puts.c            0x00000000   Number         0  puts_e.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strncpy.c        0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\Debug\cm_backtrace\fault_handler\keil\cmb_fault.S 0x00000000   Number         0  cmb_fault.o ABSOLUTE
    ..\Startup\startup_hc32l186.s            0x00000000   Number         0  startup_hc32l186.o ABSOLUTE
    Adpt_ADC.c                               0x00000000   Number         0  adpt_adc.o ABSOLUTE
    Adpt_Clock.c                             0x00000000   Number         0  adpt_clock.o ABSOLUTE
    Adpt_Flash.c                             0x00000000   Number         0  adpt_flash.o ABSOLUTE
    Adpt_GPIO.c                              0x00000000   Number         0  adpt_gpio.o ABSOLUTE
    Adpt_Iwdg.c                              0x00000000   Number         0  adpt_iwdg.o ABSOLUTE
    Adpt_PWM.c                               0x00000000   Number         0  adpt_pwm.o ABSOLUTE
    Adpt_Reset.c                             0x00000000   Number         0  adpt_reset.o ABSOLUTE
    Adpt_TimeBase.c                          0x00000000   Number         0  adpt_timebase.o ABSOLUTE
    Init_Mcu.c                               0x00000000   Number         0  init_mcu.o ABSOLUTE
    SingleDamperModule.c                     0x00000000   Number         0  singledampermodule.o ABSOLUTE
    StepperMotorLib.c                        0x00000000   Number         0  steppermotorlib.o ABSOLUTE
    SystemTimerModule.c                      0x00000000   Number         0  systemtimermodule.o ABSOLUTE
    Timebase.c                               0x00000000   Number         0  timebase.o ABSOLUTE
    adc.c                                    0x00000000   Number         0  adc.o ABSOLUTE
    adt.c                                    0x00000000   Number         0  adt.o ABSOLUTE
    aes.c                                    0x00000000   Number         0  aes.o ABSOLUTE
    bgr.c                                    0x00000000   Number         0  bgr.o ABSOLUTE
    bt.c                                     0x00000000   Number         0  bt.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    cm_backtrace.c                           0x00000000   Number         0  cm_backtrace.o ABSOLUTE
    crc.c                                    0x00000000   Number         0  crc.o ABSOLUTE
    ctrim.c                                  0x00000000   Number         0  ctrim.o ABSOLUTE
    dac.c                                    0x00000000   Number         0  dac.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    ddl.c                                    0x00000000   Number         0  ddl.o ABSOLUTE
    debug.c                                  0x00000000   Number         0  debug.o ABSOLUTE
    dmac.c                                   0x00000000   Number         0  dmac.o ABSOLUTE
    flash.c                                  0x00000000   Number         0  flash.o ABSOLUTE
    gpio.c                                   0x00000000   Number         0  gpio.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    i2c.c                                    0x00000000   Number         0  i2c.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    interrupts_hc32l186.c                    0x00000000   Number         0  interrupts_hc32l186.o ABSOLUTE
    lcd.c                                    0x00000000   Number         0  lcd.o ABSOLUTE
    lpm.c                                    0x00000000   Number         0  lpm.o ABSOLUTE
    lptim.c                                  0x00000000   Number         0  lptim.o ABSOLUTE
    lpuart.c                                 0x00000000   Number         0  lpuart.o ABSOLUTE
    lvd.c                                    0x00000000   Number         0  lvd.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    opa.c                                    0x00000000   Number         0  opa.o ABSOLUTE
    pca.c                                    0x00000000   Number         0  pca.o ABSOLUTE
    pcnt.c                                   0x00000000   Number         0  pcnt.o ABSOLUTE
    ram.c                                    0x00000000   Number         0  ram.o ABSOLUTE
    ramlog.c                                 0x00000000   Number         0  ramlog.o ABSOLUTE
    reset.c                                  0x00000000   Number         0  reset.o ABSOLUTE
    rtc.c                                    0x00000000   Number         0  rtc.o ABSOLUTE
    spi.c                                    0x00000000   Number         0  spi.o ABSOLUTE
    sysctrl.c                                0x00000000   Number         0  sysctrl.o ABSOLUTE
    system_hc32l186.c                        0x00000000   Number         0  system_hc32l186.o ABSOLUTE
    timer3.c                                 0x00000000   Number         0  timer3.o ABSOLUTE
    trim.c                                   0x00000000   Number         0  trim.o ABSOLUTE
    trng.c                                   0x00000000   Number         0  trng.o ABSOLUTE
    uart.c                                   0x00000000   Number         0  uart.o ABSOLUTE
    vc.c                                     0x00000000   Number         0  vc.o ABSOLUTE
    wdt.c                                    0x00000000   Number         0  wdt.o ABSOLUTE
    wwdt.c                                   0x00000000   Number         0  wwdt.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_hc32l186.o(RESET)
    .ARM.Collect$$$$00000000                 0x000000c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x000000c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x000000c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x000000c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x000000c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x000000c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x000000d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x000000d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x000000d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x000000d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x000000d4   Section       60  startup_hc32l186.o(.text)
    .text                                    0x00000110   Section       12  cmb_fault.o(.text)
    .text                                    0x0000011c   Section        0  memseta.o(.text)
    .text                                    0x00000140   Section        0  uidiv_div0.o(.text)
    .text                                    0x0000017e   Section        0  uldiv.o(.text)
    .text                                    0x000001e0   Section       36  init.o(.text)
    .text                                    0x00000204   Section        0  llshl.o(.text)
    .text                                    0x00000224   Section        0  llushr.o(.text)
    [Anonymous Symbol]                       0x00000246   Section        0  system_hc32l186.o(.text.$Sub$$main)
    [Anonymous Symbol]                       0x00000254   Section        0  interrupts_hc32l186.o(.text.ADC_DAC_IRQHandler)
    [Anonymous Symbol]                       0x00000260   Section        0  adc.o(.text.Adc_ClrIrqStatus)
    __arm_cp.1_0                             0x0000026c   Number         4  adc.o(.text.Adc_ClrIrqStatus)
    [Anonymous Symbol]                       0x00000270   Section        0  adc.o(.text.Adc_GetIrqStatus)
    __arm_cp.0_0                             0x00000280   Number         4  adc.o(.text.Adc_GetIrqStatus)
    [Anonymous Symbol]                       0x00000284   Section        0  adc.o(.text.Adc_GetSqrResult)
    __arm_cp.24_0                            0x0000028c   Number         4  adc.o(.text.Adc_GetSqrResult)
    [Anonymous Symbol]                       0x00000290   Section        0  adpt_adc.o(.text.Adc_IRQHandler)
    __arm_cp.2_0                             0x000002f4   Number         4  adpt_adc.o(.text.Adc_IRQHandler)
    [Anonymous Symbol]                       0x000002f8   Section        0  adc.o(.text.Adc_SQR_Stop)
    __arm_cp.13_0                            0x00000300   Number         4  adc.o(.text.Adc_SQR_Stop)
    [Anonymous Symbol]                       0x00000304   Section        0  systemtimermodule.o(.text.Add_MSecCount)
    __arm_cp.12_0                            0x00000370   Number         4  systemtimermodule.o(.text.Add_MSecCount)
    AdtTimer2Init                            0x00000375   Thumb Code   164  adpt_pwm.o(.text.AdtTimer2Init)
    [Anonymous Symbol]                       0x00000374   Section        0  adpt_pwm.o(.text.AdtTimer2Init)
    __arm_cp.2_1                             0x00000418   Number         4  adpt_pwm.o(.text.AdtTimer2Init)
    [Anonymous Symbol]                       0x0000041c   Section        0  adpt_pwm.o(.text.AdtTimer3Init)
    __arm_cp.0_0                             0x000004d0   Number         4  adpt_pwm.o(.text.AdtTimer3Init)
    __arm_cp.0_1                             0x000004d4   Number         4  adpt_pwm.o(.text.AdtTimer3Init)
    Antifreeze_SingleDamper                  0x000004d9   Thumb Code    44  singledampermodule.o(.text.Antifreeze_SingleDamper)
    [Anonymous Symbol]                       0x000004d8   Section        0  singledampermodule.o(.text.Antifreeze_SingleDamper)
    [Anonymous Symbol]                       0x00000504   Section        0  adpt_clock.o(.text.App_SystemClkInit_RC48M)
    [Anonymous Symbol]                       0x00000534   Section        0  adpt_clock.o(.text.Board_InitClock)
    [Anonymous Symbol]                       0x00000540   Section        0  adpt_gpio.o(.text.Board_InitPins)
    __arm_cp.0_0                             0x00000834   Number         4  adpt_gpio.o(.text.Board_InitPins)
    [Anonymous Symbol]                       0x00000838   Section        0  adpt_reset.o(.text.Board_InitReset)
    __arm_cp.0_0                             0x00000848   Number         4  adpt_reset.o(.text.Board_InitReset)
    __arm_cp.0_1                             0x0000084c   Number         4  adpt_reset.o(.text.Board_InitReset)
    [Anonymous Symbol]                       0x00000850   Section        0  adpt_timebase.o(.text.Board_InitSysTick)
    __arm_cp.3_0                             0x00000870   Number         4  adpt_timebase.o(.text.Board_InitSysTick)
    [Anonymous Symbol]                       0x00000874   Section        0  adpt_pwm.o(.text.Board_InitTim)
    [Anonymous Symbol]                       0x00000888   Section        0  bt.o(.text.Bt_ClearIntFlag)
    __arm_cp.1_0                             0x00000898   Number         4  bt.o(.text.Bt_ClearIntFlag)
    [Anonymous Symbol]                       0x0000089c   Section        0  bt.o(.text.Bt_GetIntFlag)
    __arm_cp.0_0                             0x000008ac   Number         4  bt.o(.text.Bt_GetIntFlag)
    [Anonymous Symbol]                       0x000008b0   Section        0  bt.o(.text.Bt_M0_ARRSet)
    [Anonymous Symbol]                       0x000008c4   Section        0  bt.o(.text.Bt_M0_Cnt16Set)
    [Anonymous Symbol]                       0x000008d8   Section        0  bt.o(.text.Bt_M0_Run)
    [Anonymous Symbol]                       0x000008e8   Section        0  bt.o(.text.Bt_M23_ARRSet)
    [Anonymous Symbol]                       0x0000090c   Section        0  bt.o(.text.Bt_M23_CCR_Set)
    __arm_cp.34_0                            0x0000093c   Number         4  bt.o(.text.Bt_M23_CCR_Set)
    [Anonymous Symbol]                       0x00000940   Section        0  bt.o(.text.Bt_M23_Cnt16Set)
    __arm_cp.32_0                            0x00000954   Number         4  bt.o(.text.Bt_M23_Cnt16Set)
    __arm_cp.32_1                            0x00000958   Number         4  bt.o(.text.Bt_M23_Cnt16Set)
    [Anonymous Symbol]                       0x0000095c   Section        0  bt.o(.text.Bt_M23_EnPWM_Output)
    __arm_cp.28_0                            0x00000984   Number         4  bt.o(.text.Bt_M23_EnPWM_Output)
    [Anonymous Symbol]                       0x00000988   Section        0  bt.o(.text.Bt_M23_PortOutput_Cfg)
    __arm_cp.38_0                            0x00000a30   Number         4  bt.o(.text.Bt_M23_PortOutput_Cfg)
    __arm_cp.38_1                            0x00000a34   Number         4  bt.o(.text.Bt_M23_PortOutput_Cfg)
    [Anonymous Symbol]                       0x00000a38   Section        0  bt.o(.text.Bt_M23_Run)
    [Anonymous Symbol]                       0x00000a48   Section        0  bt.o(.text.Bt_M23_SetValidPeriod)
    __arm_cp.44_0                            0x00000a7c   Number         4  bt.o(.text.Bt_M23_SetValidPeriod)
    [Anonymous Symbol]                       0x00000a80   Section        0  bt.o(.text.Bt_Mode0_EnableIrq)
    [Anonymous Symbol]                       0x00000a94   Section        0  bt.o(.text.Bt_Mode0_Init)
    [Anonymous Symbol]                       0x00000b14   Section        0  bt.o(.text.Bt_Mode23_Init)
    __arm_cp.27_0                            0x00000ba4   Number         4  bt.o(.text.Bt_Mode23_Init)
    [Anonymous Symbol]                       0x00000ba8   Section        0  interrupts_hc32l186.o(.text.CLKTRIM_CTRIM_IRQHandler)
    Config_IO_SingleDamperID0                0x00000bb5   Thumb Code    76  singledampermodule.o(.text.Config_IO_SingleDamperID0)
    [Anonymous Symbol]                       0x00000bb4   Section        0  singledampermodule.o(.text.Config_IO_SingleDamperID0)
    Control_SingleDamper                     0x00000c01   Thumb Code   232  singledampermodule.o(.text.Control_SingleDamper)
    [Anonymous Symbol]                       0x00000c00   Section        0  singledampermodule.o(.text.Control_SingleDamper)
    __arm_cp.4_0                             0x00000ce8   Number         4  singledampermodule.o(.text.Control_SingleDamper)
    [Anonymous Symbol]                       0x00000cec   Section        0  interrupts_hc32l186.o(.text.DMAC_IRQHandler)
    Drive_StepperMotorISR                    0x00000cf5   Thumb Code   176  steppermotorlib.o(.text.Drive_StepperMotorISR)
    [Anonymous Symbol]                       0x00000cf4   Section        0  steppermotorlib.o(.text.Drive_StepperMotorISR)
    __arm_cp.2_0                             0x00000da4   Number         4  steppermotorlib.o(.text.Drive_StepperMotorISR)
    [Anonymous Symbol]                       0x00000da8   Section        0  interrupts_hc32l186.o(.text.EnableNvic)
    [Anonymous Symbol]                       0x00000dd0   Section        0  singledampermodule.o(.text.Execute_SingleDamperControl)
    [Anonymous Symbol]                       0x00000de4   Section        0  singledampermodule.o(.text.Execute_SingleDamperDriver)
    [Anonymous Symbol]                       0x00000df8   Section        0  interrupts_hc32l186.o(.text.FLASH_RAM_IRQHandler)
    [Anonymous Symbol]                       0x00000e04   Section        0  flash.o(.text.Flash_WaitCycle)
    __arm_cp.12_0                            0x00000e2c   Number         4  flash.o(.text.Flash_WaitCycle)
    __arm_cp.12_1                            0x00000e30   Number         4  flash.o(.text.Flash_WaitCycle)
    __arm_cp.12_2                            0x00000e34   Number         4  flash.o(.text.Flash_WaitCycle)
    [Anonymous Symbol]                       0x00000e38   Section        0  systemtimermodule.o(.text.Get_SecondCount)
    [Anonymous Symbol]                       0x00000e40   Section        0  systemtimermodule.o(.text.Get_SecondElapsedTime)
    __arm_cp.9_0                             0x00000e4c   Number         4  systemtimermodule.o(.text.Get_SecondElapsedTime)
    Get_StepperMotorIsPausing                0x00000e51   Thumb Code    16  steppermotorlib.o(.text.Get_StepperMotorIsPausing)
    [Anonymous Symbol]                       0x00000e50   Section        0  steppermotorlib.o(.text.Get_StepperMotorIsPausing)
    Get_StepperMotorIsRunning                0x00000e61   Thumb Code    16  steppermotorlib.o(.text.Get_StepperMotorIsRunning)
    [Anonymous Symbol]                       0x00000e60   Section        0  steppermotorlib.o(.text.Get_StepperMotorIsRunning)
    Get_StepperMotorRemainingSteps           0x00000e71   Thumb Code    24  steppermotorlib.o(.text.Get_StepperMotorRemainingSteps)
    [Anonymous Symbol]                       0x00000e70   Section        0  steppermotorlib.o(.text.Get_StepperMotorRemainingSteps)
    Get_StepperMotorRunSteps                 0x00000e89   Thumb Code    12  steppermotorlib.o(.text.Get_StepperMotorRunSteps)
    [Anonymous Symbol]                       0x00000e88   Section        0  steppermotorlib.o(.text.Get_StepperMotorRunSteps)
    [Anonymous Symbol]                       0x00000e94   Section        0  gpio.o(.text.Gpio_ClrIO)
    __arm_cp.8_0                             0x00000ea4   Number         4  gpio.o(.text.Gpio_ClrIO)
    [Anonymous Symbol]                       0x00000ea8   Section        0  gpio.o(.text.Gpio_Init)
    __arm_cp.0_0                             0x00000f30   Number         4  gpio.o(.text.Gpio_Init)
    __arm_cp.0_2                             0x00000f34   Number         4  gpio.o(.text.Gpio_Init)
    [Anonymous Symbol]                       0x00000f38   Section        0  gpio.o(.text.Gpio_SetAfMode)
    __arm_cp.11_0                            0x00000f44   Number         4  gpio.o(.text.Gpio_SetAfMode)
    [Anonymous Symbol]                       0x00000f48   Section        0  gpio.o(.text.Gpio_SetAnalogMode)
    __arm_cp.10_0                            0x00000f58   Number         4  gpio.o(.text.Gpio_SetAnalogMode)
    [Anonymous Symbol]                       0x00000f5c   Section        0  gpio.o(.text.Gpio_SetIO)
    __arm_cp.6_0                             0x00000f6c   Number         4  gpio.o(.text.Gpio_SetIO)
    [Anonymous Symbol]                       0x00000f70   Section        0  interrupts_hc32l186.o(.text.I2C0_IRQHandler)
    [Anonymous Symbol]                       0x00000f78   Section        0  interrupts_hc32l186.o(.text.I2C1_IRQHandler)
    [Anonymous Symbol]                       0x00000f80   Section        0  timebase.o(.text.ISR_Timer_1ms)
    __arm_cp.0_0                             0x00000f90   Number         4  timebase.o(.text.ISR_Timer_1ms)
    [Anonymous Symbol]                       0x00000f94   Section        0  timebase.o(.text.ISR_Timer_500us)
    __arm_cp.1_0                             0x00000fac   Number         4  timebase.o(.text.ISR_Timer_500us)
    [Anonymous Symbol]                       0x00000fb0   Section        0  init_mcu.o(.text.Init_Mcu)
    [Anonymous Symbol]                       0x00000fc8   Section        0  singledampermodule.o(.text.Init_SingleDamper)
    Init_SingleDamperParm                    0x00000fd5   Thumb Code    32  singledampermodule.o(.text.Init_SingleDamperParm)
    [Anonymous Symbol]                       0x00000fd4   Section        0  singledampermodule.o(.text.Init_SingleDamperParm)
    __arm_cp.1_1                             0x00000ff4   Number         4  singledampermodule.o(.text.Init_SingleDamperParm)
    __arm_cp.1_2                             0x00000ff8   Number         4  singledampermodule.o(.text.Init_SingleDamperParm)
    [Anonymous Symbol]                       0x00000ffc   Section        0  steppermotorlib.o(.text.Init_StepperMotor)
    __arm_cp.1_0                             0x00001018   Number         4  steppermotorlib.o(.text.Init_StepperMotor)
    [Anonymous Symbol]                       0x0000101c   Section        0  interrupts_hc32l186.o(.text.LCD_IRQHandler)
    [Anonymous Symbol]                       0x00001024   Section        0  interrupts_hc32l186.o(.text.LPTIM0_1_IRQHandler)
    [Anonymous Symbol]                       0x00001030   Section        0  interrupts_hc32l186.o(.text.LPUART0_IRQHandler)
    [Anonymous Symbol]                       0x00001038   Section        0  interrupts_hc32l186.o(.text.LPUART1_IRQHandler)
    [Anonymous Symbol]                       0x00001040   Section        0  interrupts_hc32l186.o(.text.LVD_IRQHandler)
    [Anonymous Symbol]                       0x00001048   Section        0  interrupts_hc32l186.o(.text.PCA_WWDT_IRQHandler)
    [Anonymous Symbol]                       0x00001054   Section        0  interrupts_hc32l186.o(.text.PCNT_IRQHandler)
    [Anonymous Symbol]                       0x0000105c   Section        0  interrupts_hc32l186.o(.text.PORTA_IRQHandler)
    [Anonymous Symbol]                       0x00001064   Section        0  interrupts_hc32l186.o(.text.PORTB_IRQHandler)
    [Anonymous Symbol]                       0x0000106c   Section        0  interrupts_hc32l186.o(.text.PORTC_E_IRQHandler)
    [Anonymous Symbol]                       0x00001078   Section        0  interrupts_hc32l186.o(.text.PORTD_F_IRQHandler)
    [Anonymous Symbol]                       0x00001084   Section        0  ramlog.o(.text.Panic)
    Pause_StepperMotorAction                 0x00001087   Thumb Code    22  steppermotorlib.o(.text.Pause_StepperMotorAction)
    [Anonymous Symbol]                       0x00001086   Section        0  steppermotorlib.o(.text.Pause_StepperMotorAction)
    [Anonymous Symbol]                       0x0000109c   Section        0  interrupts_hc32l186.o(.text.RTC_IRQHandler)
    [Anonymous Symbol]                       0x000010a4   Section        0  singledampermodule.o(.text.Reset_SingleDamper)
    [Anonymous Symbol]                       0x000010c0   Section        0  interrupts_hc32l186.o(.text.SPI0_IRQHandler)
    [Anonymous Symbol]                       0x000010c8   Section        0  interrupts_hc32l186.o(.text.SPI1_IRQHandler)
    [Anonymous Symbol]                       0x000010d0   Section        0  ddl.o(.text.SetBit)
    [Anonymous Symbol]                       0x000010e8   Section        0  singledampermodule.o(.text.Set_SingleDamperState)
    __arm_cp.9_0                             0x00001104   Number         4  singledampermodule.o(.text.Set_SingleDamperState)
    Set_StepperMotorTartget                  0x00001109   Thumb Code    38  steppermotorlib.o(.text.Set_StepperMotorTartget)
    [Anonymous Symbol]                       0x00001108   Section        0  steppermotorlib.o(.text.Set_StepperMotorTartget)
    Stop_StepperMotorAction                  0x0000112f   Thumb Code    16  steppermotorlib.o(.text.Stop_StepperMotorAction)
    [Anonymous Symbol]                       0x0000112e   Section        0  steppermotorlib.o(.text.Stop_StepperMotorAction)
    SysTick_Config                           0x00001141   Thumb Code    32  adpt_timebase.o(.text.SysTick_Config)
    [Anonymous Symbol]                       0x00001140   Section        0  adpt_timebase.o(.text.SysTick_Config)
    [Anonymous Symbol]                       0x00001160   Section        0  interrupts_hc32l186.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x00001168   Section        0  adpt_timebase.o(.text.SysTick_IRQHandler)
    [Anonymous Symbol]                       0x00001174   Section        0  sysctrl.o(.text.Sysctrl_ClkSourceEnable)
    __arm_cp.0_1                             0x000012d0   Number         4  sysctrl.o(.text.Sysctrl_ClkSourceEnable)
    __arm_cp.0_2                             0x000012d4   Number         4  sysctrl.o(.text.Sysctrl_ClkSourceEnable)
    __arm_cp.0_3                             0x000012d8   Number         4  sysctrl.o(.text.Sysctrl_ClkSourceEnable)
    [Anonymous Symbol]                       0x000012dc   Section        0  sysctrl.o(.text.Sysctrl_GetHClkFreq)
    __arm_cp.3_0                             0x0000141c   Number         4  sysctrl.o(.text.Sysctrl_GetHClkFreq)
    __arm_cp.3_2                             0x00001420   Number         4  sysctrl.o(.text.Sysctrl_GetHClkFreq)
    __arm_cp.3_3                             0x00001424   Number         4  sysctrl.o(.text.Sysctrl_GetHClkFreq)
    __arm_cp.3_4                             0x00001428   Number         4  sysctrl.o(.text.Sysctrl_GetHClkFreq)
    __arm_cp.3_6                             0x0000142c   Number         4  sysctrl.o(.text.Sysctrl_GetHClkFreq)
    __arm_cp.3_7                             0x00001430   Number         4  sysctrl.o(.text.Sysctrl_GetHClkFreq)
    __arm_cp.3_8                             0x00001434   Number         4  sysctrl.o(.text.Sysctrl_GetHClkFreq)
    __arm_cp.3_10                            0x00001438   Number         4  sysctrl.o(.text.Sysctrl_GetHClkFreq)
    __arm_cp.3_11                            0x0000143c   Number         4  sysctrl.o(.text.Sysctrl_GetHClkFreq)
    [Anonymous Symbol]                       0x00001440   Section        0  sysctrl.o(.text.Sysctrl_SetPeripheralGate)
    __arm_cp.20_0                            0x00001464   Number         4  sysctrl.o(.text.Sysctrl_SetPeripheralGate)
    [Anonymous Symbol]                       0x00001468   Section        0  sysctrl.o(.text.Sysctrl_SetRC48MTrim)
    __arm_cp.17_1                            0x00001478   Number         4  sysctrl.o(.text.Sysctrl_SetRC48MTrim)
    [Anonymous Symbol]                       0x0000147c   Section        0  sysctrl.o(.text.Sysctrl_SysClkSwitch)
    __arm_cp.2_0                             0x0000149c   Number         4  sysctrl.o(.text.Sysctrl_SysClkSwitch)
    [Anonymous Symbol]                       0x000014a0   Section        0  system_hc32l186.o(.text.SystemCoreClockUpdate)
    __arm_cp.0_0                             0x000014ac   Number         4  system_hc32l186.o(.text.SystemCoreClockUpdate)
    [Anonymous Symbol]                       0x000014b0   Section        0  system_hc32l186.o(.text.SystemInit)
    __arm_cp.1_0                             0x000014f0   Number         4  system_hc32l186.o(.text.SystemInit)
    __arm_cp.1_1                             0x000014f4   Number         4  system_hc32l186.o(.text.SystemInit)
    __arm_cp.1_2                             0x000014f8   Number         4  system_hc32l186.o(.text.SystemInit)
    __arm_cp.1_3                             0x000014fc   Number         4  system_hc32l186.o(.text.SystemInit)
    __arm_cp.1_4                             0x00001500   Number         4  system_hc32l186.o(.text.SystemInit)
    __arm_cp.1_5                             0x00001504   Number         4  system_hc32l186.o(.text.SystemInit)
    __arm_cp.1_6                             0x00001508   Number         4  system_hc32l186.o(.text.SystemInit)
    __arm_cp.1_7                             0x0000150c   Number         4  system_hc32l186.o(.text.SystemInit)
    __arm_cp.1_8                             0x00001510   Number         4  system_hc32l186.o(.text.SystemInit)
    [Anonymous Symbol]                       0x00001514   Section        0  interrupts_hc32l186.o(.text.TIM0_IRQHandler)
    [Anonymous Symbol]                       0x0000151c   Section        0  interrupts_hc32l186.o(.text.TIM1_IRQHandler)
    [Anonymous Symbol]                       0x00001524   Section        0  interrupts_hc32l186.o(.text.TIM2_IRQHandler)
    [Anonymous Symbol]                       0x0000152c   Section        0  interrupts_hc32l186.o(.text.TIM3_IRQHandler)
    [Anonymous Symbol]                       0x00001534   Section        0  interrupts_hc32l186.o(.text.TIM4_IRQHandler)
    [Anonymous Symbol]                       0x0000153c   Section        0  interrupts_hc32l186.o(.text.TIM5_IRQHandler)
    [Anonymous Symbol]                       0x00001544   Section        0  interrupts_hc32l186.o(.text.TIM6_IRQHandler)
    [Anonymous Symbol]                       0x0000154c   Section        0  adpt_timebase.o(.text.Tim0_IRQHandler)
    [Anonymous Symbol]                       0x00001568   Section        0  timer3.o(.text.Tim3_M23_ARRSet)
    __arm_cp.31_0                            0x00001588   Number         4  timer3.o(.text.Tim3_M23_ARRSet)
    [Anonymous Symbol]                       0x0000158c   Section        0  timer3.o(.text.Tim3_M23_CCR_Set)
    __arm_cp.34_0                            0x00001594   Number         4  timer3.o(.text.Tim3_M23_CCR_Set)
    [Anonymous Symbol]                       0x00001598   Section        0  timer3.o(.text.Tim3_M23_Cnt16Set)
    __arm_cp.32_0                            0x000015a8   Number         4  timer3.o(.text.Tim3_M23_Cnt16Set)
    __arm_cp.32_1                            0x000015ac   Number         4  timer3.o(.text.Tim3_M23_Cnt16Set)
    [Anonymous Symbol]                       0x000015b0   Section        0  timer3.o(.text.Tim3_M23_EnPWM_Output)
    __arm_cp.28_0                            0x000015d8   Number         4  timer3.o(.text.Tim3_M23_EnPWM_Output)
    [Anonymous Symbol]                       0x000015dc   Section        0  timer3.o(.text.Tim3_M23_PortOutput_Cfg)
    __arm_cp.38_0                            0x000017dc   Number         4  timer3.o(.text.Tim3_M23_PortOutput_Cfg)
    [Anonymous Symbol]                       0x000017e0   Section        0  timer3.o(.text.Tim3_M23_Run)
    [Anonymous Symbol]                       0x000017f0   Section        0  timer3.o(.text.Tim3_M23_SetValidPeriod)
    __arm_cp.45_0                            0x00001820   Number         4  timer3.o(.text.Tim3_M23_SetValidPeriod)
    [Anonymous Symbol]                       0x00001824   Section        0  timer3.o(.text.Tim3_Mode23_Init)
    __arm_cp.27_0                            0x000018b0   Number         4  timer3.o(.text.Tim3_Mode23_Init)
    [Anonymous Symbol]                       0x000018b4   Section        0  adpt_timebase.o(.text.Time0Init)
    __arm_cp.0_0                             0x00001934   Number         4  adpt_timebase.o(.text.Time0Init)
    __arm_cp.0_1                             0x00001938   Number         4  adpt_timebase.o(.text.Time0Init)
    __arm_cp.0_2                             0x0000193c   Number         4  adpt_timebase.o(.text.Time0Init)
    [Anonymous Symbol]                       0x00001940   Section        0  interrupts_hc32l186.o(.text.UART0_2_IRQHandler)
    [Anonymous Symbol]                       0x0000194c   Section        0  interrupts_hc32l186.o(.text.UART1_3_IRQHandler)
    [Anonymous Symbol]                       0x00001958   Section        0  interrupts_hc32l186.o(.text.VC0_IRQHandler)
    [Anonymous Symbol]                       0x00001960   Section        0  interrupts_hc32l186.o(.text.VC1_2_IRQHandler)
    [Anonymous Symbol]                       0x0000196c   Section        0  interrupts_hc32l186.o(.text.WDT_IRQHandler)
    [Anonymous Symbol]                       0x00001974   Section        0  wdt.o(.text.Wdt_GetIrqStatus)
    __arm_cp.7_0                             0x00001980   Number         4  wdt.o(.text.Wdt_GetIrqStatus)
    [Anonymous Symbol]                       0x00001984   Section        0  adpt_iwdg.o(.text.Wdt_IRQHandler)
    [Anonymous Symbol]                       0x0000198c   Section        0  wdt.o(.text.Wdt_IrqClr)
    __arm_cp.4_0                             0x00001998   Number         4  wdt.o(.text.Wdt_IrqClr)
    _SysctrlUnlock                           0x0000199d   Thumb Code    12  sysctrl.o(.text._SysctrlUnlock)
    [Anonymous Symbol]                       0x0000199c   Section        0  sysctrl.o(.text._SysctrlUnlock)
    __arm_cp.1_0                             0x000019a8   Number         4  sysctrl.o(.text._SysctrlUnlock)
    __arm_cp.1_1                             0x000019ac   Number         4  sysctrl.o(.text._SysctrlUnlock)
    __arm_cp.1_2                             0x000019b0   Number         4  sysctrl.o(.text._SysctrlUnlock)
    __NVIC_ClearPendingIRQ                   0x000019b5   Thumb Code    20  interrupts_hc32l186.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x000019b4   Section        0  interrupts_hc32l186.o(.text.__NVIC_ClearPendingIRQ)
    __arm_cp.1_0                             0x000019c8   Number         4  interrupts_hc32l186.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_DisableIRQ                        0x000019cd   Thumb Code    28  interrupts_hc32l186.o(.text.__NVIC_DisableIRQ)
    [Anonymous Symbol]                       0x000019cc   Section        0  interrupts_hc32l186.o(.text.__NVIC_DisableIRQ)
    __arm_cp.4_0                             0x000019e8   Number         4  interrupts_hc32l186.o(.text.__NVIC_DisableIRQ)
    __NVIC_EnableIRQ                         0x000019ed   Thumb Code    20  interrupts_hc32l186.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x000019ec   Section        0  interrupts_hc32l186.o(.text.__NVIC_EnableIRQ)
    __arm_cp.3_0                             0x00001a00   Number         4  interrupts_hc32l186.o(.text.__NVIC_EnableIRQ)
    __NVIC_SetPriority                       0x00001a05   Thumb Code    20  adpt_timebase.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x00001a04   Section        0  adpt_timebase.o(.text.__NVIC_SetPriority)
    __arm_cp.5_0                             0x00001a18   Number         4  adpt_timebase.o(.text.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x00001a1d   Thumb Code    56  interrupts_hc32l186.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x00001a1c   Section        0  interrupts_hc32l186.o(.text.__NVIC_SetPriority)
    __arm_cp.2_0                             0x00001a54   Number         4  interrupts_hc32l186.o(.text.__NVIC_SetPriority)
    __arm_cp.2_1                             0x00001a58   Number         4  interrupts_hc32l186.o(.text.__NVIC_SetPriority)
    __NVIC_SystemReset                       0x00001a5d   Thumb Code    20  adpt_iwdg.o(.text.__NVIC_SystemReset)
    [Anonymous Symbol]                       0x00001a5c   Section        0  adpt_iwdg.o(.text.__NVIC_SystemReset)
    __arm_cp.5_0                             0x00001a70   Number         4  adpt_iwdg.o(.text.__NVIC_SystemReset)
    __arm_cp.5_1                             0x00001a74   Number         4  adpt_iwdg.o(.text.__NVIC_SystemReset)
    [Anonymous Symbol]                       0x00001a78   Section        0  cm_backtrace.o(.text.cm_backtrace_call_stack)
    __arm_cp.4_7                             0x00001b88   Number         4  cm_backtrace.o(.text.cm_backtrace_call_stack)
    __arm_cp.4_8                             0x00001b8c   Number         4  cm_backtrace.o(.text.cm_backtrace_call_stack)
    __arm_cp.4_10                            0x00001b90   Number         4  cm_backtrace.o(.text.cm_backtrace_call_stack)
    __arm_cp.4_11                            0x00001b94   Number         4  cm_backtrace.o(.text.cm_backtrace_call_stack)
    [Anonymous Symbol]                       0x00001b98   Section        0  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_0                             0x00001cc8   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_1                             0x00001ccc   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_2                             0x00001cd0   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_3                             0x00001cd4   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_4                             0x00001cd8   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_5                             0x00001cdc   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_6                             0x00001ce0   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_7                             0x00001ce4   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_9                             0x00001cec   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_10                            0x00001cf0   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_12                            0x00001d28   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_13                            0x00001d2c   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_14                            0x00001d30   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_15                            0x00001d34   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_16                            0x00001d38   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_17                            0x00001d3c   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_18                            0x00001d40   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_19                            0x00001d44   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_20                            0x00001d48   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_21                            0x00001d4c   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_22                            0x00001d50   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_23                            0x00001d54   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_24                            0x00001d58   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_25                            0x00001d5c   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_26                            0x00001d60   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    __arm_cp.8_27                            0x00001d64   Number         4  cm_backtrace.o(.text.cm_backtrace_fault)
    [Anonymous Symbol]                       0x00001db4   Section        0  cm_backtrace.o(.text.cm_backtrace_firmware_info)
    __arm_cp.1_1                             0x00001e0c   Number         4  cm_backtrace.o(.text.cm_backtrace_firmware_info)
    __arm_cp.1_2                             0x00001e10   Number         4  cm_backtrace.o(.text.cm_backtrace_firmware_info)
    __arm_cp.1_3                             0x00001e14   Number         4  cm_backtrace.o(.text.cm_backtrace_firmware_info)
    [Anonymous Symbol]                       0x00001e1c   Section        0  ddl.o(.text.ddl_memclr)
    [Anonymous Symbol]                       0x00001e2c   Section        0  ddl.o(.text.delay10us)
    __arm_cp.4_2                             0x00001e64   Number         4  ddl.o(.text.delay10us)
    [Anonymous Symbol]                       0x00001e68   Section        0  ddl.o(.text.delay1ms)
    __arm_cp.2_0                             0x00001ea0   Number         4  ddl.o(.text.delay1ms)
    __arm_cp.2_1                             0x00001ea4   Number         4  ddl.o(.text.delay1ms)
    disassembly_ins_is_bl_blx                0x00001ea9   Thumb Code    44  cm_backtrace.o(.text.disassembly_ins_is_bl_blx)
    [Anonymous Symbol]                       0x00001ea8   Section        0  cm_backtrace.o(.text.disassembly_ins_is_bl_blx)
    __arm_cp.3_0                             0x00001ed4   Number         4  cm_backtrace.o(.text.disassembly_ins_is_bl_blx)
    dump_stack                               0x00001ed9   Thumb Code   116  cm_backtrace.o(.text.dump_stack)
    [Anonymous Symbol]                       0x00001ed8   Section        0  cm_backtrace.o(.text.dump_stack)
    __arm_cp.6_0                             0x00001f4c   Number         4  cm_backtrace.o(.text.dump_stack)
    __arm_cp.6_3                             0x00001f7c   Number         4  cm_backtrace.o(.text.dump_stack)
    __arm_cp.6_6                             0x00001fc4   Number         4  cm_backtrace.o(.text.dump_stack)
    [Anonymous Symbol]                       0x00001fc8   Section        0  ramlog.o(.text.fputc)
    [Anonymous Symbol]                       0x00001fe0   Section        0  main.o(.text.main)
    print_call_stack                         0x0000204d   Thumb Code   100  cm_backtrace.o(.text.print_call_stack)
    [Anonymous Symbol]                       0x0000204c   Section        0  cm_backtrace.o(.text.print_call_stack)
    __arm_cp.7_0                             0x000020b0   Number         4  cm_backtrace.o(.text.print_call_stack)
    __arm_cp.7_3                             0x000020fc   Number         4  cm_backtrace.o(.text.print_call_stack)
    __arm_cp.7_6                             0x0000210c   Number         4  cm_backtrace.o(.text.print_call_stack)
    ramlog_putc                              0x00002131   Thumb Code    36  ramlog.o(.text.ramlog_putc)
    [Anonymous Symbol]                       0x00002130   Section        0  ramlog.o(.text.ramlog_putc)
    __arm_cp.1_0                             0x00002154   Number         4  ramlog.o(.text.ramlog_putc)
    __arm_cp.1_1                             0x00002158   Number         4  ramlog.o(.text.ramlog_putc)
    ramlog_puts                              0x0000215d   Thumb Code    20  ramlog.o(.text.ramlog_puts)
    [Anonymous Symbol]                       0x0000215c   Section        0  ramlog.o(.text.ramlog_puts)
    [Anonymous Symbol]                       0x00002178   Section        0  adpt_iwdg.o(.text.software_reset)
    i.__0printf$8                            0x000021d4   Section        0  printf8.o(i.__0printf$8)
    i.__0sprintf$8                           0x000021f4   Section        0  printf8.o(i.__0sprintf$8)
    i.__scatterload_copy                     0x0000221c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0000222a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0000222c   Section       14  handlers.o(i.__scatterload_zeroinit)
    _printf_core                             0x0000223d   Thumb Code  1020  printf8.o(i._printf_core)
    i._printf_core                           0x0000223c   Section        0  printf8.o(i._printf_core)
    _printf_post_padding                     0x00002665   Thumb Code    32  printf8.o(i._printf_post_padding)
    i._printf_post_padding                   0x00002664   Section        0  printf8.o(i._printf_post_padding)
    _printf_pre_padding                      0x00002685   Thumb Code    44  printf8.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x00002684   Section        0  printf8.o(i._printf_pre_padding)
    _sputc                                   0x000026b1   Thumb Code    10  printf8.o(i._sputc)
    i._sputc                                 0x000026b0   Section        0  printf8.o(i._sputc)
    i.puts                                   0x000026bc   Section        0  puts.o(i.puts)
    Api                                      0x000026dc   Data          32  steppermotorlib.o(.rodata.Api)
    [Anonymous Symbol]                       0x000026dc   Section        0  steppermotorlib.o(.rodata.Api)
    ary_IO_State_Lb1205m4Beats               0x000026fc   Data           4  steppermotorlib.o(.rodata.ary_IO_State_Lb1205m4Beats)
    [Anonymous Symbol]                       0x000026fc   Section        0  steppermotorlib.o(.rodata.ary_IO_State_Lb1205m4Beats)
    ary_IO_State_Lb1205m8Beats               0x00002700   Data           8  steppermotorlib.o(.rodata.ary_IO_State_Lb1205m8Beats)
    [Anonymous Symbol]                       0x00002700   Section        0  steppermotorlib.o(.rodata.ary_IO_State_Lb1205m8Beats)
    ary_IO_State_Lb1909m4Beats               0x00002708   Data           4  steppermotorlib.o(.rodata.ary_IO_State_Lb1909m4Beats)
    [Anonymous Symbol]                       0x00002708   Section        0  steppermotorlib.o(.rodata.ary_IO_State_Lb1909m4Beats)
    ary_SingleDamper_RunSteps                0x0000270c   Data          98  singledampermodule.o(.rodata.ary_SingleDamper_RunSteps)
    [Anonymous Symbol]                       0x0000270c   Section        0  singledampermodule.o(.rodata.ary_SingleDamper_RunSteps)
    ary_StepperMotorStepState                0x00002770   Data          24  steppermotorlib.o(.rodata.ary_StepperMotorStepState)
    [Anonymous Symbol]                       0x00002770   Section        0  steppermotorlib.o(.rodata.ary_StepperMotorStepState)
    st_SingleDamperConPara                   0x00002788   Data           6  singledampermodule.o(.rodata.st_SingleDamperConPara)
    [Anonymous Symbol]                       0x00002788   Section        0  singledampermodule.o(.rodata.st_SingleDamperConPara)
    [Anonymous Symbol]                       0x0000278e   Section        0  cm_backtrace.o(.rodata.str1.1)
    .data                                    0x20000000   Section        4  stdout.o(.data)
    ary_SingleDamper                         0x20000008   Data          44  singledampermodule.o(.bss.ary_SingleDamper)
    [Anonymous Symbol]                       0x20000008   Section        0  singledampermodule.o(.bss.ary_SingleDamper)
    call_stack_info                          0x20000034   Data         288  cm_backtrace.o(.bss.call_stack_info)
    [Anonymous Symbol]                       0x20000034   Section        0  cm_backtrace.o(.bss.call_stack_info)
    code_size                                0x20000154   Data           1  cm_backtrace.o(.bss.code_size)
    [Anonymous Symbol]                       0x20000154   Section        0  cm_backtrace.o(.bss.code_size)
    code_start_addr                          0x20000158   Data           1  cm_backtrace.o(.bss.code_start_addr)
    [Anonymous Symbol]                       0x20000158   Section        0  cm_backtrace.o(.bss.code_start_addr)
    fw_name                                  0x20000159   Data          33  cm_backtrace.o(.bss.fw_name)
    [Anonymous Symbol]                       0x20000159   Section        0  cm_backtrace.o(.bss.fw_name)
    hw_ver                                   0x2000017a   Data          33  cm_backtrace.o(.bss.hw_ver)
    [Anonymous Symbol]                       0x2000017a   Section        0  cm_backtrace.o(.bss.hw_ver)
    init_ok                                  0x2000019b   Data           1  cm_backtrace.o(.bss.init_ok)
    [Anonymous Symbol]                       0x2000019b   Section        0  cm_backtrace.o(.bss.init_ok)
    main_stack_size                          0x2000019c   Data           1  cm_backtrace.o(.bss.main_stack_size)
    [Anonymous Symbol]                       0x2000019c   Section        0  cm_backtrace.o(.bss.main_stack_size)
    main_stack_start_addr                    0x200001a0   Data           1  cm_backtrace.o(.bss.main_stack_start_addr)
    [Anonymous Symbol]                       0x200001a0   Section        0  cm_backtrace.o(.bss.main_stack_start_addr)
    on_fault                                 0x200001a1   Data           1  cm_backtrace.o(.bss.on_fault)
    [Anonymous Symbol]                       0x200001a1   Section        0  cm_backtrace.o(.bss.on_fault)
    regs.0.4                                 0x200001a4   Data           4  cm_backtrace.o(.bss.regs.0.4)
    [Anonymous Symbol]                       0x200001a4   Section        0  cm_backtrace.o(.bss.regs.0.4)
    regs.0.5                                 0x200001a8   Data           4  cm_backtrace.o(.bss.regs.0.5)
    [Anonymous Symbol]                       0x200001a8   Section        0  cm_backtrace.o(.bss.regs.0.5)
    regs.0.6                                 0x200001ac   Data           4  cm_backtrace.o(.bss.regs.0.6)
    [Anonymous Symbol]                       0x200001ac   Section        0  cm_backtrace.o(.bss.regs.0.6)
    regs.0.7.0                               0x200001b0   Data           4  cm_backtrace.o(.bss.regs.0.7.0)
    [Anonymous Symbol]                       0x200001b0   Section        0  cm_backtrace.o(.bss.regs.0.7.0)
    stack_is_overflow                        0x200001c8   Data           1  cm_backtrace.o(.bss.stack_is_overflow)
    [Anonymous Symbol]                       0x200001c8   Section        0  cm_backtrace.o(.bss.stack_is_overflow)
    sw_ver                                   0x200001e8   Data          33  cm_backtrace.o(.bss.sw_ver)
    [Anonymous Symbol]                       0x200001e8   Section        0  cm_backtrace.o(.bss.sw_ver)
    u32_ResetFlag                            0x20000230   Data           4  adpt_reset.o(.bss.u32_ResetFlag)
    [Anonymous Symbol]                       0x20000230   Section        0  adpt_reset.o(.bss.u32_ResetFlag)
    u8_Millisec                              0x20000234   Data           1  timebase.o(.bss.u8_Millisec)
    [Anonymous Symbol]                       0x20000234   Section        0  timebase.o(.bss.u8_Millisec)
    u8_OneMsecCount                          0x20000235   Data           1  timebase.o(.bss.u8_OneMsecCount)
    [Anonymous Symbol]                       0x20000235   Section        0  timebase.o(.bss.u8_OneMsecCount)
    STACK                                    0x20000238   Section     4096  startup_hc32l186.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$WCHAR32$ENUMINT$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    Image$$ER_IROM1$$Base                    0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           0  startup_hc32l186.o(RESET)
    _printf_a                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    ClkTrim_IRQHandler                        - Undefined Weak Reference
    Ctrim_IRQHandler                          - Undefined Weak Reference
    Dac_IRQHandler                            - Undefined Weak Reference
    Dmac_IRQHandler                           - Undefined Weak Reference
    Flash_IRQHandler                          - Undefined Weak Reference
    I2c0_IRQHandler                           - Undefined Weak Reference
    I2c1_IRQHandler                           - Undefined Weak Reference
    Lcd_IRQHandler                            - Undefined Weak Reference
    LpTim0_IRQHandler                         - Undefined Weak Reference
    LpTim1_IRQHandler                         - Undefined Weak Reference
    LpUart0_IRQHandler                        - Undefined Weak Reference
    LpUart1_IRQHandler                        - Undefined Weak Reference
    Lvd_IRQHandler                            - Undefined Weak Reference
    Pca_IRQHandler                            - Undefined Weak Reference
    Pcnt_IRQHandler                           - Undefined Weak Reference
    PortA_IRQHandler                          - Undefined Weak Reference
    PortB_IRQHandler                          - Undefined Weak Reference
    PortC_IRQHandler                          - Undefined Weak Reference
    PortD_IRQHandler                          - Undefined Weak Reference
    PortE_IRQHandler                          - Undefined Weak Reference
    PortF_IRQHandler                          - Undefined Weak Reference
    Ram_IRQHandler                            - Undefined Weak Reference
    Rtc_IRQHandler                            - Undefined Weak Reference
    Spi0_IRQHandler                           - Undefined Weak Reference
    Spi1_IRQHandler                           - Undefined Weak Reference
    Tim1_IRQHandler                           - Undefined Weak Reference
    Tim2_IRQHandler                           - Undefined Weak Reference
    Tim3_IRQHandler                           - Undefined Weak Reference
    Tim4_IRQHandler                           - Undefined Weak Reference
    Tim5_IRQHandler                           - Undefined Weak Reference
    Tim6_IRQHandler                           - Undefined Weak Reference
    Uart0_IRQHandler                          - Undefined Weak Reference
    Uart1_IRQHandler                          - Undefined Weak Reference
    Uart2_IRQHandler                          - Undefined Weak Reference
    Uart3_IRQHandler                          - Undefined Weak Reference
    Vc0_IRQHandler                            - Undefined Weak Reference
    Vc1_IRQHandler                            - Undefined Weak Reference
    Vc2_IRQHandler                            - Undefined Weak Reference
    Wwdt_IRQHandler                           - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_hc32l186.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_hc32l186.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x000000c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x000000c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x000000c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x000000c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x000000c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x000000c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x000000d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x000000d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x000000d5   Thumb Code    26  startup_hc32l186.o(.text)
    NMI_Handler                              0x000000ef   Thumb Code     2  startup_hc32l186.o(.text)
    SVC_Handler                              0x000000f3   Thumb Code     2  startup_hc32l186.o(.text)
    PendSV_Handler                           0x000000f5   Thumb Code     2  startup_hc32l186.o(.text)
    HardFault_Handler                        0x00000111   Thumb Code    12  cmb_fault.o(.text)
    __aeabi_memset                           0x0000011d   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0000011d   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0000011d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0000012b   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0000012b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0000012b   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0000012f   Thumb Code    18  memseta.o(.text)
    __aeabi_uidiv                            0x00000141   Thumb Code     0  uidiv_div0.o(.text)
    __aeabi_uidivmod                         0x00000141   Thumb Code    62  uidiv_div0.o(.text)
    __aeabi_uldivmod                         0x0000017f   Thumb Code    96  uldiv.o(.text)
    __scatterload                            0x000001e1   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x000001e1   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x00000205   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x00000205   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x00000225   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x00000225   Thumb Code     0  llushr.o(.text)
    main                                     0x00000247   Thumb Code    14  system_hc32l186.o(.text.$Sub$$main)
    ADC_DAC_IRQHandler                       0x00000255   Thumb Code    12  interrupts_hc32l186.o(.text.ADC_DAC_IRQHandler)
    Adc_ClrIrqStatus                         0x00000261   Thumb Code    12  adc.o(.text.Adc_ClrIrqStatus)
    Adc_GetIrqStatus                         0x00000271   Thumb Code    16  adc.o(.text.Adc_GetIrqStatus)
    Adc_GetSqrResult                         0x00000285   Thumb Code     8  adc.o(.text.Adc_GetSqrResult)
    Adc_IRQHandler                           0x00000291   Thumb Code   100  adpt_adc.o(.text.Adc_IRQHandler)
    Adc_SQR_Stop                             0x000002f9   Thumb Code     8  adc.o(.text.Adc_SQR_Stop)
    Add_MSecCount                            0x00000305   Thumb Code   108  systemtimermodule.o(.text.Add_MSecCount)
    AdtTimer3Init                            0x0000041d   Thumb Code   180  adpt_pwm.o(.text.AdtTimer3Init)
    App_SystemClkInit_RC48M                  0x00000505   Thumb Code    48  adpt_clock.o(.text.App_SystemClkInit_RC48M)
    Board_InitClock                          0x00000535   Thumb Code    10  adpt_clock.o(.text.Board_InitClock)
    Board_InitPins                           0x00000541   Thumb Code   756  adpt_gpio.o(.text.Board_InitPins)
    Board_InitReset                          0x00000839   Thumb Code    16  adpt_reset.o(.text.Board_InitReset)
    Board_InitSysTick                        0x00000851   Thumb Code    32  adpt_timebase.o(.text.Board_InitSysTick)
    Board_InitTim                            0x00000875   Thumb Code    20  adpt_pwm.o(.text.Board_InitTim)
    Bt_ClearIntFlag                          0x00000889   Thumb Code    16  bt.o(.text.Bt_ClearIntFlag)
    Bt_GetIntFlag                            0x0000089d   Thumb Code    16  bt.o(.text.Bt_GetIntFlag)
    Bt_M0_ARRSet                             0x000008b1   Thumb Code    20  bt.o(.text.Bt_M0_ARRSet)
    Bt_M0_Cnt16Set                           0x000008c5   Thumb Code    20  bt.o(.text.Bt_M0_Cnt16Set)
    Bt_M0_Run                                0x000008d9   Thumb Code    16  bt.o(.text.Bt_M0_Run)
    Bt_M23_ARRSet                            0x000008e9   Thumb Code    36  bt.o(.text.Bt_M23_ARRSet)
    Bt_M23_CCR_Set                           0x0000090d   Thumb Code    48  bt.o(.text.Bt_M23_CCR_Set)
    Bt_M23_Cnt16Set                          0x00000941   Thumb Code    20  bt.o(.text.Bt_M23_Cnt16Set)
    Bt_M23_EnPWM_Output                      0x0000095d   Thumb Code    40  bt.o(.text.Bt_M23_EnPWM_Output)
    Bt_M23_PortOutput_Cfg                    0x00000989   Thumb Code   168  bt.o(.text.Bt_M23_PortOutput_Cfg)
    Bt_M23_Run                               0x00000a39   Thumb Code    16  bt.o(.text.Bt_M23_Run)
    Bt_M23_SetValidPeriod                    0x00000a49   Thumb Code    52  bt.o(.text.Bt_M23_SetValidPeriod)
    Bt_Mode0_EnableIrq                       0x00000a81   Thumb Code    20  bt.o(.text.Bt_Mode0_EnableIrq)
    Bt_Mode0_Init                            0x00000a95   Thumb Code   128  bt.o(.text.Bt_Mode0_Init)
    Bt_Mode23_Init                           0x00000b15   Thumb Code   144  bt.o(.text.Bt_Mode23_Init)
    CLKTRIM_CTRIM_IRQHandler                 0x00000ba9   Thumb Code    12  interrupts_hc32l186.o(.text.CLKTRIM_CTRIM_IRQHandler)
    DMAC_IRQHandler                          0x00000ced   Thumb Code     8  interrupts_hc32l186.o(.text.DMAC_IRQHandler)
    EnableNvic                               0x00000da9   Thumb Code    38  interrupts_hc32l186.o(.text.EnableNvic)
    Execute_SingleDamperControl              0x00000dd1   Thumb Code    20  singledampermodule.o(.text.Execute_SingleDamperControl)
    Execute_SingleDamperDriver               0x00000de5   Thumb Code    20  singledampermodule.o(.text.Execute_SingleDamperDriver)
    FLASH_RAM_IRQHandler                     0x00000df9   Thumb Code    12  interrupts_hc32l186.o(.text.FLASH_RAM_IRQHandler)
    Flash_WaitCycle                          0x00000e05   Thumb Code    40  flash.o(.text.Flash_WaitCycle)
    Get_SecondCount                          0x00000e39   Thumb Code     8  systemtimermodule.o(.text.Get_SecondCount)
    Get_SecondElapsedTime                    0x00000e41   Thumb Code    12  systemtimermodule.o(.text.Get_SecondElapsedTime)
    Gpio_ClrIO                               0x00000e95   Thumb Code    16  gpio.o(.text.Gpio_ClrIO)
    Gpio_Init                                0x00000ea9   Thumb Code   136  gpio.o(.text.Gpio_Init)
    Gpio_SetAfMode                           0x00000f39   Thumb Code    12  gpio.o(.text.Gpio_SetAfMode)
    Gpio_SetAnalogMode                       0x00000f49   Thumb Code    16  gpio.o(.text.Gpio_SetAnalogMode)
    Gpio_SetIO                               0x00000f5d   Thumb Code    16  gpio.o(.text.Gpio_SetIO)
    I2C0_IRQHandler                          0x00000f71   Thumb Code     8  interrupts_hc32l186.o(.text.I2C0_IRQHandler)
    I2C1_IRQHandler                          0x00000f79   Thumb Code     8  interrupts_hc32l186.o(.text.I2C1_IRQHandler)
    ISR_Timer_1ms                            0x00000f81   Thumb Code    16  timebase.o(.text.ISR_Timer_1ms)
    ISR_Timer_500us                          0x00000f95   Thumb Code    24  timebase.o(.text.ISR_Timer_500us)
    Init_Mcu                                 0x00000fb1   Thumb Code    24  init_mcu.o(.text.Init_Mcu)
    Init_SingleDamper                        0x00000fc9   Thumb Code    10  singledampermodule.o(.text.Init_SingleDamper)
    Init_StepperMotor                        0x00000ffd   Thumb Code    28  steppermotorlib.o(.text.Init_StepperMotor)
    LCD_IRQHandler                           0x0000101d   Thumb Code     8  interrupts_hc32l186.o(.text.LCD_IRQHandler)
    LPTIM0_1_IRQHandler                      0x00001025   Thumb Code    12  interrupts_hc32l186.o(.text.LPTIM0_1_IRQHandler)
    LPUART0_IRQHandler                       0x00001031   Thumb Code     8  interrupts_hc32l186.o(.text.LPUART0_IRQHandler)
    LPUART1_IRQHandler                       0x00001039   Thumb Code     8  interrupts_hc32l186.o(.text.LPUART1_IRQHandler)
    LVD_IRQHandler                           0x00001041   Thumb Code     8  interrupts_hc32l186.o(.text.LVD_IRQHandler)
    PCA_WWDT_IRQHandler                      0x00001049   Thumb Code    12  interrupts_hc32l186.o(.text.PCA_WWDT_IRQHandler)
    PCNT_IRQHandler                          0x00001055   Thumb Code     8  interrupts_hc32l186.o(.text.PCNT_IRQHandler)
    PORTA_IRQHandler                         0x0000105d   Thumb Code     8  interrupts_hc32l186.o(.text.PORTA_IRQHandler)
    PORTB_IRQHandler                         0x00001065   Thumb Code     8  interrupts_hc32l186.o(.text.PORTB_IRQHandler)
    PORTC_E_IRQHandler                       0x0000106d   Thumb Code    12  interrupts_hc32l186.o(.text.PORTC_E_IRQHandler)
    PORTD_F_IRQHandler                       0x00001079   Thumb Code    12  interrupts_hc32l186.o(.text.PORTD_F_IRQHandler)
    Panic                                    0x00001085   Thumb Code     2  ramlog.o(.text.Panic)
    RTC_IRQHandler                           0x0000109d   Thumb Code     8  interrupts_hc32l186.o(.text.RTC_IRQHandler)
    Reset_SingleDamper                       0x000010a5   Thumb Code    28  singledampermodule.o(.text.Reset_SingleDamper)
    SPI0_IRQHandler                          0x000010c1   Thumb Code     8  interrupts_hc32l186.o(.text.SPI0_IRQHandler)
    SPI1_IRQHandler                          0x000010c9   Thumb Code     8  interrupts_hc32l186.o(.text.SPI1_IRQHandler)
    SetBit                                   0x000010d1   Thumb Code    24  ddl.o(.text.SetBit)
    Set_SingleDamperState                    0x000010e9   Thumb Code    28  singledampermodule.o(.text.Set_SingleDamperState)
    SysTick_Handler                          0x00001161   Thumb Code     8  interrupts_hc32l186.o(.text.SysTick_Handler)
    SysTick_IRQHandler                       0x00001169   Thumb Code    12  adpt_timebase.o(.text.SysTick_IRQHandler)
    Sysctrl_ClkSourceEnable                  0x00001175   Thumb Code   348  sysctrl.o(.text.Sysctrl_ClkSourceEnable)
    Sysctrl_GetHClkFreq                      0x000012dd   Thumb Code   320  sysctrl.o(.text.Sysctrl_GetHClkFreq)
    Sysctrl_SetPeripheralGate                0x00001441   Thumb Code    36  sysctrl.o(.text.Sysctrl_SetPeripheralGate)
    Sysctrl_SetRC48MTrim                     0x00001469   Thumb Code    16  sysctrl.o(.text.Sysctrl_SetRC48MTrim)
    Sysctrl_SysClkSwitch                     0x0000147d   Thumb Code    32  sysctrl.o(.text.Sysctrl_SysClkSwitch)
    SystemCoreClockUpdate                    0x000014a1   Thumb Code    12  system_hc32l186.o(.text.SystemCoreClockUpdate)
    SystemInit                               0x000014b1   Thumb Code    64  system_hc32l186.o(.text.SystemInit)
    TIM0_IRQHandler                          0x00001515   Thumb Code     8  interrupts_hc32l186.o(.text.TIM0_IRQHandler)
    TIM1_IRQHandler                          0x0000151d   Thumb Code     8  interrupts_hc32l186.o(.text.TIM1_IRQHandler)
    TIM2_IRQHandler                          0x00001525   Thumb Code     8  interrupts_hc32l186.o(.text.TIM2_IRQHandler)
    TIM3_IRQHandler                          0x0000152d   Thumb Code     8  interrupts_hc32l186.o(.text.TIM3_IRQHandler)
    TIM4_IRQHandler                          0x00001535   Thumb Code     8  interrupts_hc32l186.o(.text.TIM4_IRQHandler)
    TIM5_IRQHandler                          0x0000153d   Thumb Code     8  interrupts_hc32l186.o(.text.TIM5_IRQHandler)
    TIM6_IRQHandler                          0x00001545   Thumb Code     8  interrupts_hc32l186.o(.text.TIM6_IRQHandler)
    Tim0_IRQHandler                          0x0000154d   Thumb Code    28  adpt_timebase.o(.text.Tim0_IRQHandler)
    Tim3_M23_ARRSet                          0x00001569   Thumb Code    32  timer3.o(.text.Tim3_M23_ARRSet)
    Tim3_M23_CCR_Set                         0x0000158d   Thumb Code     8  timer3.o(.text.Tim3_M23_CCR_Set)
    Tim3_M23_Cnt16Set                        0x00001599   Thumb Code    16  timer3.o(.text.Tim3_M23_Cnt16Set)
    Tim3_M23_EnPWM_Output                    0x000015b1   Thumb Code    40  timer3.o(.text.Tim3_M23_EnPWM_Output)
    Tim3_M23_PortOutput_Cfg                  0x000015dd   Thumb Code   512  timer3.o(.text.Tim3_M23_PortOutput_Cfg)
    Tim3_M23_Run                             0x000017e1   Thumb Code    16  timer3.o(.text.Tim3_M23_Run)
    Tim3_M23_SetValidPeriod                  0x000017f1   Thumb Code    48  timer3.o(.text.Tim3_M23_SetValidPeriod)
    Tim3_Mode23_Init                         0x00001825   Thumb Code   140  timer3.o(.text.Tim3_Mode23_Init)
    Time0Init                                0x000018b5   Thumb Code   128  adpt_timebase.o(.text.Time0Init)
    UART0_2_IRQHandler                       0x00001941   Thumb Code    12  interrupts_hc32l186.o(.text.UART0_2_IRQHandler)
    UART1_3_IRQHandler                       0x0000194d   Thumb Code    12  interrupts_hc32l186.o(.text.UART1_3_IRQHandler)
    VC0_IRQHandler                           0x00001959   Thumb Code     8  interrupts_hc32l186.o(.text.VC0_IRQHandler)
    VC1_2_IRQHandler                         0x00001961   Thumb Code    12  interrupts_hc32l186.o(.text.VC1_2_IRQHandler)
    WDT_IRQHandler                           0x0000196d   Thumb Code     8  interrupts_hc32l186.o(.text.WDT_IRQHandler)
    Wdt_GetIrqStatus                         0x00001975   Thumb Code    12  wdt.o(.text.Wdt_GetIrqStatus)
    Wdt_IRQHandler                           0x00001985   Thumb Code     8  adpt_iwdg.o(.text.Wdt_IRQHandler)
    Wdt_IrqClr                               0x0000198d   Thumb Code    12  wdt.o(.text.Wdt_IrqClr)
    cm_backtrace_call_stack                  0x00001a79   Thumb Code   272  cm_backtrace.o(.text.cm_backtrace_call_stack)
    cm_backtrace_fault                       0x00001b99   Thumb Code   304  cm_backtrace.o(.text.cm_backtrace_fault)
    cm_backtrace_firmware_info               0x00001db5   Thumb Code    24  cm_backtrace.o(.text.cm_backtrace_firmware_info)
    ddl_memclr                               0x00001e1d   Thumb Code    16  ddl.o(.text.ddl_memclr)
    delay10us                                0x00001e2d   Thumb Code    56  ddl.o(.text.delay10us)
    delay1ms                                 0x00001e69   Thumb Code    56  ddl.o(.text.delay1ms)
    fputc                                    0x00001fc9   Thumb Code    22  ramlog.o(.text.fputc)
    $Super$$main                             0x00001fe1   Thumb Code    96  main.o(.text.main)
    software_reset                           0x00002179   Thumb Code    52  adpt_iwdg.o(.text.software_reset)
    __0printf$8                              0x000021d5   Thumb Code    24  printf8.o(i.__0printf$8)
    __1printf$8                              0x000021d5   Thumb Code     0  printf8.o(i.__0printf$8)
    __2printf                                0x000021d5   Thumb Code     0  printf8.o(i.__0printf$8)
    __0sprintf$8                             0x000021f5   Thumb Code    36  printf8.o(i.__0sprintf$8)
    __1sprintf$8                             0x000021f5   Thumb Code     0  printf8.o(i.__0sprintf$8)
    __2sprintf                               0x000021f5   Thumb Code     0  printf8.o(i.__0sprintf$8)
    __scatterload_copy                       0x0000221d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0000222b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0000222d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    puts                                     0x000026bd   Thumb Code    28  puts.o(i.puts)
    Region$$Table$$Base                      0x000028c0   Number         0  anon$$obj.o(Region$$Table)
    Image$$ER_IROM1$$Limit                   0x000028e0   Number         0  anon$$obj.o ABSOLUTE
    Region$$Table$$Limit                     0x000028e0   Number         0  anon$$obj.o(Region$$Table)
    __stdout                                 0x20000000   Data           4  stdout.o(.data)
    SystemCoreClock                          0x20000004   Data           4  system_hc32l186.o(.data.SystemCoreClock)
    st_SystemTimer                           0x200001b4   Data          20  systemtimermodule.o(.bss.st_SystemTimer)
    stcGpioCfg                               0x200001cc   Data          28  adpt_gpio.o(.bss.stcGpioCfg)
    u32AdcRestult                            0x2000020c   Data          36  adpt_adc.o(.bss.u32AdcRestult)
    STACK$$Base                              0x20000238   Number         0  startup_hc32l186.o(STACK)
    STACK$$Limit                             0x20001238   Number         0  startup_hc32l186.o(STACK)
    __initial_sp                             0x20001238   Data           0  startup_hc32l186.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x000028e8, Max: 0x00015e00, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x000028e0, Max: 0x00015e00, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO         1724    RESET               startup_hc32l186.o
    0x000000c0   0x000000c0   0x00000000   Code   RO         1831  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x000000c0   0x000000c0   0x00000004   Code   RO         2117    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x000000c4   0x000000c4   0x00000004   Code   RO         2120    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO         2122    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO         2124    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x000000c8   0x000000c8   0x00000008   Code   RO         2125    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO         2127    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO         2129    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x000000d0   0x000000d0   0x00000004   Code   RO         2118    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x000000d4   0x000000d4   0x0000003c   Code   RO         1725    .text               startup_hc32l186.o
    0x00000110   0x00000110   0x0000000c   Code   RO         1794    .text               cmb_fault.o
    0x0000011c   0x0000011c   0x00000024   Code   RO         1844    .text               mc_p.l(memseta.o)
    0x00000140   0x00000140   0x0000003e   Code   RO         2136    .text               mc_p.l(uidiv_div0.o)
    0x0000017e   0x0000017e   0x00000060   Code   RO         2142    .text               mc_p.l(uldiv.o)
    0x000001de   0x000001de   0x00000002   PAD
    0x000001e0   0x000001e0   0x00000024   Code   RO         2157    .text               mc_p.l(init.o)
    0x00000204   0x00000204   0x00000020   Code   RO         2159    .text               mc_p.l(llshl.o)
    0x00000224   0x00000224   0x00000022   Code   RO         2161    .text               mc_p.l(llushr.o)
    0x00000246   0x00000246   0x0000000e   Code   RO          146    .text.$Sub$$main    system_hc32l186.o
    0x00000254   0x00000254   0x0000000c   Code   RO          216    .text.ADC_DAC_IRQHandler  interrupts_hc32l186.o
    0x00000260   0x00000260   0x00000010   Code   RO          265    .text.Adc_ClrIrqStatus  adc.o
    0x00000270   0x00000270   0x00000014   Code   RO          263    .text.Adc_GetIrqStatus  adc.o
    0x00000284   0x00000284   0x0000000c   Code   RO          311    .text.Adc_GetSqrResult  adc.o
    0x00000290   0x00000290   0x00000068   Code   RO            6    .text.Adc_IRQHandler  adpt_adc.o
    0x000002f8   0x000002f8   0x0000000c   Code   RO          289    .text.Adc_SQR_Stop  adc.o
    0x00000304   0x00000304   0x00000070   Code   RO         1701    .text.Add_MSecCount  systemtimermodule.o
    0x00000374   0x00000374   0x000000a8   Code   RO           70    .text.AdtTimer2Init  adpt_pwm.o
    0x0000041c   0x0000041c   0x000000bc   Code   RO           66    .text.AdtTimer3Init  adpt_pwm.o
    0x000004d8   0x000004d8   0x0000002c   Code   RO         1642    .text.Antifreeze_SingleDamper  singledampermodule.o
    0x00000504   0x00000504   0x00000030   Code   RO           23    .text.App_SystemClkInit_RC48M  adpt_clock.o
    0x00000534   0x00000534   0x0000000a   Code   RO           25    .text.Board_InitClock  adpt_clock.o
    0x0000053e   0x0000053e   0x00000002   PAD
    0x00000540   0x00000540   0x000002f8   Code   RO           35    .text.Board_InitPins  adpt_gpio.o
    0x00000838   0x00000838   0x00000018   Code   RO          100    .text.Board_InitReset  adpt_reset.o
    0x00000850   0x00000850   0x00000024   Code   RO           86    .text.Board_InitSysTick  adpt_timebase.o
    0x00000874   0x00000874   0x00000014   Code   RO           68    .text.Board_InitTim  adpt_pwm.o
    0x00000888   0x00000888   0x00000014   Code   RO          496    .text.Bt_ClearIntFlag  bt.o
    0x0000089c   0x0000089c   0x00000014   Code   RO          494    .text.Bt_GetIntFlag  bt.o
    0x000008b0   0x000008b0   0x00000014   Code   RO          526    .text.Bt_M0_ARRSet  bt.o
    0x000008c4   0x000008c4   0x00000014   Code   RO          522    .text.Bt_M0_Cnt16Set  bt.o
    0x000008d8   0x000008d8   0x00000010   Code   RO          514    .text.Bt_M0_Run     bt.o
    0x000008e8   0x000008e8   0x00000024   Code   RO          556    .text.Bt_M23_ARRSet  bt.o
    0x0000090c   0x0000090c   0x00000034   Code   RO          562    .text.Bt_M23_CCR_Set  bt.o
    0x00000940   0x00000940   0x0000001c   Code   RO          558    .text.Bt_M23_Cnt16Set  bt.o
    0x0000095c   0x0000095c   0x0000002c   Code   RO          550    .text.Bt_M23_EnPWM_Output  bt.o
    0x00000988   0x00000988   0x000000b0   Code   RO          570    .text.Bt_M23_PortOutput_Cfg  bt.o
    0x00000a38   0x00000a38   0x00000010   Code   RO          552    .text.Bt_M23_Run    bt.o
    0x00000a48   0x00000a48   0x00000038   Code   RO          582    .text.Bt_M23_SetValidPeriod  bt.o
    0x00000a80   0x00000a80   0x00000014   Code   RO          500    .text.Bt_Mode0_EnableIrq  bt.o
    0x00000a94   0x00000a94   0x00000080   Code   RO          512    .text.Bt_Mode0_Init  bt.o
    0x00000b14   0x00000b14   0x00000094   Code   RO          548    .text.Bt_Mode23_Init  bt.o
    0x00000ba8   0x00000ba8   0x0000000c   Code   RO          230    .text.CLKTRIM_CTRIM_IRQHandler  interrupts_hc32l186.o
    0x00000bb4   0x00000bb4   0x0000004c   Code   RO         1664    .text.Config_IO_SingleDamperID0  singledampermodule.o
    0x00000c00   0x00000c00   0x000000ec   Code   RO         1640    .text.Control_SingleDamper  singledampermodule.o
    0x00000cec   0x00000cec   0x00000008   Code   RO          176    .text.DMAC_IRQHandler  interrupts_hc32l186.o
    0x00000cf4   0x00000cf4   0x000000b4   Code   RO         1803    .text.Drive_StepperMotorISR  steppermotorlib.o
    0x00000da8   0x00000da8   0x00000026   Code   RO          156    .text.EnableNvic    interrupts_hc32l186.o
    0x00000dce   0x00000dce   0x00000002   PAD
    0x00000dd0   0x00000dd0   0x00000014   Code   RO         1638    .text.Execute_SingleDamperControl  singledampermodule.o
    0x00000de4   0x00000de4   0x00000014   Code   RO         1636    .text.Execute_SingleDamperDriver  singledampermodule.o
    0x00000df8   0x00000df8   0x0000000c   Code   RO          228    .text.FLASH_RAM_IRQHandler  interrupts_hc32l186.o
    0x00000e04   0x00000e04   0x00000034   Code   RO          852    .text.Flash_WaitCycle  flash.o
    0x00000e38   0x00000e38   0x00000008   Code   RO         1685    .text.Get_SecondCount  systemtimermodule.o
    0x00000e40   0x00000e40   0x00000010   Code   RO         1695    .text.Get_SecondElapsedTime  systemtimermodule.o
    0x00000e50   0x00000e50   0x00000010   Code   RO         1813    .text.Get_StepperMotorIsPausing  steppermotorlib.o
    0x00000e60   0x00000e60   0x00000010   Code   RO         1811    .text.Get_StepperMotorIsRunning  steppermotorlib.o
    0x00000e70   0x00000e70   0x00000018   Code   RO         1817    .text.Get_StepperMotorRemainingSteps  steppermotorlib.o
    0x00000e88   0x00000e88   0x0000000c   Code   RO         1815    .text.Get_StepperMotorRunSteps  steppermotorlib.o
    0x00000e94   0x00000e94   0x00000014   Code   RO          885    .text.Gpio_ClrIO    gpio.o
    0x00000ea8   0x00000ea8   0x00000090   Code   RO          869    .text.Gpio_Init     gpio.o
    0x00000f38   0x00000f38   0x00000010   Code   RO          891    .text.Gpio_SetAfMode  gpio.o
    0x00000f48   0x00000f48   0x00000014   Code   RO          889    .text.Gpio_SetAnalogMode  gpio.o
    0x00000f5c   0x00000f5c   0x00000014   Code   RO          881    .text.Gpio_SetIO    gpio.o
    0x00000f70   0x00000f70   0x00000008   Code   RO          192    .text.I2C0_IRQHandler  interrupts_hc32l186.o
    0x00000f78   0x00000f78   0x00000008   Code   RO          194    .text.I2C1_IRQHandler  interrupts_hc32l186.o
    0x00000f80   0x00000f80   0x00000014   Code   RO          248    .text.ISR_Timer_1ms  timebase.o
    0x00000f94   0x00000f94   0x0000001c   Code   RO          250    .text.ISR_Timer_500us  timebase.o
    0x00000fb0   0x00000fb0   0x00000018   Code   RO          240    .text.Init_Mcu      init_mcu.o
    0x00000fc8   0x00000fc8   0x0000000a   Code   RO         1632    .text.Init_SingleDamper  singledampermodule.o
    0x00000fd2   0x00000fd2   0x00000002   PAD
    0x00000fd4   0x00000fd4   0x00000028   Code   RO         1634    .text.Init_SingleDamperParm  singledampermodule.o
    0x00000ffc   0x00000ffc   0x00000020   Code   RO         1801    .text.Init_StepperMotor  steppermotorlib.o
    0x0000101c   0x0000101c   0x00000008   Code   RO          226    .text.LCD_IRQHandler  interrupts_hc32l186.o
    0x00001024   0x00001024   0x0000000c   Code   RO          202    .text.LPTIM0_1_IRQHandler  interrupts_hc32l186.o
    0x00001030   0x00001030   0x00000008   Code   RO          184    .text.LPUART0_IRQHandler  interrupts_hc32l186.o
    0x00001038   0x00001038   0x00000008   Code   RO          186    .text.LPUART1_IRQHandler  interrupts_hc32l186.o
    0x00001040   0x00001040   0x00000008   Code   RO          224    .text.LVD_IRQHandler  interrupts_hc32l186.o
    0x00001048   0x00001048   0x0000000c   Code   RO          210    .text.PCA_WWDT_IRQHandler  interrupts_hc32l186.o
    0x00001054   0x00001054   0x00000008   Code   RO          218    .text.PCNT_IRQHandler  interrupts_hc32l186.o
    0x0000105c   0x0000105c   0x00000008   Code   RO          168    .text.PORTA_IRQHandler  interrupts_hc32l186.o
    0x00001064   0x00001064   0x00000008   Code   RO          170    .text.PORTB_IRQHandler  interrupts_hc32l186.o
    0x0000106c   0x0000106c   0x0000000c   Code   RO          172    .text.PORTC_E_IRQHandler  interrupts_hc32l186.o
    0x00001078   0x00001078   0x0000000c   Code   RO          174    .text.PORTD_F_IRQHandler  interrupts_hc32l186.o
    0x00001084   0x00001084   0x00000002   Code   RO         1738    .text.Panic         ramlog.o
    0x00001086   0x00001086   0x00000016   Code   RO         1809    .text.Pause_StepperMotorAction  steppermotorlib.o
    0x0000109c   0x0000109c   0x00000008   Code   RO          214    .text.RTC_IRQHandler  interrupts_hc32l186.o
    0x000010a4   0x000010a4   0x0000001c   Code   RO         1644    .text.Reset_SingleDamper  singledampermodule.o
    0x000010c0   0x000010c0   0x00000008   Code   RO          188    .text.SPI0_IRQHandler  interrupts_hc32l186.o
    0x000010c8   0x000010c8   0x00000008   Code   RO          190    .text.SPI1_IRQHandler  interrupts_hc32l186.o
    0x000010d0   0x000010d0   0x00000018   Code   RO          746    .text.SetBit        ddl.o
    0x000010e8   0x000010e8   0x00000020   Code   RO         1650    .text.Set_SingleDamperState  singledampermodule.o
    0x00001108   0x00001108   0x00000026   Code   RO         1805    .text.Set_StepperMotorTartget  steppermotorlib.o
    0x0000112e   0x0000112e   0x00000010   Code   RO         1807    .text.Stop_StepperMotorAction  steppermotorlib.o
    0x0000113e   0x0000113e   0x00000002   PAD
    0x00001140   0x00001140   0x00000020   Code   RO           88    .text.SysTick_Config  adpt_timebase.o
    0x00001160   0x00001160   0x00000008   Code   RO          166    .text.SysTick_Handler  interrupts_hc32l186.o
    0x00001168   0x00001168   0x0000000c   Code   RO           84    .text.SysTick_IRQHandler  adpt_timebase.o
    0x00001174   0x00001174   0x00000168   Code   RO         1305    .text.Sysctrl_ClkSourceEnable  sysctrl.o
    0x000012dc   0x000012dc   0x00000164   Code   RO         1311    .text.Sysctrl_GetHClkFreq  sysctrl.o
    0x00001440   0x00001440   0x00000028   Code   RO         1345    .text.Sysctrl_SetPeripheralGate  sysctrl.o
    0x00001468   0x00001468   0x00000014   Code   RO         1339    .text.Sysctrl_SetRC48MTrim  sysctrl.o
    0x0000147c   0x0000147c   0x00000024   Code   RO         1309    .text.Sysctrl_SysClkSwitch  sysctrl.o
    0x000014a0   0x000014a0   0x00000010   Code   RO          142    .text.SystemCoreClockUpdate  system_hc32l186.o
    0x000014b0   0x000014b0   0x00000064   Code   RO          144    .text.SystemInit    system_hc32l186.o
    0x00001514   0x00001514   0x00000008   Code   RO          196    .text.TIM0_IRQHandler  interrupts_hc32l186.o
    0x0000151c   0x0000151c   0x00000008   Code   RO          198    .text.TIM1_IRQHandler  interrupts_hc32l186.o
    0x00001524   0x00001524   0x00000008   Code   RO          200    .text.TIM2_IRQHandler  interrupts_hc32l186.o
    0x0000152c   0x0000152c   0x00000008   Code   RO          178    .text.TIM3_IRQHandler  interrupts_hc32l186.o
    0x00001534   0x00001534   0x00000008   Code   RO          204    .text.TIM4_IRQHandler  interrupts_hc32l186.o
    0x0000153c   0x0000153c   0x00000008   Code   RO          206    .text.TIM5_IRQHandler  interrupts_hc32l186.o
    0x00001544   0x00001544   0x00000008   Code   RO          208    .text.TIM6_IRQHandler  interrupts_hc32l186.o
    0x0000154c   0x0000154c   0x0000001c   Code   RO           82    .text.Tim0_IRQHandler  adpt_timebase.o
    0x00001568   0x00001568   0x00000024   Code   RO         1423    .text.Tim3_M23_ARRSet  timer3.o
    0x0000158c   0x0000158c   0x0000000c   Code   RO         1429    .text.Tim3_M23_CCR_Set  timer3.o
    0x00001598   0x00001598   0x00000018   Code   RO         1425    .text.Tim3_M23_Cnt16Set  timer3.o
    0x000015b0   0x000015b0   0x0000002c   Code   RO         1417    .text.Tim3_M23_EnPWM_Output  timer3.o
    0x000015dc   0x000015dc   0x00000204   Code   RO         1437    .text.Tim3_M23_PortOutput_Cfg  timer3.o
    0x000017e0   0x000017e0   0x00000010   Code   RO         1419    .text.Tim3_M23_Run  timer3.o
    0x000017f0   0x000017f0   0x00000034   Code   RO         1451    .text.Tim3_M23_SetValidPeriod  timer3.o
    0x00001824   0x00001824   0x00000090   Code   RO         1415    .text.Tim3_Mode23_Init  timer3.o
    0x000018b4   0x000018b4   0x0000008c   Code   RO           80    .text.Time0Init     adpt_timebase.o
    0x00001940   0x00001940   0x0000000c   Code   RO          180    .text.UART0_2_IRQHandler  interrupts_hc32l186.o
    0x0000194c   0x0000194c   0x0000000c   Code   RO          182    .text.UART1_3_IRQHandler  interrupts_hc32l186.o
    0x00001958   0x00001958   0x00000008   Code   RO          220    .text.VC0_IRQHandler  interrupts_hc32l186.o
    0x00001960   0x00001960   0x0000000c   Code   RO          222    .text.VC1_2_IRQHandler  interrupts_hc32l186.o
    0x0000196c   0x0000196c   0x00000008   Code   RO          212    .text.WDT_IRQHandler  interrupts_hc32l186.o
    0x00001974   0x00001974   0x00000010   Code   RO         1598    .text.Wdt_GetIrqStatus  wdt.o
    0x00001984   0x00001984   0x00000008   Code   RO           47    .text.Wdt_IRQHandler  adpt_iwdg.o
    0x0000198c   0x0000198c   0x00000010   Code   RO         1592    .text.Wdt_IrqClr    wdt.o
    0x0000199c   0x0000199c   0x00000018   Code   RO         1307    .text._SysctrlUnlock  sysctrl.o
    0x000019b4   0x000019b4   0x00000018   Code   RO          158    .text.__NVIC_ClearPendingIRQ  interrupts_hc32l186.o
    0x000019cc   0x000019cc   0x00000020   Code   RO          164    .text.__NVIC_DisableIRQ  interrupts_hc32l186.o
    0x000019ec   0x000019ec   0x00000018   Code   RO          162    .text.__NVIC_EnableIRQ  interrupts_hc32l186.o
    0x00001a04   0x00001a04   0x00000018   Code   RO           90    .text.__NVIC_SetPriority  adpt_timebase.o
    0x00001a1c   0x00001a1c   0x00000040   Code   RO          160    .text.__NVIC_SetPriority  interrupts_hc32l186.o
    0x00001a5c   0x00001a5c   0x0000001c   Code   RO           57    .text.__NVIC_SystemReset  adpt_iwdg.o
    0x00001a78   0x00001a78   0x00000120   Code   RO         1761    .text.cm_backtrace_call_stack  cm_backtrace.o
    0x00001b98   0x00001b98   0x0000021c   Code   RO         1769    .text.cm_backtrace_fault  cm_backtrace.o
    0x00001db4   0x00001db4   0x00000068   Code   RO         1755    .text.cm_backtrace_firmware_info  cm_backtrace.o
    0x00001e1c   0x00001e1c   0x00000010   Code   RO          738    .text.ddl_memclr    ddl.o
    0x00001e2c   0x00001e2c   0x0000003c   Code   RO          744    .text.delay10us     ddl.o
    0x00001e68   0x00001e68   0x00000040   Code   RO          740    .text.delay1ms      ddl.o
    0x00001ea8   0x00001ea8   0x00000030   Code   RO         1759    .text.disassembly_ins_is_bl_blx  cm_backtrace.o
    0x00001ed8   0x00001ed8   0x000000f0   Code   RO         1765    .text.dump_stack    cm_backtrace.o
    0x00001fc8   0x00001fc8   0x00000016   Code   RO         1732    .text.fputc         ramlog.o
    0x00001fde   0x00001fde   0x00000002   PAD
    0x00001fe0   0x00001fe0   0x0000006c   Code   RO         1713    .text.main          main.o
    0x0000204c   0x0000204c   0x000000e4   Code   RO         1767    .text.print_call_stack  cm_backtrace.o
    0x00002130   0x00002130   0x0000002c   Code   RO         1734    .text.ramlog_putc   ramlog.o
    0x0000215c   0x0000215c   0x0000001c   Code   RO         1736    .text.ramlog_puts   ramlog.o
    0x00002178   0x00002178   0x0000005c   Code   RO           55    .text.software_reset  adpt_iwdg.o
    0x000021d4   0x000021d4   0x00000020   Code   RO         2055    i.__0printf$8       mc_p.l(printf8.o)
    0x000021f4   0x000021f4   0x00000028   Code   RO         2057    i.__0sprintf$8      mc_p.l(printf8.o)
    0x0000221c   0x0000221c   0x0000000e   Code   RO         2171    i.__scatterload_copy  mc_p.l(handlers.o)
    0x0000222a   0x0000222a   0x00000002   Code   RO         2172    i.__scatterload_null  mc_p.l(handlers.o)
    0x0000222c   0x0000222c   0x0000000e   Code   RO         2173    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x0000223a   0x0000223a   0x00000002   PAD
    0x0000223c   0x0000223c   0x00000428   Code   RO         2062    i._printf_core      mc_p.l(printf8.o)
    0x00002664   0x00002664   0x00000020   Code   RO         2063    i._printf_post_padding  mc_p.l(printf8.o)
    0x00002684   0x00002684   0x0000002c   Code   RO         2064    i._printf_pre_padding  mc_p.l(printf8.o)
    0x000026b0   0x000026b0   0x0000000a   Code   RO         2066    i._sputc            mc_p.l(printf8.o)
    0x000026ba   0x000026ba   0x00000002   PAD
    0x000026bc   0x000026bc   0x00000020   Code   RO         1834    i.puts              mc_p.l(puts.o)
    0x000026dc   0x000026dc   0x00000020   Data   RO         1819    .rodata.Api         steppermotorlib.o
    0x000026fc   0x000026fc   0x00000004   Data   RO         1822    .rodata.ary_IO_State_Lb1205m4Beats  steppermotorlib.o
    0x00002700   0x00002700   0x00000008   Data   RO         1823    .rodata.ary_IO_State_Lb1205m8Beats  steppermotorlib.o
    0x00002708   0x00002708   0x00000004   Data   RO         1821    .rodata.ary_IO_State_Lb1909m4Beats  steppermotorlib.o
    0x0000270c   0x0000270c   0x00000062   Data   RO         1668    .rodata.ary_SingleDamper_RunSteps  singledampermodule.o
    0x0000276e   0x0000276e   0x00000002   PAD
    0x00002770   0x00002770   0x00000018   Data   RO         1820    .rodata.ary_StepperMotorStepState  steppermotorlib.o
    0x00002788   0x00002788   0x00000006   Data   RO         1667    .rodata.st_SingleDamperConPara  singledampermodule.o
    0x0000278e   0x0000278e   0x00000131   Data   RO         1778    .rodata.str1.1      cm_backtrace.o
    0x000028bf   0x000028bf   0x00000001   PAD
    0x000028c0   0x000028c0   0x00000020   Data   RO         2170    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x000028e0, Size: 0x00001238, Max: 0x00006000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x000028e0   0x00000004   Data   RW         2131    .data               mc_p.l(stdout.o)
    0x20000004   0x000028e4   0x00000004   Data   RW          148    .data.SystemCoreClock  system_hc32l186.o
    0x20000008        -       0x0000002c   Zero   RW         1666    .bss.ary_SingleDamper  singledampermodule.o
    0x20000034        -       0x00000120   Zero   RW         1782    .bss.call_stack_info  cm_backtrace.o
    0x20000154        -       0x00000001   Zero   RW         1777    .bss.code_size      cm_backtrace.o
    0x20000155   0x000028e8   0x00000003   PAD
    0x20000158        -       0x00000001   Zero   RW         1776    .bss.code_start_addr  cm_backtrace.o
    0x20000159        -       0x00000021   Zero   RW         1771    .bss.fw_name        cm_backtrace.o
    0x2000017a        -       0x00000021   Zero   RW         1772    .bss.hw_ver         cm_backtrace.o
    0x2000019b        -       0x00000001   Zero   RW         1779    .bss.init_ok        cm_backtrace.o
    0x2000019c        -       0x00000001   Zero   RW         1775    .bss.main_stack_size  cm_backtrace.o
    0x2000019d   0x000028e8   0x00000003   PAD
    0x200001a0        -       0x00000001   Zero   RW         1774    .bss.main_stack_start_addr  cm_backtrace.o
    0x200001a1        -       0x00000001   Zero   RW         1780    .bss.on_fault       cm_backtrace.o
    0x200001a2   0x000028e8   0x00000002   PAD
    0x200001a4        -       0x00000004   Zero   RW         1783    .bss.regs.0.4       cm_backtrace.o
    0x200001a8        -       0x00000004   Zero   RW         1784    .bss.regs.0.5       cm_backtrace.o
    0x200001ac        -       0x00000004   Zero   RW         1785    .bss.regs.0.6       cm_backtrace.o
    0x200001b0        -       0x00000004   Zero   RW         1786    .bss.regs.0.7.0     cm_backtrace.o
    0x200001b4        -       0x00000014   Zero   RW         1704    .bss.st_SystemTimer  systemtimermodule.o
    0x200001c8        -       0x00000001   Zero   RW         1781    .bss.stack_is_overflow  cm_backtrace.o
    0x200001c9   0x000028e8   0x00000003   PAD
    0x200001cc        -       0x0000001c   Zero   RW           39    .bss.stcGpioCfg     adpt_gpio.o
    0x200001e8        -       0x00000021   Zero   RW         1773    .bss.sw_ver         cm_backtrace.o
    0x20000209   0x000028e8   0x00000003   PAD
    0x2000020c        -       0x00000024   Zero   RW           14    .bss.u32AdcRestult  adpt_adc.o
    0x20000230        -       0x00000004   Zero   RW          104    .bss.u32_ResetFlag  adpt_reset.o
    0x20000234        -       0x00000001   Zero   RW          254    .bss.u8_Millisec    timebase.o
    0x20000235        -       0x00000001   Zero   RW          255    .bss.u8_OneMsecCount  timebase.o
    0x20000236   0x000028e8   0x00000002   PAD
    0x20000238        -       0x00001000   Zero   RW         1722    STACK               startup_hc32l186.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        60         16          0          0          0      17684   adc.o
       104          4          0          0         36       6386   adpt_adc.o
        58          0          0          0          0       1522   adpt_clock.o
       760          4          0          0         28       3673   adpt_gpio.o
       128         48          0          0          0       4232   adpt_iwdg.o
       376         12          0          0          0       6176   adpt_pwm.o
        24          8          0          0          4       2663   adpt_reset.o
       272         20          0          0          0       5722   adpt_timebase.o
       800         40          0          0          0      28214   bt.o
      1448        588        305          0        410       8455   cm_backtrace.o
        12          0          0          0          0        376   cmb_fault.o
       164         12          0          0          0       3143   ddl.o
        52         12          0          0          0      12519   flash.o
       220         24          0          0          0      56738   gpio.o
        24          0          0          0          0        356   init_mcu.o
       486         20          0          0          0       6345   interrupts_hc32l186.o
       108         12          0          0          0       1561   main.o
        96         16          0          0          0       1923   ramlog.o
       506         16        104          0         44       8436   singledampermodule.o
        60         22        192          0       4096        620   startup_hc32l186.o
       356          8         72          0          0       5615   steppermotorlib.o
       836         84          0          0          0      62675   sysctrl.o
       130         40          0          4          0       4931   system_hc32l186.o
       136          8          0          0         20       4087   systemtimermodule.o
        48          8          0          0          2        791   timebase.o
       844         32          0          0          0      29143   timer3.o
        32          8          0          0          0       2898   wdt.o

    ----------------------------------------------------------------------
      8150       <USER>        <GROUP>          4       4656     286884   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        10          0          3          0         16          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        32          0          0          0          0         68   llshl.o
        34          0          0          0          0         68   llushr.o
        36          0          0          0          0        100   memseta.o
      1222         56          0          0          0        468   printf8.o
        32          4          0          0          0         68   puts.o
         0          0          0          4          0          0   stdout.o
        62          0          0          0          0         72   uidiv_div0.o
        96          0          0          0          0         84   uldiv.o

    ----------------------------------------------------------------------
      1606         <USER>          <GROUP>          4          0        996   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1600         76          0          4          0        996   mc_p.l

    ----------------------------------------------------------------------
      1606         <USER>          <GROUP>          4          0        996   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      9756       1138        708          8       4656     287060   Grand Totals
      9756       1138        708          8       4656     287060   ELF Image Totals
      9756       1138        708          8          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                10464 (  10.22kB)
    Total RW  Size (RW Data + ZI Data)              4664 (   4.55kB)
    Total ROM Size (Code + RO Data + RW Data)      10472 (  10.23kB)

==============================================================================

